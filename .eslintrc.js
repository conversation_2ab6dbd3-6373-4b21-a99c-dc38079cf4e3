module.exports = {
  root: true,
  env: {
    node: true
  },
  extends: [
    'plugin:vue/recommended',
    'eslint:recommended'
  ],
  parserOptions: {
    parser: '@babel/eslint-parser'
  },
  globals: { '_': true },
  plugins: ['vue'],

  // add your custom rules here
  // it is base on https://github.com/vuejs/eslint-config-vue
  rules: {
    '@typescript-eslint/no-unused-vars': 'off',
    "vue/no-parsing-error": [2, { "x-invalid-end-tag": false }],
    'vue/no-template-shadow': 'off',

    "vue/singleline-html-element-content-newline": "off",
    "vue/multiline-html-element-content-newline": "off",
    // "vue/name-property-casing": ["error", "PascalCase"],
    "vue/no-v-html": "off",
    'accessor-pairs': 2, // 强制 getter 和 setter 在对象中成对出现
    'arrow-spacing': [2, { // 强制箭头函数的箭头前后使用一致的空格
      'before': true, // 箭头函数的箭头前使用空格
      'after': true // 箭头函数的箭头后使用空格
    }],
    'block-spacing': [2, 'always'], // 强制在代码块中开括号前和闭括号后有空格
    'brace-style': [2, '1tbs', { // 强制在代码块中使用一致的大括号风格
      'allowSingleLine': true // 允许块的开括号和闭括号在 同一行
    }],
    'camelcase': [2, { // 强制使用骆驼拼写法命名约定
      'properties': 'always'
    }],
    'comma-dangle': [2, 'never'], // 禁止末尾逗号
    'comma-spacing': [2, { // 强制在逗号前后使用一致的空格
      'before': false, // 禁止在逗号前使用空格
      'after': true // 要求在逗号后使用一个或多个空格
    }],
    'comma-style': [2, 'last'], // 要求逗号放在数组元素、对象属性或变量声明之后，且在同一行
    'constructor-super': 2, // 要求在构造函数中有 super() 的调用
    'curly': [2, 'multi-line'], // 强制所有控制语句使用一致的括号风格, multi-line:放宽规则，允许在单行中省略大括号
    'dot-location': [2, 'property'], // 强制在点号之前或之后换行，要求点操作符和属性放在同一行
    'eol-last': 2, // 在非空文件末尾至少存在一行空行
    'eqeqeq': 'off', // 使用类型安全的 === 和 !== 操作符代替 == 和 != 操作符
    'generator-star-spacing': [2, { // 强制 generator 函数中 * 号周围有空格
      'before': true, // 强制在 * 和 function 关键字之间有空格
      'after': true // 强制在 * 和函数名之间有空格
    }],
    'handle-callback-err': [2, '^(err|error)$'], // 强制回调错误处理
    'indent': [2, 2, { // 强制使用一致的缩进, 2个空格
      'SwitchCase': 1 // 强制 switch 语句中的 case 子句的缩进级别
    }],
    'jsx-quotes': [2, 'prefer-single'], // 强制所有不包含单引号的 JSX 属性值使用单引号
    'key-spacing': [2, { // 强制在对象字面量的属性中键和值之间使用一致的间距
      'beforeColon': false, // 禁止在对象字面量的键和冒号之间存在空格
      'afterColon': true // 要求在对象字面量的冒号和值之间存在至少有一个空格
    }],
    'keyword-spacing': [2, { // 强制在关键字前后使用一致的空格
      'before': true, // 要求在关键字之前至少有一个空格
      'after': true // 要求在关键字之后至少有一个空格
    }],
    'new-cap': [2, { // 要求构造函数首字母大写
      'newIsCap': true, // 要求调用 new 操作符时有首字母大小的函数
      'capIsNew': false // 允许调用首字母大写的函数时没有 new 操作符
    }],
    'new-parens': 2, // 强制或禁止调用无参构造函数时有圆括号
    'no-array-constructor': 2, // 禁止使用 Array 构造函数
    'no-caller': 2, // 禁止使用 arguments.caller 和 arguments.callee
    'no-console': 'off', // 允许/禁止调用 console 对象的方法
    'no-class-assign': 2, // 不允许修改类声明的变量
    'no-cond-assign': 2, // 禁止在条件语句中出现赋值操作符
    'no-const-assign': 2, // 禁止修改 const 声明的变量
    'no-control-regex': 2, // 禁止在正则表达式中使用控制字符
    'no-delete-var': 2, // 禁止对变量使用 delete 操作符
    'no-dupe-args': 2, // 禁止在函数定义或表达中出现重名参数
    'no-dupe-class-members': 2, // 不允许类成员中有重复的名称
    'no-dupe-keys': 2, // 禁止对象字面量中出现重复的 key
    'no-duplicate-case': 2, // 禁止出现重复的 case 标签
    'no-empty-character-class': 2, // 禁止在正则表达式中出现空字符集
    'no-empty-pattern': 2, // 禁止使用空解构模式
    'no-eval': 2, // 禁用 eval()
    'no-ex-assign': 2, // 禁止对 catch 子句中的异常重新赋值
    'no-extend-native': 2, // 禁止扩展原生类型
    'no-extra-bind': 2, // 禁止不必要的函数绑定 bind
    'no-extra-boolean-cast': 2, // 禁止不必要的布尔转换
    'no-extra-parens': [2, 'functions'], // 只在 函数表达式周围禁止不必要的圆括号
    'no-fallthrough': 2, // 禁止 case 语句落空
    'no-floating-decimal': 2, // 禁止浮点小数，要求小数点之前或之后必须有一个数
    'no-func-assign': 2, // 禁止对 function 声明重新赋值
    'no-implied-eval': 2, // 禁止使用类似 eval() 的方法
    'no-inner-declarations': [2, 'functions'], // 禁止在嵌套的语句块中出现变量或 function 声明
    'no-invalid-regexp': 2, // 禁止 RegExp 构造函数中存在无效的正则表达式字符串
    'no-irregular-whitespace': 2, // 禁止不规则的空白
    'no-iterator': 2, // 禁用 __iterator__ 属性
    'no-label-var': 2, // 不允许标签与变量同名
    'no-labels': [2, { // 禁用标签语句
      'allowLoop': false,
      'allowSwitch': false
    }],
    'no-lone-blocks': 2, // 禁用不必要的嵌套块
    'no-mixed-spaces-and-tabs': 2, // 禁止空格和 tab 的混合缩进
    'no-multi-spaces': 2, // 禁止使用多个空格
    'no-multi-str': 2, // 禁止使用多行字符串(使用\形式创建的)
    'no-multiple-empty-lines': [2, { // 禁止出现多行空行
      'max': 2 // 最多两行
    }],
    'no-native-reassign': 2,
    'no-negated-in-lhs': 2,
    'no-new-object': 2, // 禁用 Object 的构造函数
    'no-new-require': 2, // 禁用 new require 表达式
    'no-new-symbol': 2, // 禁止 Symbol 不和 new 操作符一起使用
    'no-new-wrappers': 2, // 禁止对 String，Number 和 Boolean 使用 new 操作符
    'no-obj-calls': 2, // 禁止把全局对象作为函数调用
    'no-octal': 2, // 禁用八进制字面量(ES5 已经弃用了八进制字面量)
    'no-octal-escape': 2, // 禁止在字符串中使用八进制转义序列
    'no-path-concat': 2, // 阻止在 Node.js 中使用字符串拼接路径
    'no-proto': 2, // 禁用 __proto__ 属性
    'no-redeclare': 2, // 禁用__proto__ (ECMAScript 3.1 中已经被弃用)
    'no-regex-spaces': 2, // 禁止在正则表达式字面量中出现多个空格
    'no-return-assign': [2, 'except-parens'], // 禁止在 return 语句中使用赋值语句,除非使用括号把它们括起来
    'no-self-assign': 2, // 禁止自身赋值
    'no-self-compare': 2, // 禁止自身比较
    'no-sequences': 2, // 禁用逗号操作符
    'no-shadow-restricted-names': 2, // 禁止将标识符定义为受限的名字(保留字)
    'no-spaced-func': 2,
    'no-sparse-arrays': 2, // 禁用稀疏数组
    'no-this-before-super': 2, // 禁止在构造函数中，在调用 super() 之前使用 this 或 super
    'no-throw-literal': 2, // 禁止抛出异常字面量
    'no-trailing-spaces': [2, { // 禁止使用行尾空白（空格、tab 和其它 Unicode 空白字符）。
      "skipBlankLines": true // 允许在空行使用空白符
    }], // 禁止将变量初始化为 undefined
    'no-unexpected-multiline': 2, // 禁止使用令人困惑的多行表达式
    'no-unmodified-loop-condition': 2,
    'no-unneeded-ternary': [2, { // 禁用一成不变的循环条件
      'defaultAssignment': false
    }],
    'no-unreachable': 2, // 禁止在 return、throw、continue 和 break 语句后出现不可达代码
    'no-unsafe-finally': 2, // 禁止在 finally 语句块中出现控制流语句
    'no-unused-vars': [2, { // 禁止出现未使用过的变量
      'vars': 'all',
      'args': 'none'
    }],
    'no-useless-call': 2, // 禁止不必要的 .call() 和 .apply()
    'no-useless-computed-key': 2, // 禁止在对象中使用不必要的计算属性 动态键名 []
    'no-useless-constructor': 2, // 禁用不必要的构造函数
    'no-useless-escape': 0, // 禁用不必要的转义字符
    'no-whitespace-before-property': 2, // 禁止点属性前有空白
    'no-with': 2, // 禁用 with 语句
    'one-var': [2, { // 强制函数中的变量要么一起声明要么分开声明
      'initialized': 'never' // 要求每个作用域的初始化的变量有一个变量声明
    }],
    'operator-linebreak': [2, 'after', { // 强制操作符使用一致的换行符，要求把换行符放在操作符后面
      'overrides': { // 覆盖对指定的操作的全局设置
        '?': 'before', // 要求把换行符放在操作符前面
        ':': 'before' // 要求把换行符放在操作符前面
      }
    }],
    'padded-blocks': [2, 'never'], // 要求或禁止块内填充, 禁止块语句和类的开始或末尾有空行
    'semi': [2, 'always'], // 要求在语句末尾使用分号
    'semi-spacing': [2, { // 强制分号之前和之后使用一致的空格
      'before': false, // 分号之前禁止有空格
      'after': true // 分号之后强制有空格（只在分号不是行尾时起作用）
    }],
    'space-before-blocks': [2, 'always'], // 强制在块之前（{）使用一致的空格
    'space-before-function-paren': [2, 'never'], // 强制在 function的左括号之前使用一致的空格（never：禁止在参数的 ( 前面有空格）
    'space-in-parens': [2, 'never'], // 强制在圆括号内使用一致的空格 (never: 强制圆括号内没有空格)
    'space-infix-ops': 2, // 要求中缀操作符周围有空格
    'space-unary-ops': [2, { // 强制在一元操作符前后使用一致的空格
      'words': true, // 适用于单词类一元操作符，例如：new、delete、typeof、void、yield
      'nonwords': false // 适用于这些一元操作符: -、+、--、++、!、!!
    }],
    'spaced-comment': [2, 'always', { // 强制在注释中 // 或 /* 使用一致的空格 (always: // 或 /* 必须跟随至少一个空白。)
      'markers': ['global', 'globals', 'eslint', 'eslint-disable', '*package', '!', ',']
    }],
    'template-curly-spacing': [2, 'never'], // 要禁止模板字符串中的嵌入表达式周围空格的使用
    'use-isnan': 2, // 要求使用 isNaN() 检查 NaN
    'valid-typeof': 2, // 强制 typeof 表达式与有效的字符串进行比较
    'wrap-iife': [2, 'any'], // 需要把立即执行的函数包裹起来
    'yield-star-spacing': [2, 'both'], // 强制在 yield* 表达式中 * 周围使用空格
    'yoda': [2, 'never'], // 要求或者禁止Yoda条件
    'prefer-const': 2, // 要求使用 const 声明那些声明后不再被修改的变量
    'no-debugger': process.env.NODE_ENV === 'production' ? 2 : 0, // 禁用 debugger
    'object-curly-spacing': [2, 'always', { // 强制在大括号中使用一致的空格
      objectsInObjects: false // 禁止以对象元素开始或结尾的对象的花括号中有空格
    }],
    'array-bracket-spacing': [2, 'never'], // 强制数组方括号中使用一致的空格（never：禁止在数组括号内出现空格）
    'no-undef': "off",
    "vue/require-default-prop": "off",
    "vue/multi-word-component-names": "off",
    "vue/no-lone-template": "off"
  }
};
