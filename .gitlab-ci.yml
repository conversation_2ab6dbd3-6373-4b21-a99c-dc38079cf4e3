before_script:
  - BUILD_DATE=$(date +'%y%m%d')
  - export
  - |
    echo "-----BEGIN CERTIFICATE-----
    MIIDIzCCAgugAwIBAgIUROW+/XVeJ0MRMiRFPebean57XjQwDQYJKoZIhvcNAQEL
    BQAwITELMAkGA1UEBhMCQ04xEjAQBgNVBAMMCWNhdHRsZS1jYTAeFw0yNDEyMDkx
    MTEwMzZaFw0zNDEyMDcxMTEwMzZaMCExCzAJBgNVBAYTAkNOMRIwEAYDVQQDDAlj
    YXR0bGUtY2EwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQDE1v94RSAp
    ZOTQEJHBlZ0W2pfN4hngoWMTuy35VYKYEY+vaKHewWCjShz4ygTW6lvhHpwoKmV3
    cHBZdIvHnDWcYGAiDxwfSsn55q4BxW+1BP3HFL6X5/Z3NusZP5uIJcYRp7WroPbQ
    lK0P+Xje5xEYo6TStbiVgCqQptecIZG3Iq/fF6F/LyQh1C7Me4qfsvzR3HDunMs3
    ubxy/xkCjznXjcS6g2U/GX+4c/vY/CDoUK1Z2YYAIT7l8Ekx5LPfFb1oY5iO+CC4
    SJNjzTrsRA9mky4I18LMIZ9uGVUN2ywT0pup1cTXn8v2kEY4yO0ybLgOwklitWEK
    0elIz502LZXXAgMBAAGjUzBRMB0GA1UdDgQWBBSaNOjs66juYIltoJprCDibljQj
    +zAfBgNVHSMEGDAWgBSaNOjs66juYIltoJprCDibljQj+zAPBgNVHRMBAf8EBTAD
    AQH/MA0GCSqGSIb3DQEBCwUAA4IBAQC4W2M8mf/M6XNrM9dHz2DH1cmcBH3qG4sc
    CO90W2pz3JyugVvPn4J1wn165UDU7G9dTaPNFwZkv8M+0h8noINp+ZQCtGRRBa7L
    4NYKq3Y6HZ49vFMVvz0JJx0BHdkvsvxH6rvFhQfGQtTlTDa8+yx/1NOpVAinD0ap
    ah+BgOahqWxdMozeUikdmOR59fUpP5Db2la0Tg937u1mnOiJNoE1P+fqCUIkJXN8
    /wFcWfFjLL2s+Wunt+bf9F0Do3EEnmYHhfmLt1oAKx/HfKf/dCoR/lPpX1RSU25m
    dAh64f6iWHTuf7D/3zdcVbE3Nhymi9TuzFskODSPPzgBsoy0WKm5
    -----END CERTIFICATE-----" >> /kaniko/ssl/certs/additional-ca-cert-bundle.crt

stages:
  - build
  - deploy-dev
  - build-arm
  - deploy-test
  - deploy-6c
  - sonarqube-check

build:
  stage: build
  only:
    - 6c-init
    - dev
    - 6c
    - test
  tags:
    - master01
  image:
    name: registry.eos-harbor.com/build/kaniko:debug
    entrypoint: [""]
  script:
    - mkdir -p /kaniko/.docker
    - echo "{\"auths\":{\"$CI_REGISTRY\":{\"username\":\"$CI_REGISTRY_USER\",\"password\":\"$CI_REGISTRY_PASSWORD\"}}}" > /kaniko/.docker/config.json
    - >-
      /kaniko/executor
      --context "${CI_PROJECT_DIR}"
      --dockerfile "${CI_PROJECT_DIR}/Dockerfile"
      --destination "$CI_REGISTRY_IMAGE:${CI_COMMIT_SHORT_SHA}-6c"
      --destination "$CI_REGISTRY_IMAGE:6c-latest"
      --destination "$CI_REGISTRY_IMAGE:latest"

build-arm:
  stage: build-arm
  only:
    - 6c
    - dev
    - test
  tags:
    - master01
  image:
    name: registry.cn-beijing.aliyuncs.com/zorel/kaniko:debug
    entrypoint: [""]
  script:
    - mkdir -p /kaniko/.docker
    - echo "{\"auths\":{\"$CI_REGISTRY\":{\"username\":\"$CI_REGISTRY_USER\",\"password\":\"$CI_REGISTRY_PASSWORD\"}}}" > /kaniko/.docker/config.json
    - >-
      /kaniko/executor
      --context "${CI_PROJECT_DIR}"
      --dockerfile "${CI_PROJECT_DIR}/Dockerfile-arm"
      --destination "$CI_REGISTRY_IMAGE:${CI_COMMIT_SHORT_SHA}-arm-6c"
      --destination "$CI_REGISTRY_IMAGE:6c-latest-arm-6c"
      --destination "$CI_REGISTRY_IMAGE:latest-arm-6c"
      --custom-platform=linux/arm64

deploy-dev:
  stage: deploy-dev
  only:
    - dev
  tags:
    - eos-dev
  image: bitnami/kubectl:1.25.0
  dependencies:
    - build
  script:
    - kubectl set image deployment mfp-console-v1 -nmfp mfp-console=$CI_REGISTRY_IMAGE:${CI_COMMIT_SHORT_SHA}

deploy-test:
  stage: deploy-test
  only:
    - test
  tags:
    - eos-test
  image: bitnami/kubectl:1.25.0
  dependencies:
    - build
  script:
    - kubectl set image deployment mfp-console-v1 -nmfp mfp-console=$CI_REGISTRY_IMAGE:${CI_COMMIT_SHORT_SHA}

deploy-6c:
  stage: deploy-6c
  only:
    - 6c
  tags:
    - master01
  image: bitnami/kubectl:1.25.0
  dependencies:
    - build
  script:
    - kubectl set image deployment mfp-console-v1 -nmfp mfp-console=$CI_REGISTRY_IMAGE:${CI_COMMIT_SHORT_SHA}

  
sonarqube-check:
  stage: sonarqube-check
  image:
    name: sonarsource/sonar-scanner-cli:latest
    entrypoint: [""]
  variables:
    SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar"  # Defines the location of the analysis task cache
    GIT_DEPTH: "0"  # Tells git to fetch all the branches of the project, required by the analysis task
  cache:
    key: "${CI_JOB_NAME}"
    paths:
      - .sonar/cache
  script:
    - sonar-scanner
  allow_failure: true
  only:
    - dev
  tags:
    - eos-dev
  dependencies:
    - deploy-dev

