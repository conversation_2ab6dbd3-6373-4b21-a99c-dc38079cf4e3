<!DOCTYPE html>
<html lang="cn">

<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width,initial-scale=1.0">
  <!-- <link rel="icon" href="<%= BASE_URL %>favicon.ico"> -->
  <title id="sys-title"></title>
  <!-- <title> <%= htmlWebpackPlugin.options.title %> </title> -->
  <script src="<%= BASE_URL %>server.js"></script>

</head>

<body>
  <noscript>
    <strong>We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work properly without JavaScript enabled.
        Please enable it to continue.</strong>
  </noscript>
  <div id="app" role="config_app"></div>
  <!-- built files will be auto injected -->
</body>

</html>
<script>
  setTimeout(()=>{
    var sysLocal = JSON.parse(localStorage.getItem('sysLocal'))
    document.getElementById('sys-title').innerHTML = sysLocal.sys_name;
  },1000)
</script>