{"name": "wxqb", "version": "1.0.0", "private": true, "scripts": {"dev": "vue-cli-service serve --port 8045 --mode development", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@element-plus/icons-vue": "^2.0.10", "@kangc/v-md-editor": "^2.3.18", "@vueuse/core": "^9.3.0", "axios": "^0.27.2", "browser-md5-file": "^1.1.1", "core-js": "^3.8.3", "crypto-js": "^4.1.1", "dayjs": "^1.11.3", "echarts": "^5.4.0", "element-plus": "^2.2.29", "html2canvas": "^1.4.1", "js-base64": "^3.7.5", "json-bigint": "^1.0.0", "jspdf": "^2.5.1", "klona": "^2.0.5", "lodash": "^4.17.21", "monaco-editor": "^0.45.0", "pinia": "^2.0.21", "pinia-plugin-persist": "^1.0.0", "prismjs": "^1.29.0", "qianji-ui": "^1.0.22", "qs": "^6.10.3", "regenerator-runtime": "^0.13.9", "spark-md5": "^3.0.2", "tiny-emitter": "^2.1.0", "validator": "^13.7.0", "vue": "^3", "vue-clipboard3": "^2.0.0", "vue-router": "^4.1.2", "vue3-cookies": "^1.0.6", "vue3-count-to": "^1.1.2", "xss": "^1.0.14"}, "config": {"commitizen": {"path": "node_modules/cz-customizable"}}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "^5", "@vue/cli-plugin-eslint": "^5", "@vue/cli-plugin-router": "^5", "@vue/cli-service": "^5", "cz-customizable": "^7.0.0", "eslint": "^8.23.1", "eslint-plugin-vue": "^9.5.1", "sass": "^1.51.0", "sass-loader": "^10.2.0", "unplugin-auto-import": "^0.11.2", "url-loader": "^4.1.1"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {"no-unused-vars": "off", "no-console": "off", "no-debugger": "off", "no-empty": "off", "no-useless-escape": "off", "vue/no-unused-components": "off", "vue/no-unused-vars": "off", "no-undef": "off", "no-unreachable": "off", "no-irregular-whitespace": "off", "vue/multi-word-component-names": "off", "vue/no-v-model-argument": "off", "vue/name-property-casing": "off"}}}