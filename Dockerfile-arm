FROM registry.cn-beijing.aliyuncs.com/zorel-base/node:14.21.3 AS builder
WORKDIR /home/<USER>/app
RUN node -v
RUN npm config set registry https://registry.npm.taobao.org
ADD package.json /home/<USER>/app/
RUN npm set registry http://172.16.110.141:4873/
RUN npm get registry
RUN npm install qianji-ui --save
RUN npm install
ADD . /home/<USER>/app
RUN npm run build

FROM registry.eos-harbor.com/build/nginx-arm:1.27.2
WORKDIR /usr/share/nginx/html/dist
COPY --from=builder /home/<USER>/app/dist /usr/share/nginx/html/dist
COPY ./nginx.conf /etc/nginx/
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
