const { name } = require('./package');
const { resolve } = require('path');
const defaultSettings = require('./src/settings.js');
module.exports = {
  publicPath: '/mfp',
  productionSourceMap: false,
  outputDir: 'dist',
  css: {
    extract: false,
    sourceMap: true
    // loaderOptions: {
    // 默认情况下 `sass` 选项会同时对 `sass` 和 `scss` 语法同时生效
    // 因为 `scss` 语法在内部也是由 sass-loader 处理的
    // 但是在配置 `data` 选项的时候
    // `scss` 语法会要求语句结尾必须有分号，`sass` 则要求必须没有分号
    // 在这种情况下，我们可以使用 `scss` 选项，对 `scss` 语法进行单独配置
    //     scss: {
    //         additionalData: `@import "~@/css/common.scss";`,
    //     },
    // },
  },
  configureWebpack: {
    resolve: {
      alias: {
        '@': resolve('src')
      }
    },
    externals: {},
    plugins: [
      require('unplugin-auto-import/webpack')({
        // targets to transform
        include: [
          /\.[tj]sx?$/, // .ts, .tsx, .js, .jsx
          /\.vue$/,
          /\.vue\?vue/, // .vue
          /\.md$/ // .md
        ],

        // global imports to register
        imports: [
          // presets
          'vue',
          'vue-router',
          // custom
          {
            'element-plus': ['ElMessage', 'ElMessageBox'],
            '@/store/store': ['useStore'],
            axios: [
              // default imports
              ['default', 'axios'] // import { default as axios } from 'axios',
            ],
            '[package-name]': [
              '[import-names]',
              // alias
              ['[from]', '[alias]']
            ]
          }
        ],

        // Auto import for module exports under directories
        // by default it only scan one level of modules under the directory
        dirs: [
          // './hooks',
          // './composables' // only root modules
          // './composables/**', // all nested modules
          // ...
        ],

        // Filepath to generate corresponding .d.ts file.
        // Defaults to './auto-imports.d.ts' when `typescript` is installed locally.
        // Set `false` to disable.
        dts: './auto-imports.d.ts',

        // Auto import inside Vue template
        // see https://github.com/unjs/unimport/pull/15 and https://github.com/unjs/unimport/pull/72
        vueTemplate: false,

        // Custom resolvers, compatible with `unplugin-vue-components`
        // see https://github.com/antfu/unplugin-auto-import/pull/23/
        resolvers: [
          /* ... */
        ]

        // Generate corresponding .eslintrc-auto-import.json file.
        // eslint globals Docs - https://eslint.org/docs/user-guide/configuring/language-options#specifying-globals
        // eslintrc: {
        //   enabled: false, // Default `false`
        //   filepath: './.eslintrc-auto-import.json', // Default `./.eslintrc-auto-import.json`
        //   globalsPropValue: true // Default `true`, (true | false | 'readonly' | 'readable' | 'writable' | 'writeable')
        // }
      })
    ],
    output: {
      library: `${name}-[name]`,
      libraryTarget: 'umd' // 把微应用打包成 umd 库格式
      // jsonpFunction: `webpackJsonp_${name}`,
    }
  },
  chainWebpack(config) {
    config.plugin('html').tap((args) => {
      args[0].title = defaultSettings.title;
      return args;
    });
    // config.module.rule('fonts').use('url-loader').loader('url-loader').options({}).end();
  },
  devServer: {
    client: { // 关掉接口错误弹窗
      overlay: false
    },
    headers: {
      'Access-Control-Allow-Origin': '*'
    }
    // proxy: {
    //   '/api': {
    //     target: 'http://192.168.4.35:32000',
    //     ws: true,
    //     changOrigin: true,
    //     pathRewrite: {
    //       '^/api': ''
    //     }
    //   }
    // }
  },
  lintOnSave: false
};
