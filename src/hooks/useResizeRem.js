import { useResizeObserver, useDebounceFn } from '@vueuse/core';

/**
 * 监听给定元素的尺寸改变事件，动态设置根元素的fontSize值，适用于大屏或自适应布局
 * @param {*} draftWidth 设计稿的宽度 默认1920
 * @returns ref
 */
export function useResizeRem(draftWidth = 1920) {
  const domRef = ref(null);

  useResizeObserver(domRef, entries => {
    const entry = entries[0];
    const { width } = entry.contentRect;
    debouncedFn(width);
  });

  const debouncedFn = useDebounceFn(width => {
    const baseSize = 10;
    const scale = width / draftWidth;
    document.documentElement.style.fontSize = baseSize * Math.min(scale, 2) + 'px';
  }, 20);

  return {
    domRef
  };
}
