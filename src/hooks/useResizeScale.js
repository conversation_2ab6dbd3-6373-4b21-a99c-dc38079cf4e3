/**
 *  监听窗口改变事件，动态计算缩放比例，通过scale属性进行适配
 * @param {*} draftWidth 设计稿宽度
 * @param {*} draftHeight 设计稿高度
 * @returns
 */
export function useResizeScale(draftWidth = 1920, draftHeight = 1080) {
  const domRef = ref(null);

  const timer = ref(0);

  const scale = {
    width: '1',
    height: '1'
  };

  /** 设计稿的宽高 需要保持的比例 */
  const draftRate = parseFloat((draftWidth / draftHeight).toFixed(5));

  function calcRate() {
    /** 当前宽高比 */
    const currentRate = parseFloat((window.innerWidth / window.innerHeight).toFixed(5));

    if (domRef.value) {
      // 表示更宽
      if (currentRate > draftRate) {
        scale.width = ((window.innerHeight * draftRate) / draftWidth).toFixed(5);
        scale.height = (window.innerHeight / draftHeight).toFixed(5);
        domRef.value.style.transform = `scale(${scale.width}, ${scale.height})`;
        // 表示更高
      } else {
        scale.height = (window.innerWidth / draftRate / draftHeight).toFixed(5);
        scale.width = (window.innerWidth / draftWidth).toFixed(5);
        domRef.value.style.transform = `scale(${scale.width}, ${scale.height})`;
        console.log(scale);
      }
    }
  }

  /** 重新计算比例 增加防抖 */
  function resize() {
    clearTimeout(timer.value);
    timer.value = window.setTimeout(() => {
      calcRate();
    }, 200);
  }

  // 改变窗口大小重新绘制
  const windowDraw = () => {
    window.addEventListener('resize', resize);
  };

  // 改变窗口大小重新绘制
  const unWindowDraw = () => {
    window.removeEventListener('resize', resize);
  };

  onMounted(() => {
    windowDraw();
  });
  onBeforeUnmount(() => {
    unWindowDraw();
  });

  return {
    domRef,
    calcRate
  };
}
