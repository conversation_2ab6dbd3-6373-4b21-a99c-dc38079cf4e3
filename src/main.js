/* eslint-disable no-undef */
import * as Vue from 'vue';
import App from './App.vue';
import * as VueRouter from 'vue-router';
import VueCookies from 'vue3-cookies';
// ---解决浏览器 DOM 元素大小动态变化时出现了循环引用的问题警告 //
const _ResizeObserver = window.ResizeObserver;
window.ResizeObserver = class ResizeObserver extends _ResizeObserver {
  constructor(callback) {
    callback = debounce(callback, 200);
    super(callback);
  }
};
const debounce = (fn, delay) => {
  let timer = null;
  return function() {
    const context = this;
    const args = arguments;
    clearTimeout(timer);
    timer = setTimeout(function() {
      fn.apply(context, args);
    }, delay);
  };
};
// --- //
import store from '@/store';
import './css/common.scss';
import ccDialog from './components/common/ccDialog';
import './assets/font_icon/iconfont.css';
import 'qianji-ui/lib/style.css'; // 引入千机UI的样式
import QianJiUi, { ZrMessage } from 'qianji-ui';
import { getRoutes } from '@/router/getRoutes/getRoutes';
import clipboard3 from "vue-clipboard3";
import { routes } from './router/getRoutes/routeList';
import { useStore } from '@/store/store';
import { storeToRefs } from 'pinia';

import VMdEditor from '@kangc/v-md-editor/lib/codemirror-editor';
import '@kangc/v-md-editor/lib/style/codemirror-editor.css';
import githubTheme from '@kangc/v-md-editor/lib/theme/github.js';
import '@kangc/v-md-editor/lib/theme/style/github.css';
// highlightjs
import hljs from 'highlight.js';
// codemirror 编辑器的相关资源
import Codemirror from 'codemirror';
// mode
import 'codemirror/mode/markdown/markdown';
import 'codemirror/mode/javascript/javascript';
import 'codemirror/mode/css/css';
import 'codemirror/mode/htmlmixed/htmlmixed';
import 'codemirror/mode/vue/vue';
// edit
import 'codemirror/addon/edit/closebrackets';
import 'codemirror/addon/edit/closetag';
import 'codemirror/addon/edit/matchbrackets';
// placeholder
import 'codemirror/addon/display/placeholder';
// active-line
import 'codemirror/addon/selection/active-line';
// scrollbar
import 'codemirror/addon/scroll/simplescrollbars';
import 'codemirror/addon/scroll/simplescrollbars.css';
// style
import 'codemirror/lib/codemirror.css';
const { toClipboard } = clipboard3();
export const router = VueRouter.createRouter({
  history: VueRouter.createWebHistory('/mfp'),
  // history: VueRouter.createWebHashHistory(),
  scrollBehavior: () => ({
    top: 0
  }),
  routes: []
});
// 路由守卫--start
import { useCookies } from 'vue3-cookies';
const { cookies } = useCookies();
if (!window.__POWERED_BY_QIANKUN__) {
  router.beforeEach((to, from, next) => {
    // 判断是否有权限,返回登录界面
    const token = cookies.get('dsp_token');
    if (to.meta && to.meta.isNotLogin) {
      next();
    } else {
      if (token) {
        const stores = useStore();
        const { routeBool } = storeToRefs(stores);
        if (!routeBool.value) {
          getRoutes().then(() => {
            next({ ...to, replace: true });
            // window.$vueApp.use(router);
            // window.$vueApp.mount('#app');
            // if (
            //   window.__POWERED_BY_QIANKUN__ &&
            //   process.env.NODE_ENV === 'development'
            // ) {
            //   // 全局挂载子应用vm
            //   window.__QIANKUN_SUB_APP_VM__ = window.$vueApp;
            //   // 以下代码都要注意在开发环境开启即可
            //   var subDiv = document.createElement('div');
            //   subDiv.__vue__ = window.$vueApp;
            //   document.body.appendChild(subDiv);
            // }
          });
        } else {
          next();
        }
      } else {
        next("/");
      }
    }
  });
}

// 路由守卫--end
import { transformComponent } from './router/getRoutes/getRoutes';
import '@/css/independ.scss';
import '@/css/index.scss';


// 作为子应用运行时 注入开发环境下需要的配置
if (window.__POWERED_BY_QIANKUN__) {
  // eslint-disable-next-line camelcase
  __webpack_public_path__ = window.__INJECTED_PUBLIC_PATH_BY_QIANKUN__;
  store.state.__POWERED_BY_QIANKUN__ = true;
  console.log(window);
}
function render(props = {}) {
  // 先获取路由表再渲染
  if (window.__POWERED_BY_QIANKUN__) {
    // 接受父组件的eventBus
    window.ce = props.ce;
  }
  ce.emit('appBeforeLoad');

  const { container } = props;
  const parentStore = props.store;
  VMdEditor.Codemirror = Codemirror;
  VMdEditor.use(githubTheme, {
    Hljs: hljs
  });
  window.$vueApp = null;
  window.$vueApp = Vue.createApp(App);
  window.$vueApp.use(store);
  window.$vueApp.use(VueCookies);
  window.$vueApp.use(VMdEditor);
  window.$vueApp.component('cc-dialog', ccDialog);
  // window.$vueApp.directive('loading', loading);
  // import('./components/install.js')

  window.$vueApp.config.globalProperties.copyFun = (text) => { // 全局复制方法
    var content;
    if (typeof text === 'string') {
      content = text;
    } else {
      content = JSON.stringify(text);
    }
    toClipboard(content);
    ZrMessage.success('复制成功');
  };
  window.$vueApp.config.globalProperties.routerAppend = (path, pathToAppend) => {
    return path + (path.endsWith('/') ? '' : '/') + pathToAppend;
  };
  window.$vueApp.use(QianJiUi, { size: 'default', zIndex: 3000 });

  // 从主应用传过来的
  window.$vueApp.config.globalProperties.$parentStore = parentStore;
  window.$parentStore = parentStore;

  // getRoutes().then(() => {
  window.$vueApp.use(router);
  window.$vueApp.mount('#app');
  routes.forEach(route => {
    transformComponent(route);
    if (route.children && route.children.length > 0) {
      route.children.forEach(child => {
        transformComponent(child);
        if (child.children && child.children.length > 0) {
          child.children.forEach(sun => {
            transformComponent(sun);
          });
        }
      });
    }
    router.addRoute(route);
  });

  //   window.$vueApp.mount(container ? container.querySelector('#app') : '#app');
  //   // window.$vueApp.mount('#app');

  //   // if (
  //   //   window.__POWERED_BY_QIANKUN__ &&
  //   //   process.env.NODE_ENV === 'development'
  //   // ) {
  //   //   // 全局挂载子应用vm
  //   //   window.__QIANKUN_SUB_APP_VM__ = window.$vueApp;
  //   //   // 以下代码都要注意在开发环境开启即可
  //   //   var subDiv = document.createElement('div');
  //   //   subDiv.__vue__ = window.$vueApp;
  //   //   document.body.appendChild(subDiv);
  //   // }
  // });
}

export async function bootstrap() {
  console.log('[vue] vue app bootstraped');
}
export async function mount(props) {
  console.log('[vue] 从主应用启动，注入参数：', props);
  render(props);
}
export async function unmount() {
  try {
    window.$vueApp.unmount();
    window.$vueApp._container.innerHTML = '';
    window.$vueApp._container = null;
  } catch (error) {
    console.error(error);
  }
  window.$vueApp = null;
  // router = null
}

// 独立运行时
if (!window.__POWERED_BY_QIANKUN__) {
  // 独立启动时，添加一个额外的class
  document.querySelector('body').classList.add('by_independ');

  console.log('[vue] 独立启动');
  // 独立启动时替换 主应用传过来的eventbus
  window.ce = {
    emit: function(name) { },
    on: function(name, fn) { }
  };
  render();
}
