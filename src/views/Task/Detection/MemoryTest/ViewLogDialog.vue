<template>
  <div>
    <zr-dialog
      v-bind="$attrs"
      destroy-on-close
      title="查看日志"
    >
      <div
        v-for="(item,index) in logData"
        :key="index"
        class="log-data-style"
        style="margin-bottom:10px"
      >
        <span class="time-style">{{ timeFormat(item.created_at) }}</span>
        <span>{{ item.log }}</span>
      </div>
      <template #footer>
        <div style="flex: auto">
          <zr-button
            size="default"
            @click="cancelDialogClick"
          >
            关闭
          </zr-button>
        </div>
      </template>
    </zr-dialog>
  </div>
</template>
<script setup>
import { ref, defineEmits, defineProps } from 'vue';
import { getLog } from '@/api/task/memory-test';
import { timeFormat } from '@/utils/formatting';

const emit = defineEmits(['cancelLogDialogClick']);
const props = defineProps({
  rowId: {
    type: String,
    default: ''
  }
});
watch(() => props.rowId, (value) => {
  if (value) {
    getLogFun();
  }
});

const logData = ref([]);
function getLogFun() {
  getLog(props.rowId).then(res => {
    console.log(res.data.data.List, "查看日志");
    logData.value = res.data.data.List;
  });
}

function cancelDialogClick() {
  emit('cancelLogDialogClick');
}
</script>
<style lang="scss" scoped>
:deep(.el-dialog__body){
  text-align: left;
}
.log-data-style{
  margin-bottom: 10px;
  .time-style{
    margin-right:9px;
  }
}
</style>
