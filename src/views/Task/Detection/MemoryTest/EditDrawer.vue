<template>
  <div>
    <zr-drawer
      v-bind="$attrs"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      @open="draOpen"
    >
      <template #header>
        <h4 style="color: #303133;">
          修改分析配置
        </h4>
      </template>
      <div
        class="titleStyle"
      >基本信息</div>
      <p
        v-for="item in basicMessage"
        :key="item.key"
        class="basic"
        :label="item.label"
      >
        <span>{{ item.label }}：</span>
        <span v-if="item.key == 'size'">{{ numDelivery(getData[item.key]) }}</span>
        <span v-else-if="getData[item.key] !==''">{{ getData[item.key] }}</span>
        <span v-else> - </span>
      </p>
      <div
        class="titleStyle"
        style="margin-top:30px"
      >重新分析配置信息</div>
      <zr-form label-position="top">
        <zr-form-item label="操作系统类型">
          <zr-radio-group
            v-model="editForm.os"
            @change="typeChange"
          >
            <zr-radio-button label="windows">Windows</zr-radio-button>
            <zr-radio-button label="linux">Linux</zr-radio-button>
          </zr-radio-group>
        </zr-form-item>
        <zr-form-item
          label="所属任务"
        >
          <zr-select
            v-model="editForm.task_id"
            filterable
            @change="taskChange"
          >
            <zr-option
              v-for="item in taskOption"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </zr-select>
        </zr-form-item>
        <zr-form-item label="系统内核符号表">
          <template #label>
            <div class="label-wrapper">
              <span class="label-text">系统内核符号表</span>
              <zr-icon
                name="InfoFilled"
                size="16"
                color="#1890FF"
              />
              <span class="warnTip">请确保您所选择的系统内核符号表与内存镜像匹配</span>
            </div>
          </template>
          <zr-select
            v-model="editForm.symbol_id"
            filterable
            clearable
            @change="symbolChange"
          >
            <zr-option
              label="内置符号表"
              value=""
            />
            <zr-option
              v-for="item in symbolOption"
              :key="item.Id"
              :label="item.Name"
              :value="item.Id"
            />
          </zr-select>
        </zr-form-item>
        <zr-form-item
          label=""
          prop="volatility_version"
        >
          <template #label>
            <div style="display: flex;align-items: center;">
              内存分析引擎
              <zr-tooltip
                class="box-item"
                effect="dark"
                placement="top-start"
              > <template #content> V3：在V2引擎的基础上，可兼容更高版本的操作系统<br>V2：适用于 Windows 10.0.19041 和 Linux Kernel 4.4 以及更早版本的操作系统</template>
                <zr-icon
                  style="margin-left: 4px;"
                  name="InfoFilled"
                  size="15"
                />
              </zr-tooltip>
            </div>
          </template>
          <zr-select
            v-model="editForm.volatility_version"
          >
            <zr-option
              label="V2"
              value="V2"
            />
            <zr-option
              label="V3"
              value="V3"
            />
          </zr-select>
        </zr-form-item>
        <zr-form-item
          label="分析插件"
          prop="plugin_ids"
        >
          <zr-select
            v-model="editForm.plugin_ids"
            :options="pluginsOption"
            multiple
            clearable
            collapse-tags
            collapse-tags-tooltip
          />
        </zr-form-item>
      </zr-form>
      <template #footer>
        <zr-button
          type="primary"
          size="default"
          @click="submit"
        >
          分析
        </zr-button>
        <zr-button
          size="default"
          @click="cancelClick"
        >
          取消
        </zr-button>
      </template>
    </zr-drawer>
  </div>
</template>
<script setup>
import { ref, defineEmits, defineProps } from 'vue';
import { getDetectionItems, editDetectionItems } from '@/api/task/memory-test';
import { pluginsOptionApi } from '@/api/task/dataPacket';
import { symbolsList } from '@/api/rule/symbol';
import { tasksList } from '@/api/task/management';
import { ZrMessage } from 'qianji-ui';


const emit = defineEmits(['cancelClick', 'saveClick']);
const props = defineProps({
  editRowId: {
    type: String,
    default: ''
  },
  editRowOs: {
    type: String,
    default: ''
  },
  editRow: {
    type: Object,
    default: {}
  }
});
const pluginsOption = ref([]);

watch(() => props.editRowId, (value) => {
  if (value) {
    // getDetectionItemsFun();
    // SymbolsListFun();
    // tasksListFun();
  }
});
const editForm = reactive({
  'task_id': '',
  'symbol_id': '',
  'os': '',
  'volatility_version': '',
  'plugin_ids': []
});
const getData = ref({});
const basicMessage = [
  {
    label: '类型',
    key: 'data_type'
  },
  {
    label: '文件名',
    key: 'image_name'
  },
  {
    label: '校验和（Etag）',
    key: 'etag'
  },
  {
    label: '大小（GB）',
    key: 'size'
  }
];
const searchForm = ref({
  os: '',
  params: '',
  'is_built_in': 'custom',
  'page_index': 1,
  'page_size': 20
});
const taskSearchForm = ref({
  name: '',
  page: 1,
  'page_size': 999
});
const symbolOption = ref([]);
const taskOption = ref([]);

function draOpen() {
  getDetectionItemsFun();
  SymbolsListFun();
  tasksListFun();
  pluginsOptionFun(props.editRowOs);
  editForm['volatility_version'] = props.editRow.volatility_version;
}

function getDetectionItemsFun() {
  getDetectionItems(props.editRowId).then(res => { // 获取详情
    getData.value = res.data.data;
    editForm['task_id'] = res.data.data.task_id;
    editForm['symbol_id'] = res.data.data.symbol_id;
    editForm['plugin_ids'] = res.data.data.plugin_ids;
    editForm['volatility_version'] = res.data.data.volatility_version;
  });
}

function typeChange(type) {
  editForm['plugin_ids'] = []; // 切换操作系统后，先清空分析插件已选择的内容再重新赋值
  pluginsOptionFun(type);
}

function pluginsOptionFun(type) { // 分析插件
  editForm['os'] = type;
  pluginsOptionApi({ os: type }).then(res => {
    pluginsOption.value = res.data.data.list;
  });
}

function tasksListFun() {
  tasksList(taskSearchForm.value).then(res => {
    taskOption.value = res.data.data.list;
  });
}

function taskChange(val) {
  editForm['task_id'] = val;
}

function SymbolsListFun() {
  searchForm.value.os = props.editRowOs == 'windows' ? 'Windows' : 'Linux';
  symbolsList(searchForm.value).then(res => {
    symbolOption.value = res.data.data.list;
  });
}
function symbolChange(val) {
  editForm['symbol_id'] = val;
}
function submit() {
  editForm['plugin_ids'] = editForm['plugin_ids'].join(',');
  editForm['os'] = editForm['os'] == 'windows' ? 'Windows' : 'Linux';
  editDetectionItems({ id: props.editRowId, editForm }).then(res => {
    if (res.data.code == 0) {
      ZrMessage.success('修改成功');
    } else {
      ZrMessage.success(res.data.messge);
    }
  });
  emit('saveClick');
}
function cancelClick() {
  emit('cancelClick');
}
function numDelivery(num) { // 从B转化为GB
  var size = num / 1024 / 1024 / 1024;
  var result = parseFloat(size);
  if (isNaN(result)) {
    return false;
  }
  result = Math.round(size * 100) / 100;
  return result;
}
</script>
<style lang="scss" scoped>
// .basic{
//     font-size: 14px;
//     }
.el-alert.el-alert--info.is-light {
    background: #fff;
    padding: 0px;
}
.label-wrapper {
  display: flex;
  align-items: center;
}

.label-text {
  margin-right: 5px;
  color: #606266;
}

.warnTip{
    color:#1890FF;
    margin-left:5px
}
.titleStyle{
  font-size: 14px;
  font-weight: 800;
  margin-bottom: 10px;
}
p{
  line-height: 30px;
  color:#606266;
    display: flex;
    align-items: center;
    font-size: 14px;
  }
</style>
