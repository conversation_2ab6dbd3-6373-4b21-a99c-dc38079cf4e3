<template>
  <div class="">
    <zr-row
      :gutter="24"
      justify="space-between"
      style="white-space: nowrap"
    >
      <zr-col :span="6">
        <zr-button
          :disabled="deleteList.length<=0"
          type="primary"
          size="default"
          @click="associationHostClick('project')"
        >批量关联任务</zr-button>
        <zr-button
          type="danger"
          size="default"
          plain
          :disabled="deleteList.length<=0"
          @click="batchDeleteClick"
        >批量删除</zr-button>
      </zr-col>
      <zr-col
        :span="18"
        style="text-align: right;"
      >
        <zr-form :inline="true">
          <zr-form-item>
            <zr-select
              v-model="searchForm.task"
              size="default"
              @change="searchClick"
            >
              <zr-option
                v-for="item in taskOption"
                :key="item"
                :label="item.key"
                :value="item.value"
              />
            </zr-select>
          </zr-form-item>
          <zr-form-item>
            <zr-select
              v-model="searchForm.status"
              size="default"
              @change="searchClick"
            >
              <zr-option
                v-for="item in statusOption"
                :key="item"
                :label="item.key"
                :value="item.value"
              />
            </zr-select>
          </zr-form-item>
          <zr-form-item>
            <zr-date-picker
              v-model="timeArr"
              type="datetimerange"
              :shortcuts="shortcuts"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              @change="dateChange"
            />
          </zr-form-item>
          <zr-form-item>
            <zr-input
              v-model="searchForm.name"
              placeholder="镜像名称/主机名称"
              size="default"
              @keydown.enter.prevent="searchClick"
            >
              <template #append>
                <zr-button
                  @click="searchClick"
                >
                  <icon name="zr-search" />
                </zr-button>
              </template>
            </zr-input>
          </zr-form-item>
        </zr-form>

      </zr-col>
    </zr-row>
    <zr-table
      ref="tableRef"
      v-loading="loading"
      :data="tableData"
      :row-key="getRowKey"
      :default-sort="{ prop: 'start_time', order: 'descending' }"
      empty-text="暂无数据"
      @selection-change="handleSelectionChange"
      @expand-change="handleExpandChange"
      @sort-change="sortChange"
    >
      <zr-table-column
        type="expand"
        width="30"
      >
        <template
          #default="props"
        >
          <div class="detail-msg">
            <zr-card>
              <zr-row
                :gutter="24"
                class="memory-test"
              >
                <zr-col :span="9">
                  <h3 class="title-pie">数据包基本信息</h3>
                  <p
                    v-for="item in basicMessage"
                    :key="item.value"
                    :label="item.label"
                  >
                    <span>{{ item.label }}：</span>
                    <span v-if="expandData[props.row.id]">
                      <span v-if="item.value=='size'">{{ numDelivery(expandData[props.row.id][item.value]) }}</span>
                      <span v-else>{{ expandData[props.row.id][item.value] || '-' }}</span>
                      <zr-button
                        v-if="item.copy && expandData[props.row.id][item.value]"
                        type="primary"
                        link
                        left-icon="zr-copy-file"
                        @click="copyClick(expandData[props.row.id][item.value] )"
                      />
                    </span>
                  </p>
                </zr-col>
                <zr-col :span="9">
                  <h3 class="title-pie">关联信息</h3>
                  <p
                    v-for="item in relatesMessage"
                    :key="item.value"
                    :label="item.label"
                  >
                    <span>{{ item.label }}：</span>
                    <span v-if="expandData[props.row.id]">
                      <span v-if="item.value == 'ip' && expandData[props.row.id][item.value] && expandData[props.row.id][item.value].length>0">
                        {{ expandData[props.row.id][item.value].join(',') }}
                      </span>
                      <span
                        v-else-if="item.value == 'os'"
                        :class="expandData[props.row.id][item.value]=='linux' ? '' : 'system-icon-style'"
                      >
                        <SystemIcon :name="expandData[props.row.id][item.value]" />
                      </span>
                      <span v-else>{{ expandData[props.row.id][item.value] || '-' }}</span>
                    </span>
                
                  </p>
                </zr-col>
                <zr-col :span="6">
                  <h3 class="title-pie">执行信息</h3>
                  <p
                    v-for="item in executionMessage"
                    :key="item.value"
                    :label="item.label"
                  >
                    <span>{{ item.label }}：</span>
                    <span v-if="expandData[props.row.id]">
                      <span v-if="item.value === 'status'">
                        {{ progressOptionList[expandData[props.row.id][item.value]] }}
                        <span v-if="item.progress"> ({{ expandData[props.row.id].progress }}) </span>
                      </span>
                      <span v-else-if="item.value === 'start_time'">{{ timeFormat(expandData[props.row.id][item.value]) }}   <zr-tag v-if="expandData[props.row.id].start_time_ago">{{ expandData[props.row.id].start_time_ago }}</zr-tag> </span>
                      <span v-else>{{ timeFormat(expandData[props.row.id][item.value]) }}    <zr-tag v-if="expandData[props.row.id].updated_time_ago">{{ expandData[props.row.id].updated_time_ago }}</zr-tag> </span>
                    </span>
                  </p>
                </zr-col>
              </zr-row>
            </zr-card>
          </div>
        </template>
      </zr-table-column>
      <zr-table-column
        type="selection"
        width="30"
        :reserve-selection="true"
      />
      <zr-table-column
        label="数据类型/镜像名称"
        prop="name"
        width="220"
      >
        <template #default="scope">
          <div
            v-if="scope.row.data_type || scope.row.image_name"
            class="data-type"
          >
            <span class="table-item-content-one">{{ scope.row.data_type }}</span>
            <zr-tooltip
              v-if="scope.row.image_name"
              effect="dark"
              :content="scope.row.image_name"
              placement="top"
            >
              <p class="table-item-content-one">{{ scope.row.image_name }}</p>
            </zr-tooltip>
          </div>
          <div v-else>-</div>
        </template>
      </zr-table-column>
      <zr-table-column
        label="操作系统类型"
        prop="os"
        width="120"
        show-overflow-tooltip
      >
        <template #default="scope">
          <span
            v-if="scope.row.os"
            :class="scope.row.os=='linux' ?'':'system-icon-style'"
          >
            <SystemIcon :name="scope.row.os" />
          </span>
          <span v-else>-</span>
        </template>
      </zr-table-column>
      <zr-table-column
        label="关联主机"
        prop="host_name"
        width="240"
      >
        <template #default="scope">
          <span v-if="scope.row.host_name || scope.row.ip">
            <p class="table-item-content-one">{{ scope.row.host_name }}</p>
            <zr-tooltip
              v-if="scope.row.ip && scope.row.ip.length>0"
              effect="dark"
              :content="scope.row.ip.join(',')"
              placement="top"
            >
              <p class="table-item-content-one">{{ scope.row.ip.join(',') }}</p>
            </zr-tooltip>
          </span>
          <span v-else> - </span>
        </template>
      </zr-table-column>
      <zr-table-column
        label="分析引擎"
        prop="volatility_version"
      />
      <zr-table-column
        label="所属任务"
        prop="task_name"
      />
      <zr-table-column
        label="所属用户"
        prop="user_name"
        width="150"
      />
      <zr-table-column
        label="当前分析状态"
        prop="status"
      >
        <template #default="scope">
          <div style="display: flex;align-items: center;">
            <Icon
              :name="progressOptionIcon[scope.row.status]"
              :size="15"
              :color="customColorMethod(scope.row.status)"
            />
            <p style="padding-left:7px">{{ progressOptionList[scope.row.status] }}</p>
          </div>
          <div
            class="progress_style"
          >
            <zr-progress
              :percentage="validatePercentage(scope.row.progress)"
              :color="customColorMethod(scope.row.status)"
            />
          </div>
        </template>
      </zr-table-column>
      <zr-table-column
        label="分析开始时间"
        prop="start_time"
        sortable
        width="160"
      >
        <template #default="scope">
          <div v-if="scope.row.start_time_ago || scope.row.start_time">
            <zr-tag v-if="scope.row.start_time_ago">{{ scope.row.start_time_ago }}</zr-tag>
            <p>{{ timeFormat(scope.row.start_time) }}</p>
          </div>
          <div v-else>-</div>
        </template>
      </zr-table-column>
      <zr-table-column
        label="最近更新时间"
        prop="updated_at"
        sortable
        width="160"
      >
        <template #default="scope">
          <div v-if="scope.row.updated_time_ago || scope.row.updated_at">
            <zr-tag v-if="scope.row.updated_time_ago">{{ scope.row.updated_time_ago }}</zr-tag>
            <p>{{ timeFormat(scope.row.updated_at) }}</p>
          </div>
          <div v-else>-</div>
        </template>
      </zr-table-column>
      <zr-table-column
        label="操作"
        prop=""
        width="150px"
      >
        <template #default="scope">
          <div class="buttonStyle">
            <zr-button
              type="primary"
              link
              :disabled="scope.row.status !=='success' || scope.row.success_record_id ==''"
              @click="viewReportClick(scope.row)"
            >查看报告</zr-button>
            <zr-dropdown>
              <span class="zr-dropdown-link">
                更多
                <Icon
                  name="zr-down"
                  class="el-icon--right"
                />
              </span>
              <template #dropdown>
                <zr-dropdown-menu>
                  <zr-dropdown-item
                    command="analysis"
                    :disabled="!scope.row.analysis_able"
                    @click="analysisClick(scope.row)"
                  >解析并检测</zr-dropdown-item>
                  <zr-dropdown-item
                    command="onlyDetect"
                    :disabled="!scope.row.detect_able"
                    @click="onlyDetectClick(scope.row)"
                  >仅检测</zr-dropdown-item>
                  <zr-dropdown-item
                    command="associationHost"
                    @click="associationHostClick('host',scope.row)"
                  >关联主机</zr-dropdown-item>
                  <zr-dropdown-item
                    command="editAnalysis"
                    @click="editAnalysisClick(scope.row)"
                  >更改分析配置</zr-dropdown-item>
                  <zr-dropdown-item
                    command="viewLog"
                    :disabled="scope.row.status !== 'error' && scope.row.status !== 'detect_error'"
                    @click="viewLogClick(scope.row)"
                  >查看日志</zr-dropdown-item>
                  <zr-dropdown-item
                    command="downloadLog"
                    :disabled="scope.row.status !== 'error' && scope.row.status !== 'success'"
                    @click="downloadLogClick(scope.row)"
                  >下载日志</zr-dropdown-item>
                  <zr-dropdown-item
                    command="delete"
                    @click="deleteClick(scope.row)"
                  >删除</zr-dropdown-item>
                </zr-dropdown-menu>
              </template>
            </zr-dropdown>
          </div>
        </template>
      </zr-table-column>
     
    </zr-table>
    <zr-pagination
      :current-page="searchForm.page"
      :page-size="searchForm.page_size"
      :page-sizes="[20, 40, 60, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
    <!-- 关联主机弹窗内容 -->
    <zr-dialog
      v-model="dialogFormVisible"
      :destroy-on-close="true"
      :title="dialogFormVisibleTitle"
      width="30%"
    >
      <zr-alert
        :title="`重新${dialogFormVisibleTitle}后，所有数据归属都将发生变化。`"
        show-icon
        :closable="false"
        style="margin-bottom:20px"
        center
      />
      <span style="margin-right:5px">请选择{{ dialogFormVisibleTitle }}</span>
      <zr-select
        v-model="hostForm.task_id"
        filterable
        placeholder="请选择"
        remote
        :remote-method="remoteMethod"
        @change="hostChange"
      >
        <zr-option
          v-for="item in assocHostOption"
          :key="item.id"
          :label="dialogFormVisibleTitle=='关联主机'? item.host : item.name"
          :value="item.id"
        />
      </zr-select>
      <template #footer>
        <div style="flex: auto">
          <zr-button
            type="primary"
            size="default"
            @click="saveDialogClick"
          >
            确定
          </zr-button>
          <zr-button
            size="default"
            @click="cancelDialogClick"
          >
            取消
          </zr-button>
        </div>
      </template>
    </zr-dialog>
    <!-- 更改分析配置 -->
    <EditDrawer
      v-model="editDrawerVisible"
      :edit-row-id="editRowId"
      :edit-row-os="editRowOs"
      :edit-row="editRow"
      @saveClick="saveClick"
      @cancelClick="cancelClick"
    />
    <!-- 查看日志 -->
    <ViewLogDialog
      v-model="dialogVisible"
      :row-id="rowId"
      @cancelLogDialogClick="cancelLogDialogClick"
    />
  </div>
</template>
<script setup>
import { ref, onMounted, onBeforeUnmount, getCurrentInstance } from 'vue';
import { SystemIcon, ZrMessage, ZrMessageBox } from 'qianji-ui';
import EditDrawer from './EditDrawer.vue';
import ViewLogDialog from './ViewLogDialog';
import { getOptions, getDetectionList, deleteDetection, batchDeleteDetection, analysis, getAssocHost, getDetectionItems, assocTask, assocHost, getTaskOptions, detect } from '@/api/task/memory-test';
import { formatDate } from '@/utils/index';
import { timeFormat } from '@/utils/formatting';
import { useRouter } from 'vue-router';
import { downloadFun } from '@/utils/downloadUtils';

const router = useRouter();
const { proxy } = getCurrentInstance();
const taskOption = ref([]);
const statusOption = ref([]);

const shortcuts = [
  {
    text: '最近7天',
    value: () => {
      // const end = new Date();
      const end = timeFormat(new Date().setHours(23, 59, 59, 59));
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      const newStart = timeFormat(start.setHours(0, 0, 0, 0));
      return [newStart, end];
    }
  },
  {
    text: '最近30天',
    value: () => {
      const end = timeFormat(new Date().setHours(23, 59, 59, 59));
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      const newStart = timeFormat(start.setHours(0, 0, 0, 0));
      return [newStart, end];
    }
  },
  {
    text: '最近90天',
    value: () => {
      const end = timeFormat(new Date().setHours(23, 59, 59, 59));
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
      const newStart = timeFormat(start.setHours(0, 0, 0, 0));
      return [newStart, end];
    }
  }
];
const progressOptionList = ref({ // 进度状态
  waiting: '等待中',
  analyzing: ' 解析中',
  detecting: '检测中',
  'not_analyzed': '未分析',
  success: '成功',
  error: '分析异常',
  'detect_error': '检测异常'
});
const progressOptionIcon = ref({ // 进度状态
  waiting: 'zr-time',
  analyzing: 'zr-history',
  detecting: 'zr-cancel-withdrawal-fill',
  'not_analyzed': 'zr-circle-Warning',
  success: 'zr-circle-seleted',
  error: 'zr-circle-close',
  'detect_error': 'zr-vulnerability-control'
});
const customColorMethod = (status) => { // 进度条颜色
  if (status == 'waiting') {
    return '#FFB968'; // 橙色
  } else if (status == 'not_analyzed') {
    return '#B0B0B0'; // 灰色
  } else if (status == 'analyzing') {
    return '#53A9FF'; // 蓝色
  } else if (status == 'success') {
    return '#8BCF6D'; // 绿色
  } else if (status == 'detecting') {
    return '#3062f1';
    // return '#1094A3';
  }
  return '#D72C00'; // 红色
};
function validatePercentage(progress) {
  if (progress >= 0 && progress <= 100) {
    return progress;
  } else {
    return 0;
  }
}
const searchForm = ref({
  task: '',
  status: '',
  'start_time': '',
  'end_time': '',
  name: '',
  page: 1,
  "page_size": 20,
  sort: '-start_time'
});
const timeArr = ref([]);
// const formPicker = ref({});
// onMounted(() => {
//   formPicker.value = sessionStorage.getItem('formPicker');
//   const start = new Date(); // 获取当前时间
//   start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
//   const startDate = timeFormat(start.setHours(0, 0, 0, 0)); // 转化成标准时间格式
//   const endDate = timeFormat(new Date().setHours(23, 59, 59, 59));
//   timeArr.value = formPicker.value !== null ? JSON.parse(formPicker.value).time : [startDate, endDate];
//   searchForm.value['start_time'] = timeArr.value[0];
//   searchForm.value['end_time'] = timeArr.value[1];
//   getDetectionListFun();
// });

getDetectionListFun();

onBeforeUnmount(() => { // 离开页面，销毁定时器
  clearInterval(inter.value);
});
onMounted(() => {
  routerNameFun();
});
const loading = ref(true);
const total = ref(0);
const tableRef = ref();
const tableData = ref([]);
const expandData = ref({});
const deleteList = ref([]);
const rowId = ref('');
const rowImageId = ref('');
const editRowId = ref('');
const editRowOs = ref('');
const editRow = ref({});
const basicMessage = [ // 数据包基本信息
  {
    label: '文件名',
    value: 'image_name',
    copy: true
  },
  {
    label: '数据包类型',
    value: 'data_type'
  },
  {
    label: '大小（GB）',
    value: 'size'
  },
  {
    label: '校验和（Etag）',
    value: 'etag',
    copy: true
  }
];
const relatesMessage = [ // 关联信息
  {
    label: '关联主机名',
    value: 'host_name'
  },
  {
    label: '关联主机IP',
    value: 'ip'
  },
  {
    label: '操作系统',
    value: 'os'
  },
  {
    label: '所属任务',
    value: 'task_name'
  },
  {
    label: '所属用户',
    value: 'user_name'
  }
];
const executionMessage = [ // 执行信息
  {
    label: '当前分析状态',
    value: 'status'
  },
  {
    label: '分析开始时间',
    value: 'start_time'
  },
  {
    label: '最近更新时间',
    value: 'updated_at'
  }
];
const dialogFormVisible = ref(false);
const dialogFormVisibleTitle = ref('');
const editDrawerVisible = ref(false);

const dialogVisible = ref(false);
const hostForm = reactive({ // 批量关联任务所需参数
  'task_id': '',
  ids: []
});

const assocHostOption = ref([]);

getOptionsFun();
function getOptionsFun() { // 获取参数下拉内容
  getOptions().then(res => {
    if (res.data.code == 0) {
      statusOption.value = res.data.data.status;
      taskOption.value = res.data.data.task;
    }
  });
}
function dateChange(val) { // 时间组件：点击确定时触发
  if (val) {
    searchForm.value['start_time'] = formatDate(val[0]);
    searchForm.value['end_time'] = formatDate(val[1]);
  } else { // 点时间组件中的清除按钮时，val为null
    searchForm.value['start_time'] = '';
    searchForm.value['end_time'] = '';
  }
  getDetectionListFun();
}
function getRowKey(row) { // 在列表刷新之前，保存已勾选的数据
  return row.id;
}

function routerNameFun() { // 接收从任务管理页面，跳转传的参数name
  if (router.currentRoute._value.query.id) {
    searchForm.value.task = router.currentRoute._value.query.id;
  }
  getDetectionListFun();
}

const inter = ref();
function getDetectionListFun() { // 获取列表内容
  getDetectionList(searchForm.value).then(res => {
    if (inter.value) {
      clearInterval(inter.value);
    }
    inter.value = setInterval(() => {
      getDetectionListFun();
    }, 10000);
    tableData.value = res.data.data.list;
    total.value = res.data.data.total;
  }).finally(() => {
    loading.value = false;
  });
}

function handleSelectionChange(val) {
  deleteList.value = val.map(item => item.id);
  hostForm.ids = deleteList.value;
  if (deleteList.value.length > 0) { // 如果勾选复选框就清除定时器，防止刷新的时候勾选的数据回显到其他页
    if (inter.value) {
      clearInterval(inter.value);
    }
  } else {
    getDetectionListFun();
  }
}

function copyClick(text) { // 复制
  proxy.copyFun(text);
}

function handleExpandChange(row) { // 列表的展开功能点击方法
  getDetectionItems(row.id).then(res => {
    expandData.value[row.id] = res.data.data;
  });
}

function searchClick() { // 搜索
  getDetectionListFun();
}

function viewReportClick(row) { // 查看报告
  if (row.status == 'success') {
    if (row.success_record_id) {
      window.open('/mfp/mirror-angle/' + row.detection_id);
    } else {
      ZrMessage.error('当前检测项暂无可查看的记录');
    }
  }
}
function analysisClick(row) { // 解析并检测
  analysis(row.id).then(res => {
    if (res.data.code == 0) {
      ZrMessage.success('开始解析并检测');
      getDetectionListFun();
      setTimeout(() => {
        getOptionsFun();
      }, 2000);
    } else {
      ZrMessage.error(res.data.data);
    }
  });
}

const hostQuery = reactive({
  host: '',
  page: 1,
  'page_size': 999
});
function getAssocHostFun(hostQuery) { // 获取关联主机下拉内容
  getAssocHost(hostQuery).then(res => {
    assocHostOption.value = res.data.data.data;
  });
}

const taskOptionsQuery = reactive({
  name: '',
  page: 1,
  'page_size': 999
});
function getTaskOptionsFun(taskOptionsQuery) { // 获取批量关联任务下拉内容
  getTaskOptions(taskOptionsQuery).then(res => {
    assocHostOption.value = res.data.data;
  });
}

function associationHostClick(type, row) { // 关联主机\批量关联任务
  hostForm['task_id'] = '';
  dialogFormVisible.value = true;
  dialogFormVisibleTitle.value = type === 'host' ? '关联主机' : '关联任务';
  if (type === 'host') {
    rowImageId.value = row.image_id;
    getAssocHostFun(hostQuery);
  } else {
    getTaskOptionsFun(taskOptionsQuery);
  }
}


function hostChange(val) { // 关联主机\批量关联任务勾选方法
  hostForm['task_id'] = val;
}

function remoteMethod(query) { // 关联主机\批量关联任务远程搜索方法
  if (dialogFormVisibleTitle.value == '关联主机') {
    if (query !== '') {
      hostQuery.host = query;
      getAssocHostFun(hostQuery);
    } else {
      hostQuery.host = '';
      getAssocHostFun(hostQuery);
    }
  } else {
    if (query !== '') {
      taskOptionsQuery.name = query;
      getTaskOptionsFun(taskOptionsQuery);
    } else {
      taskOptionsQuery.name = '';
      getTaskOptionsFun(taskOptionsQuery);
    }
  }
}

function saveDialogClick() {
  if (dialogFormVisibleTitle.value == '关联任务') {
    assocTask(hostForm).then(res => { // 批量关联任务接口
      if (res.data.code == 0) {
        ZrMessage.success('批量关联成功');
        getDetectionListFun();
        getOptionsFun();
        tableRef.value.clearSelection(); // 清空勾选框
      } else {
        ZrMessage.error(res.data.data);
      }
    });
  } else { // 关联主机接口
    assocHost(rowImageId.value, { 'host_id': hostForm.task_id }).then(res => {
      if (res.data.code == 0) {
        ZrMessage.success(res.data.data);
        getDetectionListFun();
      } else {
        ZrMessage.error(res.data.data);
      }
    });
  }
  dialogFormVisible.value = false;
}
 
function onlyDetectClick(row) { // 仅检测
  detect(row.id).then(res => {
    if (res.data.code == 0) {
      ZrMessage.success('开始检测');
      getDetectionListFun();
    } else {
      ZrMessage.error(res.data.data);
    }
  });
}

function editAnalysisClick(row) { // 更改分析设置
  editDrawerVisible.value = true;
  editRowId.value = row.id;
  editRowOs.value = row.os;
  editRow.value = row;
}

function viewLogClick(row) { // 查看日志
  dialogVisible.value = true;
  rowId.value = row.id;
}
function cancelLogDialogClick() { // 关闭查看日志
  dialogVisible.value = false;
}

function downloadLogClick(row) { // 下载日志
  downloadFun('/mfp-service/api/v1/detection_items/' + row.analysis_id + '/get_detail_log_file');
}

function batchDeleteClick() { // 批量删除
  ZrMessageBox.confirm('确认删除勾选的所有选项?', '确认操作', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    batchDeleteDetection({ 'ids': deleteList.value }).then(res => { // 批量删除接口
      if (res.data.code == 0) {
        ZrMessage.success('删除成功');
        getDetectionListFun();
        getOptionsFun();
        tableRef.value.clearSelection(); // 清空勾选框
      } else {
        ZrMessage.error(res.data.msg);
      }
    });
  });
}
function deleteClick(row) { // 删除
  ZrMessageBox.confirm('确认删除?', '确认操作', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      deleteDetection(row.id).then(res => {
        if (res.data.code == 0) {
          ZrMessage.success('删除成功');
          getDetectionListFun();
          getOptionsFun();
        } else {
          ZrMessage.error(res.data.msg);
        }
      });
    });
}

function handleSizeChange(val) {
  searchForm.value['page_size'] = val;
  searchForm.value.page = 1;
  getDetectionListFun();
}
function handleCurrentChange(val) {
  searchForm.value.page = val;
  getDetectionListFun();
}


function cancelDialogClick() {
  dialogFormVisible.value = false;
}
// ---更改分析配置
function saveClick() {
  editDrawerVisible.value = false;
  getDetectionListFun();
  setTimeout(() => {
    getOptionsFun();
  }, 2000);
}
function cancelClick() {
  editDrawerVisible.value = false;
}

function sortChange(data) { // 更新时间 排序
  const { prop, order } = data;
  if (order === 'ascending') {
    searchForm.value.sort = '+' + prop;
  } else {
    searchForm.value.sort = '-' + prop;
  }
  getDetectionListFun();
}

function numDelivery(num) { // 从B转化为GB
  var size = num / 1024 / 1024 / 1024;
  var result = parseFloat(size);
  if (isNaN(result)) {
    return false;
  }
  result = Math.round(size * 100) / 100;
  return result;
}
</script>
  <style lang="scss" scoped>
  .zr-dropdown-link {
  cursor: pointer;
  color: var(--el-color-primary);
  display: flex;
  align-items: center;
  margin-left: 10px
}
  .buttonStyle{
  display:flex;
  align-items:center ;
}

.memory-test{
  line-height: 24px;
  margin-bottom: 20px;
  h3{
    margin: 12px 0 8px;
    font-size: 14px;
  }
  p{
    display: flex;
    align-items: center;
    font-size: 14px;
    line-height: 30px;
  }
}

// //  列表内容不超过1行
// .table-item-content-one{
//   text-overflow: -o-ellipsis-lastline;
//   overflow: hidden;
//   text-overflow: ellipsis;
//   display: -webkit-box;
//   -webkit-line-clamp: 1;
//   line-clamp: 1;
//   -webkit-box-orient: vertical;
//   word-break: break-all;
// }

.progress_style{
  display: flex;
  align-items: center;
  :deep(.el-progress--line){
    width: 140px;
  }
}
.el-alert--info.is-light{
  background: #ECF5FF;
  color: #1890ff;
}

.data-type{
  >div{
    display: flex;
    align-items: center;
    >span{
      margin-right: 4px;
    }
  }
  >p{
    font-weight: 600;
  }
  :deep(.icon-box){
  >span{
    display: none;
  }
}
}
.system-icon-style{
  :deep(.svg-icon){
    width: 16px;
    height: 16px;
  }
}
  </style>
  
