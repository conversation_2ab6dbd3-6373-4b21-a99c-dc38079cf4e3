<template>
  <div class="main-content">
    <tab-pane
      :option="tabOption"
      @handleClick="handleClick"
    />
    <MemoryTest v-if="tabName=='1'" />
    <HistoryRecord v-else-if="tabName=='2'" />
  </div>
</template>
<script setup>
import TabPane from '@/components/common/TabPane.vue';
import MemoryTest from './MemoryTest/index.vue';
import HistoryRecord from './HistoryRecord/index.vue';

const tabName = ref('1');
const tabOption = [
  {
    label: '最近一次检测',
    name: '1'
  }
  // {
  //   label: '检测历史记录',
  //   name: '2'
  // }
];

function handleClick(name) { // tab切换
  tabName.value = name;
}
</script>
