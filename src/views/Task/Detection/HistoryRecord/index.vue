<template>
  <div>
    <zr-row
      :gutter="24"
      justify="space-between"
      style="white-space: nowrap"
    >
      <zr-col :span="6">
        <zr-button
          type="danger"
          size="default"
          plain
          :disabled="deleteList.length<=0"
          @click="batchDeleteClick"
        >批量删除</zr-button>
      </zr-col>
      <zr-col
        :span="18"
        style="text-align: right;"
      >
        <zr-form :inline="true">
          <zr-form-item>
            <zr-date-picker
              v-model="timeArr"
              type="datetimerange"
              :shortcuts="shortcuts"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              @change="dateChange"
            />
          </zr-form-item>
          <zr-form-item>
            <zr-input
              v-model="searchForm.keyword"
              placeholder="镜像名称"
              size="default"
              @keydown.enter.prevent="searchClick"
            >
              <template #append>
                <zr-button
                  @click="searchClick"
                >
                  <icon name="zr-search" />
                </zr-button>
              </template>
            </zr-input>
          </zr-form-item>
        </zr-form>
      </zr-col>
    </zr-row>
    <zr-table
      ref="tableRef"
      v-loading="loading"
      :data="tableData"
      row-key="image_id"
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <zr-table-column
        type="expand"
        width="1"
      >
        <template #default="scope">
          <div class="detail-msg">
            <zr-card>
              <zr-table
                :data="scope.row.timelineTableData"
                empty-text="暂无数据"
                style="margin-top:0px;"
              >
                <zr-table-column
                  label="关联主机"
                  prop="host_name"
                >
                  <template #default="props">
                    {{ props.row.host_name || '-' }}
                  </template>
                </zr-table-column>
                <zr-table-column
                  label="所属任务"
                  prop="task_name"
                >
                  <template #default="props">
                    {{ props.row.task_name || '-' }}
                  </template>
                </zr-table-column>
                <zr-table-column
                  label="所属用户"
                  prop="user_name"
                >
                  <template #default="props">
                    {{ props.row.user_name || '-' }}
                  </template>
                </zr-table-column>
                <zr-table-column
                  label="检测状态"
                  prop="status"
                >
                  <template #default="props">
                    <div style="display: flex;align-items: center;">
                      <Icon
                        :name="progressOptionIcon[props.row.status]"
                        :size="15"
                        :color="customColorMethod(props.row.status)"
                      />
                      <p style="padding-left:7px">{{ progressOptionList[props.row.status] }}</p>
                    </div>
                    <div
                      class="progress_style"
                    >
                      <zr-progress
                        :percentage="validatePercentage(props.row.progress)"
                        :color="customColorMethod(props.row.status)"
                      />
                    </div>
                  </template>
                </zr-table-column>
                <zr-table-column
                  label="检测开始时间"
                  prop="start_time"
                  width="180"
                >
                  <template #default="props">
                    <div v-if="props.row.start_time_ago || props.row.start_time">
                      <zr-tag v-if="props.row.start_time_ago">{{ props.row.start_time_ago }}</zr-tag>
                      <p>{{ timeFormat(props.row.start_time) || '-' }}</p>
                    </div>
                    <div v-else>-</div>
                  </template>
                </zr-table-column>
                <zr-table-column
                  label="检测完成时间"
                  prop="completed_time"
                  width="180"
                >
                  <template #default="props">
                    <div v-if="props.row.completed_time || props.row.completed_time_ago">
                      <zr-tag v-if="props.row.completed_time_ago">{{ props.row.completed_time_ago }}</zr-tag>
                      <p>{{ timeFormat(props.row.completed_time) || '-' }}</p>
                    </div>
                    <div v-else>-</div>
                  </template>
                </zr-table-column>
                <zr-table-column
                  label="操作"
                  width="220px"
                >
                  <template #default="props">
                    <zr-button
                      type="primary"
                      link
                      :disabled="props.row.status !=='success'"
                      @click="viewReportClick(props.row)"
                    >查看报告</zr-button>
                    <zr-button
                      type="primary"
                      link
                      :disabled="props.row.status !== 'error' && props.row.status !== 'detect_error'"
                      @click="viewLogClick(props.row)"
                    >查看日志</zr-button>
                    <zr-button
                      type="primary"
                      link
                      @click="deleteTimeLineData(scope.row,props.row)"
                    >删除</zr-button>
                  </template>
                </zr-table-column>
              </zr-table>
              <div
                v-if="scope.row.timelineTableData&&scope.row.timelineTableData.length>4"
                class="action"
              >
                <span
                  style="cursor:pointer;color:#1890ff;"
                  @click="openAll(scope.row)"
                >
                  {{ scope.row.unfold ? '收起' : '展开全部' }}
                  <zr-icon
                    :name="scope.row.unfold ? 'ArrowUp' : 'ArrowDown'"
                    color="#1890ff"
                    size="18"
                  />
                </span>
              </div>
            </zr-card>
          </div>
        </template>
      </zr-table-column>
      <zr-table-column
        type="selection"
        width="30"
      />
      <zr-table-column
        label="数据类型/镜像名称"
        prop="image_name"
      />
      <zr-table-column
        label="校验和（Etag）"
        prop="etag"
      />
      <zr-table-column
        label="检测次数"
        prop="num"
        width="120px"
      >
        <template #default="scope">
          <span
            class="col-history"
            @click="timeLineClick(scope.row,true)"
          >{{ scope.row.num }}</span>
        </template>
      </zr-table-column>
      <zr-table-column
        label="操作系统类型"
        prop="os"
        width="120"
        show-overflow-tooltip
      >
        <template #default="scope">
          <span
            v-if="scope.row.os"
            :class="scope.row.os=='linux' ?'':'system-icon-style'"
          >
            <SystemIcon :name="scope.row.os" />
          </span>
          <span v-else>-</span>
        </template>
      </zr-table-column>

      <zr-table-column
        label="最近一次检测时间"
        prop="last_check_time"
        width="160"
      >
        <template #default="scope">
          <div v-if="scope.row.last_check_time_ago || scope.row.last_check_time">
            <zr-tag v-if="scope.row.last_check_time_ago">{{ scope.row.last_check_time_ago }}</zr-tag>
            <p>{{ timeFormat(scope.row.last_check_time) }}</p>
          </div>
          <div v-else>-</div>
        </template>
      </zr-table-column>
      <zr-table-column
        label="首次检测时间"
        prop="first_check_time"
        width="160"
      >
        <template #default="scope">
          <div v-if="scope.row.first_check_time_ago || scope.row.first_check_time">
            <zr-tag v-if="scope.row.first_check_time_ago">{{ scope.row.first_check_time_ago }}</zr-tag>
            <p>{{ timeFormat(scope.row.first_check_time) }}</p>
          </div>
          <div v-else>-</div>
        </template>
      </zr-table-column>
      <zr-table-column
        label="操作"
        prop=""
        width="220px"
      >
        <template #default="scope">
          <zr-button
            type="primary"
            link
            @click="timeLineClick(scope.row,true)"
          >查看历史记录</zr-button>
          <zr-button
            type="primary"
            link
            @click="deleteAll(scope.row)"
          >删除所有记录</zr-button>
        </template>
      </zr-table-column>
    </zr-table>
    <zr-pagination
      :current-page="searchForm.page"
      :page-size="searchForm.page_size"
      :page-sizes="[20, 40, 60, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
    <!-- 查看日志 -->
    <ViewLogDialog
      v-model="dialogVisible"
      :row-id="rowId"
      @cancelLogDialogClick="cancelLogDialogClick"
    />
  </div>
</template>
<script setup>
import { ref } from 'vue';
import { ZrMessage, ZrMessageBox } from 'qianji-ui';
import { detectionRecordsList, deleteDetectionRecords, batchDelete, deleteAllRecords, timelineList } from '@/api/task/history-record';
import { timeFormat } from '@/utils/formatting';
import { formatDate } from '@/utils/index';
import ViewLogDialog from './ViewLogDialog.vue';


const tableRef = ref();
const timeArr = ref([]);
const loading = ref(false);
const shortcuts = [
  {
    text: '最近7天',
    value: () => {
      const end = timeFormat(new Date().setHours(23, 59, 59, 59));
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      const newStart = timeFormat(start.setHours(0, 0, 0, 0));
      return [newStart, end];
    }
  },
  {
    text: '最近30天',
    value: () => {
      const end = timeFormat(new Date().setHours(23, 59, 59, 59));
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      const newStart = timeFormat(start.setHours(0, 0, 0, 0));
      return [newStart, end];
    }
  },
  {
    text: '最近90天',
    value: () => {
      const end = timeFormat(new Date().setHours(23, 59, 59, 59));
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
      const newStart = timeFormat(start.setHours(0, 0, 0, 0));
      return [newStart, end];
    }
  }
];
const searchForm = reactive({
  'start_time': '',
  'end_time': '',
  keyword: '',
  page: 1,
  'page_size': 20
});
const total = ref(0);
const tableData = ref([]);
const deleteList = ref([]);
const dialogVisible = ref(false);
const progressOptionList = ref({ // 进度状态
  waiting: '等待中',
  analyzing: ' 解析中',
  detecting: '检测中',
  'not_analyzed': '未分析',
  success: '成功',
  error: '分析异常',
  'detect_error': '检测异常'
});
const progressOptionIcon = ref({ // 进度状态
  waiting: 'zr-time',
  analyzing: 'zr-history',
  detecting: 'zr-cancel-withdrawal-fill',
  'not_analyzed': 'zr-circle-Warning',
  success: 'zr-circle-seleted',
  error: 'zr-circle-close',
  'detect_error': 'zr-vulnerability-control'
});
const customColorMethod = (status) => { // 进度条颜色
  if (status == 'waiting') {
    return '#FFB968'; // 橙色
  } else if (status == 'not_analyzed') {
    return '#B0B0B0'; // 灰色
  } else if (status == 'analyzing') {
    return '#53A9FF'; // 蓝色
  } else if (status == 'success') {
    return '#8BCF6D'; // 绿色
  } else if (status == 'detecting') {
    return '#3062f1';
    // return '#1094A3';
  }
  return '#D72C00'; // 红色
};
function validatePercentage(progress) {
  if (progress >= 0 && progress <= 100) {
    return progress;
  } else {
    return 0;
  }
}
function dateChange(val) {
  if (val) {
    searchForm['start_time'] = formatDate(val[0]);
    searchForm['end_time'] = formatDate(val[1]);
  } else {
    searchForm['start_time'] = '';
    searchForm['end_time'] = '';
  }
  detectionRecordsListFun();
}

detectionRecordsListFun();
function detectionRecordsListFun() {
  loading.value = true;
  detectionRecordsList(searchForm).then(res => {
    tableData.value = res.data.data.list;
    total.value = res.data.data.total;
  }).finally(() => {
    loading.value = false;
  });
}
function searchClick() { // 搜索
  detectionRecordsListFun();
}

function handleSelectionChange(val) {
  deleteList.value = val.map(item => item.image_id);
}
function batchDeleteClick() { // 批量删除
  ZrMessageBox.confirm('确认删除勾选的所有选项?', '确认操作', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    batchDelete({ 'image_ids': deleteList.value }).then(res => { // 批量删除接口
      if (res.data.code == 0) {
        ZrMessage.success('删除成功');
        detectionRecordsListFun();
      } else {
        ZrMessage.error(res.data.msg);
      }
    });
  });
}

function deleteAll(row) { // 删除所有记录
  ZrMessageBox.confirm('确认删除勾选的所有选项?', '确认操作', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    deleteAllRecords({ 'image_id': row.image_id }).then(res => {
      if (res.data.code == 0) {
        ZrMessage.success('删除成功');
        detectionRecordsListFun();
      } else {
        ZrMessage.error(res.data.msg);
      }
    });
  });
}

function timeLineClick(row, bool) { // 查看时间线
  const timelineListQuery = reactive({
    'image_id': row.image_id,
    'is_limit': !row.unfold
  });
  timelineList(timelineListQuery).then(res => {
    row.timelineTableData = res.data.data.list;
    row.total = res.data.data.total;
    if (bool) {
      tableRef.value.toggleRowExpansion(row);
    }
  });
}

function viewReportClick(row) { // 查看报告
  if (row.status == 'success') {
    // if (row.success_record_id) {
    window.open('/mfp/mirror-angle/' + row.id);
    // }
  } else {
    ZrMessage.error('当前检测项暂无可查看的记录');
  }
}

const rowId = ref('');
function viewLogClick(row) { // 查看日志
  dialogVisible.value = true;
  rowId.value = row.id;
}
function cancelLogDialogClick() { // 关闭查看日志
  dialogVisible.value = false;
}
function deleteTimeLineData(obj, row) { // 单条删除
  ZrMessageBox.confirm('确认删除' + row.host_name + '?', '确认操作', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      deleteDetectionRecords(row.id).then(res => {
        if (res.data.code == 0) {
          ZrMessage.success('删除成功');
          timeLineClick(obj); // 刷新内存列表数据
          obj.num--; // 外层列表数据num减一
        } else {
          ZrMessage.error(res.data.msg);
        }
      });
    });
}

function openAll(row) { // 展开全部、收起
  row.unfold = !row.unfold;
  timeLineClick(row);
}


function handleSizeChange(val) {
  searchForm['page_size'] = val;
  searchForm.page = 1;
  detectionRecordsListFun();
}

function handleCurrentChange(val) {
  searchForm.page = val;
  detectionRecordsListFun();
}
</script>
<style lang="scss" scoped>
.progress_style{
  display: flex;
  align-items: center;
  :deep(.el-progress--line){
    width: 140px;
  }
}

.action{
  margin-top: 10px;
  display: flex;
    justify-content: center;
    align-items: center;
  font-size: 16px;
}
:deep(.el-table__expand-icon>.el-icon){
  display: none;
}
</style>
