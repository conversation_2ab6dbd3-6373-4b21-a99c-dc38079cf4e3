<template>
  <!-- 数据包上传 -->
  <div class="main-content">
    <div class="data-packet">
      <zr-card class="box-card">
        <zr-tabs
          v-model="activeName"
          class="zr-tabs"
        >
          <zr-tab-pane
            label="内存镜像上传"
            name="1"
          />
        </zr-tabs>
        <div class="upload-save">
          <div />
          <div>
            <span>上传成功后自动分析</span>
            <zr-switch v-model="form.is_auto_analyse" />
          </div>
        </div>
        <zr-form
          ref="ruleForm"
          :model="form"
          :rules="rules"
          label-width="148px"
        >
          <zr-form-item label="操作系统类型">
            <zr-radio-group
              v-model="form.sys_type"
              @change="typeChange"
            >
              <zr-radio-button label="Windows">Windows</zr-radio-button>
              <zr-radio-button label="Linux">Linux</zr-radio-button>
            </zr-radio-group>
          </zr-form-item>
          <zr-form-item
            label="选择所属任务"
            prop="assignment_id"
          >
            <zr-select
              v-model="form.assignment_id"
              style="width: 100%;"
              filterable
            >
              <zr-option
                v-for="item in tasksOption"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </zr-select>
          </zr-form-item>
          <zr-form-item
            label=""
            prop="symbol_id"
          >
            <template #label>
              <div style="display: flex;align-items: center;">
                系统内核符号表
                <zr-tooltip
                  class="box-item"
                  effect="dark"
                  placement="top-start"
                > <template #content> 请选择与内存镜像的采集环境相匹配的符号表。</template>
                  <zr-icon
                    style="margin-left: 4px;"
                    name="InfoFilled"
                    size="15"
                  />
                </zr-tooltip>
              </div>
            </template>
            <zr-select
              v-model="form.symbol_id"
              style="width: 100%;"
              filterable
              clearable
            >
              <zr-option
                label="内置符号表"
                value=""
              />
              <zr-option
                v-for="item in symbolsOption"
                :key="item.Id"
                :label="item.Name"
                :value="item.Id"
              />
            </zr-select>
          </zr-form-item>
          <zr-form-item
            label=""
            prop="volatility_version"
          >
            <template #label>
              <div style="display: flex;align-items: center;">
                内存分析引擎
                <zr-tooltip
                  class="box-item"
                  effect="dark"
                  placement="top-start"
                > <template #content> V3：在V2引擎的基础上，可兼容更高版本的操作系统<br>V2：适用于 Windows 10.0.19041 和 Linux Kernel 4.4 以及更早版本的操作系统</template>
                  <zr-icon
                    style="margin-left: 4px;"
                    name="InfoFilled"
                    size="15"
                  />
                </zr-tooltip>
              </div>
            </template>
            <zr-select
              v-model="form.volatility_version"
              style="width: 100%;"
              filterable
              clearable
            >
              <zr-option
                label="V2"
                value="V2"
              />
              <zr-option
                label="V3"
                value="V3"
              />
            </zr-select>
          </zr-form-item>
          <zr-form-item
            label="分析插件"
            prop="plugin_ids"
          >
            <zr-select
              v-model="form.plugin_ids"
              style="width: 100%;"
              :options="pluginsOption"
              multiple
              clearable
              collapse-tags
              collapse-tags-tooltip
            />
          </zr-form-item>
          <!-- <zr-form-item label="描述">
            <zr-input
              v-model="form.description"
              maxlength="500"
              show-word-limit
              type="textarea"
            />
          </zr-form-item> -->
          <zr-form-item label="上传文件">
            <zr-upload
              ref="upload"
              action="#"
              drag
              multiple
              :auto-upload="false"
              :limit="5"
              :on-change="uploadChange"
              style="width:100%"
              :on-exceed="handleExceed"
              :on-remove="handleRemove"
            >
              <template #trigger>
                <Icon
                  name="zr-cloud-upload-fill-b"
                  class="el-icon--upload"
                />
                <div class="el-upload__text">将内存镜像文件拖到此处，或<em>点击上传</em></div>
              </template>
              <!-- <template #tip>
                <div
                  class="el-upload__tip"
                  style="line-height: 25px;"
                >
                  <span style="display:flex; align-items: center;">
                    <zr-icon
                      name="InfoFilled"
                      size="15"
                      style="margin-right:2px"
                    />
                    1、支持内存镜像文件类型包括</span>raw、.mem、.dmp、.img、lime、.bin,dump、.zip <br>
                  2、针对大于2GB的vmem文件，需将对应的vmss文件一同打包成zip包上传，并且文件名和vmem文件一致 <br>
                  3、zip包文件名应与内存镜像文件名（含后缀）保持一致（如win7.bin压缩成zip包的文件名应为win7.bin.zip）
                  <br>
                  <Icon
                    name="zr-circle-Warning-fill"
                    color="#ff9900"
                    size="14"
                  /> 开始上传后，可以点击其他菜单，不要刷新或者关闭当前页面，否则上传将无法继续进行
                </div>
              </template> -->
            </zr-upload>
          </zr-form-item>
          <div
            class="upload-msg"
          >
            1、支持内存镜像文件类型包括 raw、.vmem、.dmp、.img、lime、.bin、dump、.zip <br>
            2、针对大于2GB的vmem文件，需将对应的vmss文件一同打包成zip包上传，并且文件名和vmem文件一致 <br>
            3、zip包文件名应与内存镜像文件名（含后缀）保持一致（如win7.bin压缩成zip包的文件名应为win7.bin.zip）
            <br>
            <Icon
              name="zr-circle-Warning-fill"
              color="#ff9900"
              size="14"
            /> 开始上传后，可以点击其他菜单，不要刷新或者关闭当前页面，否则上传将无法继续进行
          </div>
          <zr-form-item>
            <zr-tooltip
              class="box-item"
              effect="dark"
              :content="authorizedStatus=='Unauthorized'?'系统未授权':''"
              :disabled="authorizedStatus!=='Unauthorized'"
              placement="top"
            >
              <zr-button
                type="primary"
                style="width:100%"
                :loading="loading"
                :disabled="authorizedStatus=='Unauthorized'"
                @click="uploadClick(ruleForm)"
              >{{ loading?'上传中':'上 传' }}</zr-button>
            </zr-tooltip>
          </zr-form-item>
        </zr-form>
        <!-- <zr-progress
          v-if="progressBool"
          :text-inside="true"
          :percentage="percentCompleted"
          :stroke-width="20"
        /> -->
      </zr-card>
    </div>
  </div>
</template>
<script setup>
import { ref, reactive, onMounted } from 'vue';
import axios from 'axios';
import { useRouter } from 'vue-router';
import { tasksOptionApi, uploadApi, uploadExistApi, pluginsOptionApi, uploadApiNew, packagesApiNew, uploadExistApiNew } from '@/api/task/dataPacket';
import { getAuthMsg } from '@/api/login/index';
import { symbolsList } from '@/api/rule/symbol';
import { ZrMessage } from 'qianji-ui';
import { genFileId } from 'element-plus';
import { useStore } from '@/store/store';
import { storeToRefs } from 'pinia';
const store = useStore();
const { uploadProgressList } = storeToRefs(store);
const router = useRouter();
const CancelToken = axios.CancelToken;
const activeName = ref('1');
const form = reactive({
  'sys_type': 'Windows',
  'is_auto_analyse': true,
  'symbol_id': '',
  'volatility_version': 'V3',
  'plugin_ids': []
});
const pluginsOption = ref([]);
const tasksOption = ref([]);
const upload = ref();
const fileList = ref([]);
// const progressBool = ref(false);
const percentCompleted = ref(0);
const ruleForm = ref();
const loading = ref(false);
const authorizedStatus = ref('');
const symbolsOption = ref([]);
const rules = reactive({
  'assignment_id': [
    {
      required: true,
      message: '请选择所属任务',
      trigger: 'change'
    }
  ]
});

const creatFormattedTime = ref('');
const endFormattedTime = ref('');
const uploadApiNewData = ref({}); // upload接口返回的数据
const packageError = ref(false); // 用来判断packages接口是否成功，如果不成功则长度不继续计算

onMounted(() => {
  tasksOptionFun();
  symbolsListFun();
  pluginsOptionFun('Windows');
  var obj = {
    'business_type': 'mfp'
  };
  getAuthMsg(obj).then(res => {
    authorizedStatus.value = res.data.data.product_authorized_status;
  });
});
function symbolsListFun() {
  symbolsList({ os: form['sys_type'], 'is_built_in': 'custom', 'page_index': 1, 'page_size': 999 }).then(res => {
    symbolsOption.value = res.data.data.list;
  });
}
function tasksOptionFun() {
  tasksOptionApi({ page: 1, 'page_size': 999 }).then(res => {
    tasksOption.value = res.data.data.list;
    form['assignment_id'] = 'Default-task';
  });
}
function pluginsOptionFun(type) {
  pluginsOptionApi({ os: type == 'Windows' ? 'windows' : 'linux' }).then(res => {
    pluginsOption.value = res.data.data.list;
  });
}
function typeChange(type) {
  symbolsListFun();
  pluginsOptionFun(type);
  form['symbol_id'] = '';
  form['volatility_version'] = 'V3';
  form['plugin_ids'] = [];
}
function handleRemove(file, files) {
  fileList.value = files;
}

// ----------文件分块上传到uploadApiNew接口，然后调用packagesApiNew接口保存数据，最后调用uploadExistApiNew接口启动检测任务
const uploadClick = async(formEl) => {
  if (!formEl) return;
  await formEl.validate((valid, fields) => {
    if (valid) {
      if (fileList.value.length == 0) {
        ZrMessage.error('请上传文件');
        return;
      }
      loading.value = true;

      creatFormattedTime.value = '';
      const now = new Date();
      creatFormattedTime.value = now.toISOString().replace('T', ' ').slice(0, 19);

      // 顺序处理每个文件
      processFilesSequentially();
    }
  });
};

// 顺序处理文件上传
const processFilesSequentially = async () => {
  for (let fileIndex = 0; fileIndex < fileList.value.length; fileIndex++) {
    const item = fileList.value[fileIndex];
    console.log(item, 'item单个文件信息');

    try {
      await uploadSingleFile(item);
    } catch (error) {
      console.error('文件上传失败:', error);
      ZrMessage.error(`文件 ${item.name} 上传失败`);
      loading.value = false;
      return;
    }
  }
};

// 上传单个文件
const uploadSingleFile = async (item) => {
  const size = item.size;
  const psize = 500 * 1024 * 1024; // 每片为500兆
  const allCount = Math.ceil(size / psize);

  // 初始化该文件的进度对象
  const fileProgress = {
    filename: item.name,
    id: '',
    totalChunks: allCount, // 总分片数
    uploadedChunks: 0, // 已上传片数
    percentCompleted: 0, // 进度
    chunks: Array(allCount).fill(0), // 记录每个分片的上传进度(0-100)
    abortController: new AbortController(), // 用于停止上传
    status: 'uploading' // 上传状态
  };

  // 添加到进度列表
  uploadProgressList.value.unshift(fileProgress);

  // 显示上传进度窗口
  var e = document.getElementById('upload-progress');
  if (e.style.visibility === 'hidden') {
    e.style.visibility = 'visible';
  }

  // 顺序上传每个分片
  for (let i = 0; i < allCount; i++) {
    // 检查是否被停止
    if (fileProgress.status === 'cancelled') {
      throw new Error('上传已被取消');
    }

    try {
      await uploadSingleChunk(item, i, allCount, fileProgress);
    } catch (error) {
      fileProgress.status = 'failed';
      throw error;
    }
  }

  // 所有分片上传完成
  fileProgress.status = 'completed';
  endFormattedTime.value = '';
  const finishTime = new Date();
  endFormattedTime.value = finishTime.toISOString().replace('T', ' ').slice(0, 19);

  // 调用后续接口
  await packageFun();
};

// 上传单个分片
const uploadSingleChunk = async (item, chunkIndex, totalChunks, fileProgress) => {
  const upForm = new FormData();
  upForm.append('file', item.raw);
  upForm.append('file_name', item.name);
  upForm.append('file_id', item.uid);
  upForm.append('chunk_index', chunkIndex + 1);
  upForm.append('chunk_end', (chunkIndex === totalChunks - 1).toString());
  upForm.append('total_filesize', item.size);

  try {
    const res = await uploadApiNew(upForm, fileProgress.abortController);
    console.log(res, `上传接口 - 分片 ${chunkIndex + 1}/${totalChunks}`);

    if (res.data.code == 0) {
      uploadApiNewData.value = res.data.data;

      // 标记该分片为已完成(100%)
      fileProgress.chunks[chunkIndex] = 100;
      fileProgress.uploadedChunks++;
      fileProgress.id = uploadApiNewData.value.file_id;

      // 更新整体进度
      if (!packageError.value) {
        fileProgress.percentCompleted = Math.round((fileProgress.uploadedChunks / totalChunks) * 100);

        // 检查停止状态
        const currentProgress = uploadProgressList.value.find(row => row.filename === fileProgress.filename);
        if (currentProgress && currentProgress.stop) {
          fileProgress.status = 'cancelled';
          fileProgress.abortController.abort();
          throw new Error('上传已被用户取消');
        }

        // 更新进度显示
        updateProgressDisplay(fileProgress);
      }
    } else {
      throw new Error(res.data.msg || '分片上传失败');
    }
  } catch (error) {
    if (error.name === 'AbortError') {
      throw new Error('上传已被取消');
    }
    throw error;
  }
};

// 更新进度显示
const updateProgressDisplay = (fileProgress) => {
  const index = uploadProgressList.value.findIndex(row => row.filename === fileProgress.filename);
  if (index !== -1) {
    uploadProgressList.value[index].percentCompleted = fileProgress.percentCompleted;
    uploadProgressList.value[index].uploadedChunks = fileProgress.uploadedChunks;
    uploadProgressList.value[index].status = fileProgress.status;
    // 触发响应式更新
    uploadProgressList.value = [...uploadProgressList.value];
  }
};

const packageFun = async () => { // 数据包添加
  const packageFormObj = {
    'sys_type': form['sys_type'],
    'minio_bucket': uploadApiNewData.value.bucket,
    'business_type': 'mfp',
    'is_exist': true,
    type: 'mem-image',
    properties: {
      'file_serve_id': uploadApiNewData.value.file_id
    },
    'upload_methed': 'manual',
    'minio_path': uploadApiNewData.value.dest_file,
    'package_create_date': creatFormattedTime.value,
    'upload_finished_at': endFormattedTime.value
  };

  try {
    const res = await packagesApiNew(packageFormObj);
    console.log(res, 'package接口');

    const existForm = {
      'is_auto_analyse': form.is_auto_analyse,
      'assignment_id': form.assignment_id,
      'symbol_id': form.symbol_id
    };

    if (res.data.code === 0) {
      await uploadExistNewFun(res.data.data.id, existForm);
    } else {
      ZrMessage.error(res.data.msg);
      packageError.value = true;
      throw new Error(res.data.msg);
    }
  } catch (error) {
    packageError.value = true;
    throw error;
  }
};

const uploadExistNewFun = async (id, existForm) => { // 数据包状态更改
  try {
    const res = await uploadExistApiNew(id, existForm);
    console.log(res, 'exist接口');

    if (res.data.code === 0) {
      ZrMessage.success('上传成功');
      const path = router.currentRoute.value.path;
      if (path === '/task/data-packet') { // 上传成功之后如果在数据包上传页面就跳转到内存检测页面
        setTimeout(() => {
          router.push({ name: 'TaskDetection' });
        }, 500);
      }
    } else {
      ZrMessage.error(res.data.msg);
      throw new Error(res.data.msg);
    }
  } catch (error) {
    throw error;
  } finally {
    loading.value = false;
  }
};

// const uploadClick = async(formEl) => {
//   if (!formEl) return;
//   await formEl.validate((valid, fields) => {
//     if (valid) {
//       if (fileList.value.length == 0) {
//         ZrMessage.error('请上传文件');
//         return;
//       }
//       // progressBool.value = true;
//       loading.value = true;
//       fileList.value.forEach(item => {
//         form['filename'] = item.name;
//         form['plugin_ids'] = form.plugin_ids && form.plugin_ids.length > 0 ? form.plugin_ids.join(',') : ''; // 传参的时候为字符串类型
//         uploadApi(form).then(res => {
//           form['plugin_ids'] = form.plugin_ids && form.plugin_ids.length > 0 ? form['plugin_ids'].split(',') : []; // 点击上传后，需要将字段值转化为数组用于回显分析插件
//           item.fileUrl = res.data.data;
//           var e = document.getElementById('upload-progress');
//           uploadProgressList.value.unshift(
//             {
//               'filename': item.name,
//               percentCompleted: 0,
//               id: res.data.data.id
//             }
//           );
//           if (e.style.visibility === 'hidden') {
//             e.style.visibility = 'visible'; // 打开上传进度窗口
//           }
//           // const data = new FormData();
//           // data.append('binary', fileList.value.raw);
//           var formObj = {
//             'is_auto_analyse': form.is_auto_analyse,
//             'assignment_id': form.assignment_id,
//             'symbol_id': form.symbol_id,
//             'volatility_version': form.volatility_version,
//             'plugin_ids': form.plugin_ids.join(',') // 传参的时候为字符串
//           };
//           axios({ // 获取到minio服务地址，跳过后端直接上传
//             url: res.data.data.url,
//             method: 'put',
//             data: item.raw,
//             cancelToken: new CancelToken(function executor(c) {
//             // 将取消函数赋值给全局变量cancel
//               item.cancel = c;
//             }),
//             onUploadProgress: progressEvent => { // 获取上传进度
//               if (progressEvent.lengthComputable) {
//                 percentCompleted.value = Math.round((progressEvent.loaded * 100) / progressEvent.total);
//                 uploadProgressList.value.forEach(row => {
//                   if (row.id === item.fileUrl.id) {
//                     if (row.stop) {
//                       item.cancel('停止上传');
//                     }
//                     row.percentCompleted = percentCompleted.value;
//                   }
//                 });
//                 store.uploadProgressFun(uploadProgressList.value);
//               // if (percentCompleted.value > 1) {
//               //   progressBool.value = true;
//               // }
//               } else {
//                 ZrMessage.error('无法计算上传进度');
//               }
//             }
//           }).then(obj => {
//             if (obj.status === 200) {
//               uploadExistFun(res.data.data.id, item.fileUrl, formObj);
//             } else {
//               loading.value = false;
//               // progressBool.value = false;
//             }
//           }).catch(() => {
//             loading.value = false;
//             // progressBool.value = false;
//           });
//         }).catch(() => {
//           loading.value = false;
//           // progressBool.value = false;
//         });
//       });
//     }
//   });
// };
// function uploadExistFun(id, fileUrl, obj) {
//   uploadExistApi(id, obj).then(res => {
//     if (res.data.code === 0) {
//       ZrMessage.success('上传成功');
//       if (percentCompleted.value < 100) {
//         uploadProgressList.value.forEach(item => {
//           if (item.id === fileUrl.id) {
//             item.percentCompleted = 100;
//           }
//           store.uploadProgressFun(uploadProgressList.value);
//         });
//       }
//       var path = router.currentRoute.value.path;
//       if (path === '/task/data-packet') { // 上传成功之后如果在数据包上传页面就跳转到内存检测页面
//         setTimeout(() => {
//           router.push({ name: 'TaskDetection' });
//         }, 500);
//       }
//     } else {
//       ZrMessage.error(res.data.msg);
//     }
//   }).finally(() => {
//     // progressBool.value = false;
//     loading.value = false;
//   });
// }

function uploadChange(file) {
  const fileName = file.name;
  const pos = fileName.lastIndexOf('.');
  const lastName = fileName.substring(pos, fileName.length);
  var name = lastName.toLowerCase();
  var format = ['.raw', '.vmem', '.dmp', '.img', '.lime', '.bin', '.dump', '.zip'];
  if (format.indexOf(name) === -1) {
    ZrMessage.error('文件必须为.raw、.vmem、.dmp、.img、.lime、.bin、.dump、.zip格式');
    upload.value.clearFiles();
    fileList.value = [];
    return;
  }
  fileList.value.push(file);
}
const handleExceed = () => {
  ZrMessage.error('文件限制最多可上传 5 个');
};
</script>
<style lang="scss" scoped>
.data-packet{
  width: 50%;
  margin: 2% auto 0;
  .upload-save{
    display: flex;
    justify-content: space-between;
    >div{
      display: flex;
      align-items: center;
      >span{
        margin-right: 8px;
        font-size: 14px;
      }
    }
  }
}
.el-form{
  width: 60%;
  margin: 0 auto;
}
:deep(.zr-tabs .el-tabs__item){
  font-weight: bold
}
.upload-msg{
  line-height: 25px;
  font-size: 12px;
  color: #606266;
  white-space:nowrap;
  margin-bottom: 20px;
}
</style>
  
