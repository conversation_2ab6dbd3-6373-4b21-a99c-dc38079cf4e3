<template>
  <div>
    <zr-drawer
      v-bind="$attrs"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      @open="draOpen"
    >
      <template #header>
        <h4 style="color: #303133;">
          {{ title }}
        </h4>
      </template>
      <p
        class="titleStyle"
      >任务基本信息</p>
      <zr-form
        ref="formRef"
        label-position="top"
        :model="form"
      >
        <zr-form-item
          label="任务名称"
          prop="name"
          :rules="[
            {
              required: true,
              message: '请输入任务名称',
              trigger: 'blur'
            }]"
        >
          <zr-input
            v-model="form.name"
            maxlength="50"
            show-word-limit
            placeholder="请输入任务名称,1-50位"
          />
        </zr-form-item>
        <zr-form-item
          prop="number"
          label="任务编号"
          :rules="[
            {
              pattern: /^[a-zA-Z0-9-_:\s]+$/,
              message: '只能输入大小写字母、中划线、下划线、冒号、数字',
              trigger: 'blur'
            }]"
        >
          <zr-input
            v-model="form.number"
            maxlength="50"
            show-word-limit
            placeholder="大小写字母、中划线、下划线、冒号、数字、0-50位"
          />
        </zr-form-item>
        <zr-form-item
          prop="description"
          label="任务描述"
        >
          <zr-input
            v-model="form.description"
            type="textarea"
            maxlength="500"
            show-word-limit
            placeholder="请输入任务描述,0-500位"
          />
        </zr-form-item>
        <p
          class="titleStyle"
        >规则模版信息</p>
        <zr-row style="margin-bottom:16px">
          <zr-col>
            <zr-form-item
              prop="yara"
              label="Yara规则模板"
              class="switchStyle"
            >
              <zr-switch
                v-model="form.yara_rule_template"
                @change="form.yara_rule_template?form.template_id='Default-template':form.template_id=''"
              />
            </zr-form-item>
          </zr-col>
          <zr-select
            v-if="form.yara_rule_template"
            v-model="form.template_id"
            filterable
          >
            <zr-option
              v-for="item in yaraOption"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </zr-select>
        </zr-row>
        <!-- <zr-form-item
          prop="switch"
          label="启用威胁情报"
          class="switchStyle"
        >
          <zr-switch
            v-model="form.threat_info_switch"
          />
        </zr-form-item>-->
      </zr-form>
      <template #footer>
        <div style="flex: auto">
          <zr-button
            type="primary"
            size="default"
            @click="submit(formRef)"
          >
            确定
          </zr-button>
          <zr-button
            size="default"
            @click="cancelClick"
          >
            取消
          </zr-button>
        </div>
      </template>
    </zr-drawer>
  </div>
</template>

<script setup>
import { ref, defineEmits, defineProps } from 'vue';
import { tasksDetail, templateOption } from '@/api/task/management';

const emit = defineEmits(['cancelClick', 'saveClick']);
const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  editRowId: {
    type: String,
    default: ''
  }
});
const formRef = ref();
const form = ref({
  name: '',
  number: '',
  description: '',
  'template_id': '',
  'yara_rule_template': false,
  'threat_info_switch': false
});
const yaraOption = ref([]);
function draOpen() {
  templateOption().then(res => {
    yaraOption.value = res.data.data.list;
  });
  if (props.title === '添加任务') {
    form.value = {
      'template_id': 'Default-template',
      'yara_rule_template': true,
      'threat_info_switch': true
    };
  } else {
    tasksDetail(props.editRowId).then(res => {
      form.value = res.data.data;
    });
  }
}
const submit = async(formEl) => {
  if (!formEl) return;
  await formEl.validate((valid) => {
    if (valid) {
      emit('saveClick', form.value);
    } else {
      return false;
    }
  });
};
// function submit() {
//   emit('saveClick', form.value);
// }
function cancelClick() {
  emit('cancelClick');
}
</script>
<style lang="scss" scoped>
  .titleStyle{
  font-size: 14px;
  font-weight: 800;
  margin-bottom: 10px;
}
.switchStyle{
 display: flex;
 align-items: center;
 .el-switch{
  display: inline-block;
}

}
.el-form-item.asterisk-left.switchStyle {
    margin-bottom: 0px;
}
</style>
