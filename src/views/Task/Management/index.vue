<template>
  <!-- 任务管理 -->
  <div class="main-content">
    <zr-row
      :gutter="24"
      justify="space-between"
      style="white-space: nowrap"
    >
      <zr-col :span="8">
        <zr-button
          type="primary"
          size="default"
          @click="openDrawerClick('add')"
        >添加任务</zr-button>
      </zr-col>
      <zr-col
        :span="16"
        style="text-align:right"
      >
        <zr-form :inline="true">
          <zr-form-item>
            <zr-input
              v-model="searchForm.name"
              placeholder="任务名称/任务编号"
              size="default"
              @keydown.enter.prevent="searchClick"
            >
              <template #append>
                <zr-button
                  @click="searchClick"
                >
                  <icon name="zr-search" />
                </zr-button>
              </template>
            </zr-input>
          </zr-form-item>
        </zr-form>

      </zr-col>
    </zr-row>
    <zr-table
      v-loading="loading"
      :data="tableData"
      :default-sort="{ prop: 'created_at', order: 'descending' }"
      empty-text="暂无数据"
      @sort-change="sortChange"
    >
      <zr-table-column
        label="任务名称"
        prop="name"
      />
      <zr-table-column
        label="任务编号"
        prop="number"
      >
        <template #default="scope">
          {{ scope.row.number || '-' }}
        </template>
      </zr-table-column>
      <zr-table-column
        label="所属用户"
        prop="user_name"
      >
        <template #default="scope">
          {{ scope.row.user_name || '-' }}
        </template>
      </zr-table-column>
      <zr-table-column
        label="最近执行时间"
        prop="last_executed"
        width="180"
      >
        <template #default="scope">
          <div v-if="scope.row.last_executed || scope.row.last_executed_ago">
            <zr-tag v-if="scope.row.last_executed_ago">{{ scope.row.last_executed_ago }}</zr-tag>
            <p>{{ timeFormat(scope.row.last_executed) }}</p>
          </div>
          <div v-else>-</div>
        </template>
      </zr-table-column>
      <zr-table-column
        label="首次执行时间"
        prop="first_executed"
        width="180"
      >
        <template #default="scope">
          <div v-if="scope.row.first_executed || scope.row.first_executed_ago">
            <zr-tag v-if="scope.row.first_executed_ago">{{ scope.row.first_executed_ago }}</zr-tag>
            <p>{{ timeFormat(scope.row.first_executed) }}</p>
          </div>
          <div v-else>-</div>
        </template>
      </zr-table-column>
      <zr-table-column
        label="创建时间"
        prop="created_at"
        sortable
        width="180"
      >
        <template #default="scope">
          <div v-if="scope.row.created_at || scope.row.created_at_ago">
            <zr-tag v-if="scope.row.created_at_ago">{{ scope.row.created_at_ago }}</zr-tag>
            <p>{{ timeFormat(scope.row.created_at) }}</p>
          </div>
          <div v-else>-</div>
        </template>
      </zr-table-column>
      <zr-table-column
        label="内存镜像总数"
        prop="property_count"
        width="150"
      >
        <template #default="scope">
          <span
            :class="{'col-history':scope.row.property_count !==0,'col-history-disabled':scope.row.property_count ==0}"
            @click="scope.row.property_count !==0 ? propertyCountClick(scope.row):''"
          >{{ scope.row.property_count }}</span>
        </template>
      </zr-table-column>
      <zr-table-column
        label="已完成/未完成/异常"
        prop="last_status"
        width="200px"
      >
        <template #default="scope">
          <span
            v-if="scope.row.last_status.all>0"
          >
            <StackedProgress
              :total="scope.row.last_status.all"
              :progress="[scope.row.last_status.finished, scope.row.last_status.progress, scope.row.last_status.abnormal]"
            />
          </span>
          <span v-else>-</span>
        </template>
      </zr-table-column>
     
      <zr-table-column
        label="操作"
        prop=""
        width="150px"
      >
        <template #default="scope">
          <div class="buttonStyle">
            <zr-button
              type="primary"
              link
              :disabled="scope.row.property_count ==0"
              @click="taskDeatilClick(scope.row)"
            >任务详情</zr-button>
            <zr-dropdown>
              <span class="zr-dropdown-link">
                更多
                <Icon
                  name="zr-down"
                  class="el-icon--right"
                />
              </span>
              <template #dropdown>
                <zr-dropdown-menu>
                  <zr-dropdown-item
                    command="edit"
                    :disabled="scope.row.is_built_in"
                    @click="openDrawerClick('edit',scope.row)"
                  >编辑</zr-dropdown-item>
                  <zr-dropdown-item
                    command="delete"
                    :disabled="scope.row.is_built_in || scope.row.property_count !==0"
                    @click="deleteClick(scope.row)"
                  >删除</zr-dropdown-item>
                </zr-dropdown-menu>
              </template>
            </zr-dropdown>
          </div>
        </template>
      </zr-table-column>
    </zr-table>
    <zr-pagination
      v-if="total>0"
      :current-page="searchForm.page"
      :page-size="searchForm.page_size"
      :page-sizes="[20, 40, 60, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
    <!-- 抽屉内容 -->
    <drawer
      v-model="openDrawer"
      :title="drawerTitle"
      :edit-row-id="editRowId"
      @saveClick="saveClick"
      @cancelClick="cancelClick"
    />
  </div>
</template>
<script setup>
import { ref } from 'vue';
import drawer from './drawer.vue';
import { ZrMessageBox, ZrMessage } from 'qianji-ui';
import { tasksList, addtasks, edittasks, deletetasks } from '@/api/task/management';
import StackedProgress from '@/components/common/StackedProgress';
import { timeFormat } from '@/utils/formatting';
import { useRouter } from 'vue-router';

const router = useRouter();
const editRowId = ref('');
const drawerTitle = ref('');
const openDrawer = ref(false);
const searchForm = ref({
  name: '',
  sort: '-created_at',
  page: 1,
  'page_size': 20
});
const loading = ref(false);
const total = ref(0);
const tableData = ref([]);

tasksListFun();
function tasksListFun() {
  loading.value = true;
  tasksList(searchForm.value).then(res => {
    tableData.value = res.data.data.list || [];
    total.value = res.data.data.count;
  }).finally(() => {
    loading.value = false;
  });
}
function propertyCountClick(row) { // 内存镜像总数跳转到内存检测页面
  router.push({ name: 'TaskDetection', query: { id: row.id }});
}
function taskDeatilClick(row) { // 任务详情跳转到内存检测页面
  router.push({ name: 'TaskDetection', query: { id: row.id }});
}

function openDrawerClick(type, row) { // 添加任务
  drawerTitle.value = type === 'add' ? '添加任务' : type === 'edit' ? '编辑任务' : '查看详情';
  openDrawer.value = true;
  if (type !== 'add') {
    editRowId.value = row.id;
  }
}

function searchClick() { // 搜索
  tasksListFun();
}

function deleteClick(row) { // 删除
  ZrMessageBox.confirm('确认删除' + row.name + '任务?', '确认操作', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      deletetasks(row.id).then(res => { // 删除接口
        ZrMessage.success(res.data.data);
        tasksListFun();
      });
    }).catch(() => {
    
    });
}

function handleSizeChange(val) {
  searchForm.value['page_size'] = val;
  searchForm.value.page = 1;
  tasksListFun();
}
function handleCurrentChange(val) {
  searchForm.value.page = val;
  tasksListFun();
}
function saveClick(form) {
  if (drawerTitle.value == '添加任务') {
    addtasks(form).then(res => { // 添加接口
      if (res.data.code == 0) {
        ZrMessage.success(res.data.data);
        openDrawer.value = false;
        tasksListFun();
      }
    });
  } else {
    edittasks(editRowId.value, form).then(res => { // 编辑接口
      if (res.data.code == 0) {
        ZrMessage.success(res.data.data);
        openDrawer.value = false;
        tasksListFun();
      }
    });
  }
}
function cancelClick() {
  openDrawer.value = false;
}
function sortChange(data) { // 更新时间 排序
  const { prop, order } = data;
  if (order === 'ascending') {
    searchForm.value.sort = '+' + prop;
  } else {
    searchForm.value.sort = '-' + prop;
  }
  tasksListFun();
}

</script>

<style lang="scss" scoped>
.zr-dropdown-link {
  cursor: pointer;
  color: var(--el-color-primary);
  display: flex;
  align-items: center;
  margin-left: 10px
}
.buttonStyle{
  display:flex;
  align-items:center ;
}
.col-history {
  background: #53a9ff;
  color: #fff;
  padding: 3px 8px;
  border-radius: 3px;
  cursor: pointer;
}
.col-history-disabled {
  background: #9FCEFF;
  color: #fff;
  padding: 3px 8px;
  border-radius: 3px;
}
</style>

  
