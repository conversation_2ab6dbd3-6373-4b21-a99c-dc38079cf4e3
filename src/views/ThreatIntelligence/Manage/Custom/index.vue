<template>
  <!-- 自定义威胁情报 -->
  <div>
    <zr-row :gutter="24">
      <zr-col :span="8">
        <zr-button
          type="primary"
          @click="importClick"
        >导入情报</zr-button>
        <zr-button
          type="danger"
          plain
          :disabled="ids.length===0"
          @click="batchDelClick"
        >批量删除</zr-button>
        <zr-button
          type="primary"
          plain
          @click="downloadClick"
        >下载模板文件</zr-button>
        <zr-button
          type="primary"
          link
          @click="dialogVisible=true"
        >支持的情报类型</zr-button>
      </zr-col>
      <zr-col
        :span="16"
        style="text-align: right;"
      >
        <zr-form
          :model="searchForm"
          inline
        >
          <zr-form-item>
            <zr-select
              v-model="searchForm.type"
              @change="listFun"
            >
              <zr-option
                v-for="item in typeOption"
                :key="item.value"
                :label="item.label+' ('+item.num+')'"
                :value="item.value"
              />
            </zr-select>
          </zr-form-item>
          <zr-form-item style="width:300px">
            <zr-input
              v-model="searchForm.name"
              placeholder="IOC指标/所属组织/标签"
              @keyup.enter="listFun"
            >
              <template #append>
                <zr-button @click="listFun">
                  <zr-icon
                    name="Search"
                  />
                </zr-button>
              </template>
            </zr-input>
          </zr-form-item>
        </zr-form>
      </zr-col>
    </zr-row>
    <zr-table
      ref="tabelList"
      v-loading="loading"
      :data="tableData"
      empty-text="暂无数据"
      @selection-change="handleSelectionChange"
    >
      <zr-table-column
        type="selection"
        width="30"
      />
      <zr-table-column
        prop="type"
        label="类型"
        width="100"
        show-overflow-tooltip
      />
      <zr-table-column
        prop="subtype"
        label="子类"
        width="100"
        show-overflow-tooltip
      />
      <zr-table-column
        prop="indicator"
        label="IOC指标"
        show-overflow-tooltip
      />
      <zr-table-column
        prop="actors"
        label="关联组织"
        width="100"
        show-overflow-tooltip
      />
      <zr-table-column
        prop="tags"
        label="标签"
      >
        <template #default="scope">
          <zr-scrollbar
            v-if="scope.row.tags"
            :wrap-class="['scrollbar-wrapper', 'wrapper-custom']"
            :wrap-style="[{ 'max-height': '50px', height: '50px'}]"
          >
            <zr-tag
              v-for="item in scope.row.tags.split(';')"
              :key="item"
            >{{ item }}</zr-tag>
          </zr-scrollbar>
        </template>
      </zr-table-column>
      <zr-table-column
        prop="mitre_attack"
        label="ATT&CK映射"
      >
        <template #default="scope">
          <zr-scrollbar
            v-if="scope.row.mitre_attack"
            :wrap-class="['scrollbar-wrapper', 'wrapper-custom']"
            :wrap-style="[{ 'max-height': '50px', height: '50px'}]"
          >
            <zr-tag
              v-for="item in scope.row.mitre_attack.split(';')"
              :key="item"
            >{{ item }}</zr-tag>
          </zr-scrollbar>
        </template>
      </zr-table-column>
      <zr-table-column
        prop="kill_chain"
        label="KillChain映射"
      >
        <template #default="scope">
          <zr-scrollbar
            v-if="scope.row.kill_chain"
            :wrap-class="['scrollbar-wrapper', 'wrapper-custom']"
            :wrap-style="[{ 'max-height': '50px', height: '50px'}]"
          >
            <zr-tag
              v-for="item in scope.row.kill_chain.split(';')"
              :key="item"
            >{{ item }}</zr-tag>
          </zr-scrollbar>
        </template>
      </zr-table-column>
      <zr-table-column
        prop="created_at"
        label="首次发布时间"
        width="120"
        show-overflow-tooltip
      >
        <template #default="scope">
          <zr-tag v-if="scope.row.published_time_ago">{{ scope.row.published_time_ago }}</zr-tag>
          <p>{{ scope.row.published_date }}</p>
        </template>
      </zr-table-column>
      <zr-table-column
        prop="updated_at"
        label="更新时间"
        width="200"
        show-overflow-tooltip
      >
        <template #default="scope">
          <zr-tag v-if="scope.row.updated_time_ago">{{ scope.row.updated_time_ago }}</zr-tag>
          <p>{{ timeFormat(scope.row.updated_at) }}</p>
        </template>
      </zr-table-column>
    </zr-table>
    <zr-pagination
      v-if="total>0"
      :current-page="searchForm.page"
      :page-size="searchForm.page_size"
      :page-sizes="[20, 40, 60, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
    <zr-drawer
      v-model="drawerBool"
      title="导入规则"
      :close-on-click-modal="false"
      destroy-on-close
      @close="closeClick"
    >
      <template #default>
        <div>
          <zr-upload
            ref="upload"
            action="#"
            :drag="true"
            multiple
            :auto-upload="false"
            :limit="1"
            :on-change="uploadChange"
            :on-exceed="handleExceed"
          >
            <template #trigger>
              <Icon
                name="zr-cloud-upload-fill-b"
                class="el-icon--upload"
              />
              <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em>
                <p class="upload-msg">请下载模板文件，将威胁情报信息填入后上传</p>
              </div>
            </template>
            <!-- <template #tip>
              <div class="el-upload__tip">支持jpg/png格式图片，仅限500KB以内</div>
            </template> -->
          </zr-upload>
          <div
            v-if="importSuccess&&importMsg"
            class="success-msg"
          >
            <p v-if="importMsg.failed_list&&importMsg.failed_list.length>0">规则 “ {{ importMsg.failed_list.join('，') }} ” 上传失败；</p>
            <p>上传成功规则 {{ importMsg.success_count }} 条；</p>
          </div>

        </div>
      </template>
      <template #footer>
        <div style="flex: auto">
          <zr-button
            v-if="!importSuccess"
            type="primary"
            :loading="btnLoading"
            @click="confirmClick"
          >确 定</zr-button>
          <zr-button
            v-if="!importSuccess"
            @click="drawerBool=false"
          >取 消</zr-button>
          <zr-button
            v-if="importSuccess"
            @click="drawerBool=false"
          >关 闭</zr-button>
        </div>
      </template>
    </zr-drawer>
    <!-- 情报类型窗口 -->
    <zr-dialog
      v-model="dialogVisible"
      title="情报类型"
      :width="480"
    >
      <div class="explain">
        <table>
          <tr>
            <td
              v-for="(item,index) in intelligenceOption.label"
              :key="index"
              class="title"
              rowspan="1"
            >{{ item }}</td>
          </tr>
          <tr
            v-for="item in intelligenceOption.data"
            :key="item.subtype"
          >
            <td
              v-if="item.type||item.type==''"
              class="title"
              :rowspan="item.type==='Hash'?'3':item.type==='IP'?'2':'1'"
            >{{ item.type }}</td>
            <td>{{ item.subtype }}</td>
          </tr>
        </table>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <zr-button @click="dialogVisible = false">关闭</zr-button>
        </span>
      </template>
    </zr-dialog>
  </div>
</template>
<script setup>
import { ref, reactive, onMounted } from 'vue';
import { ZrMessage, ZrMessageBox } from 'qianji-ui';
import { timeFormat } from '@/utils/formatting';
import { genFileId } from 'element-plus';
import { iocListApi, typeOptions, batchDelete, importCustom } from '@/api/threatIntelligence/manage';
const searchForm = reactive({
  'type': '',
  page: 1,
  'page_size': 20,
  source: 'custom'
});
const total = ref(0);
const tableData = ref([]);
const loading = ref(false);
const ids = ref([]);
const drawerBool = ref(false);
const typeOption = ref([]);
const fileList = ref({});
const upload = ref();
const btnLoading = ref(false);
const importMsg = ref({});
const importSuccess = ref(false);
const dialogVisible = ref(false);
const intelligenceOption = reactive({
  label: ['类型（type）', '子类型（subtype）'],
  data: [
    {
      type: 'Hash',
      subtype: 'MD5'
    },
    {
      subtype: 'SHA1'
    },
    {
      subtype: 'SHA256'
    },
    {
      type: 'IP',
      subtype: 'IPv4'
    },
    {
      subtype: 'IPv6'
    }
  ]
});

onMounted(() => {
  listFun();
});
function listFun() {
  typeOptions({ source: searchForm.source }).then(res => {
    typeOption.value = res.data.data.data || [];
  });
  loading.value = true;
  iocListApi(searchForm).then(res => {
    tableData.value = res.data.data.list || [];
    total.value = res.data.data.count || 0;
  }).finally(() => {
    loading.value = false;
  });
}
function uploadChange(file) {
  const fileName = file.name;
  const pos = fileName.lastIndexOf('.');
  const lastName = fileName.substring(pos, fileName.length);
  var name = lastName.toLowerCase();
  var format = ['.csv'];
  if (format.indexOf(name) === -1) {
    ZrMessage.error('文件必须为.csv格式');
    upload.value.clearFiles();
    return;
  }
  fileList.value = file;
  // importFun();
}
const handleExceed = (files) => {
  upload.value.clearFiles();
  const file = files[0];
  file.uid = genFileId();
  upload.value.handleStart(file);
};
function importFun() {
  btnLoading.value = true;
  // if (!fileList.value.raw) {
  //   ZrMessage.error('请上传文件');
  //   return;
  // }
  const params = new FormData();
  params.append('file', fileList.value.raw);
  params.append('group', 'common');
  params.append('business_type', 'mfp');
  importCustom(params).then(res => {
    if (res.data.code === 0) {
      importMsg.value = res.data.data;
      importSuccess.value = true;
      // ZrMessage.success('上传成功');
    } else {
      ZrMessage.error(res.data.msg);
    }
  }).finally(() => {
    btnLoading.value = false;
  });
}
function closeClick() {
  drawerBool.value = false;
  listFun();
}
function confirmClick() {
  importFun();
}
function importClick() {
  importSuccess.value = false;
  importMsg.value = {};
  fileList.value = {};
  drawerBool.value = true;
}
function downloadClick() {
  var a = document.createElement('a');
  a.href = new URL('@/assets/file/iocLoad.csv', import.meta.url);
  a.download = 'iocLoad.csv';
  a.click();
}
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
}
function batchDelClick() {
  ZrMessageBox.confirm('确认删除所选项?', '提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      batchDelete({ ids: ids.value }).then(res => {
        if (res.data.code === 0) {
          ZrMessage.success('删除成功');
          listFun();
        } else {
          ZrMessage.error(res.data.message);
        }
      });
    });
}
function handleSizeChange(val) {
  searchForm.page = 1;
  searchForm['page_size'] = val;
  listFun();
}
function handleCurrentChange(val) {
  searchForm.page = val;
  listFun();
}
</script>
<style lang="scss" scoped>
.upload-msg{
    font-size: 16px;
    margin-top: 8px;
}
.success-msg{
  margin-top:18px;
  color: #666;
  line-height: 24px;
}
.explain {
      margin: 0px 10px;
      width: calc(100% - 20px);
      text-align: center;
      background-color: white;
      table {
          width: 100%;
          border-right: 1px solid #000;
          border-bottom: 1px solid #000;
          /* 设置边缘间距0 */
          border-spacing: 0;
          /* 用于表格属性, 表示表格的两边框合并为一条 */
          border-collapse: collapse;
          tr{
              td:nth-child(1) {
                  width: 40%;
              }
              td:nth-child(2) {
                  width: 60%;
              }
              .title{
                text-align: center;
              }
          }
          td {
              border-left: 1px solid #000;
              border-top: 1px solid #000;
              text-align: left;
              font-size: 12px;
              font-weight: bold;
              border-right: 1px solid #000;
              padding: 6px 4px;
          }
      }
  }
</style>
