<!-- 情报管理 -->
<template>
  <div class="main-content">
    <TabPane
      :option="tabOption"
      @handleClick="handleClick"
    />
    <BuiltIn v-if="tabNum=='1'" />
    <Custom v-else />
  </div>
</template>
<script setup>
import TabPane from '@/components/common/TabPane.vue';
import Custom from './Custom/index.vue';
import BuiltIn from './BuiltIn/index.vue';

const tabOption = [
  {
    name: '1',
    label: '内置威胁情报'
  },
  {
    name: '2',
    label: '自定义威胁情报'
  }
];
const tabNum = ref('1');

function handleClick(val) {
  tabNum.value = val;
}

</script>
    
  
