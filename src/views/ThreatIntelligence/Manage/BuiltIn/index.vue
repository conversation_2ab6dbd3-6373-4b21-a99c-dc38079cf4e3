<template>
  <!-- 内置威胁情报 -->
  <div>
    <zr-form
      :model="searchForm"
      inline
      style="text-align: right;"
    >
      <zr-form-item>
        <zr-select
          v-model="searchForm.type"
          @change="listFun"
        >
          <zr-option
            v-for="item in typeOption"
            :key="item.value"
            :label="item.label+' ('+item.num+')'"
            :value="item.value"
          />
        </zr-select>
      </zr-form-item>
      <zr-form-item style="width:300px">
        <zr-input
          v-model="searchForm.name"
          placeholder="IOC指标/所属组织/标签"
          @keyup.enter="listFun"
        >
          <template #append>
            <zr-button @click="listFun">
              <zr-icon
                name="Search"
              />
            </zr-button>
          </template>
        </zr-input>
      </zr-form-item>
    </zr-form>
    <zr-table
      ref="tabelList"
      v-loading="loading"
      :data="tableData"
      empty-text="暂无数据"
    >
      <zr-table-column
        prop="type"
        label="类型"
        width="100"
        show-overflow-tooltip
      />
      <zr-table-column
        prop="subtype"
        label="子类"
        width="100"
        show-overflow-tooltip
      />
      <zr-table-column
        prop="indicator"
        label="IOC指标"
        show-overflow-tooltip
      />
      <zr-table-column
        prop="actors"
        label="关联组织"
        width="100"
        show-overflow-tooltip
      />
      <zr-table-column
        prop="tags"
        label="标签"
      >
        <template #default="scope">
          <zr-scrollbar
            v-if="scope.row.tags"
            :wrap-class="['scrollbar-wrapper', 'wrapper-custom']"
            :wrap-style="[{ 'max-height': '50px', height: '50px'}]"
          >
            <zr-tag
              v-for="item in scope.row.tags.split(';')"
              :key="item"
            >{{ item }}</zr-tag>
          </zr-scrollbar>
        </template>
      </zr-table-column>
      <zr-table-column
        prop="mitre_attack"
        label="ATT&CK映射"
      >
        <template #default="scope">
          <zr-scrollbar
            v-if="scope.row.mitre_attack"
            :wrap-class="['scrollbar-wrapper', 'wrapper-custom']"
            :wrap-style="[{ 'max-height': '50px', height: '50px'}]"
          >
            <zr-tag
              v-for="item in scope.row.mitre_attack.split(';')"
              :key="item"
            >{{ item }}</zr-tag>
          </zr-scrollbar>
        </template>
      </zr-table-column>
      <zr-table-column
        prop="kill_chain"
        label="KillChain映射"
      >
        <template #default="scope">
          <zr-scrollbar
            v-if="scope.row.kill_chain"
            :wrap-class="['scrollbar-wrapper', 'wrapper-custom']"
            :wrap-style="[{ 'max-height': '50px', height: '50px'}]"
          >
            <zr-tag
              v-for="item in scope.row.kill_chain.split(';')"
              :key="item"
            >{{ item }}</zr-tag>
          </zr-scrollbar>
        </template>
      </zr-table-column>
      <zr-table-column
        prop="published_date"
        label="首次发布时间"
        width="120"
        show-overflow-tooltip
      >
        <template #default="scope">
          <zr-tag v-if="scope.row.published_time_ago">{{ scope.row.published_time_ago }}</zr-tag>
          <p>{{ scope.row.published_date }}</p>
        </template>
      </zr-table-column>
      <zr-table-column
        prop="created_at"
        label="更新时间"
        width="200"
        show-overflow-tooltip
      >
        <template #default="scope">
          <zr-tag v-if="scope.row.create_time_ago">{{ scope.row.create_time_ago }}</zr-tag>
          <p>{{ timeFormat(scope.row.created_at) }}</p>
        </template>
      </zr-table-column>
    </zr-table>
    <zr-pagination
      v-if="total>0"
      :current-page="searchForm.page"
      :page-size="searchForm.page_size"
      :page-sizes="[20, 40, 60, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>
<script setup>
import { ref, reactive, onMounted } from 'vue';
import { timeFormat } from '@/utils/formatting';
import { iocListApi, typeOptions } from '@/api/threatIntelligence/manage';
const searchForm = reactive({
  'type': '',
  page: 1,
  'page_size': 20,
  source: 'built_in'
});
const total = ref(0);
const tableData = ref([]);
const loading = ref(false);
const typeOption = ref([]);

onMounted(() => {
  typeOptions({ source: searchForm.source }).then(res => {
    typeOption.value = res.data.data.data || [];
  });
  listFun();
});
function listFun() {
  loading.value = true;
  iocListApi(searchForm).then(res => {
    tableData.value = res.data.data.list || [];
    total.value = res.data.data.count || 0;
  }).finally(() => {
    loading.value = false;
  });
}
function handleSizeChange(val) {
  searchForm.page = 1;
  searchForm['page_size'] = val;
  listFun();
}
function handleCurrentChange(val) {
  searchForm.page = val;
  listFun();
}
</script>
    
  
