<template>
  <div id="login">
    <div class="bg" />
    <img
      v-if="sysLogoAddr"
      class="logo"
      :src="sysLogoAddr"
    >
    <div class="register">
      <h2>{{ sysName }}</h2>
      <!-- <h2>睿士·数据安全管控平台</h2> -->
      <!-- <h3>(DC版)</h3> -->
      <zr-form
        label-position="top"
        label-width="100px"
        :model="formLabelAlign"
      >
        <zr-form-item
          label="用户名"
        >
          <zr-input
            v-model="formLabelAlign.username"
            size="large"
            @keyup.enter="loginClick"
          >
            <template #prefix>
              <Icon
                name="zr-user"
                size="20"
                color="#fff"
              />
            </template>
          </zr-input>
        </zr-form-item>
        <zr-form-item label="密码">
          <zr-input
            v-model="formLabelAlign.password"
            type="password"
            size="large"
            @keyup.enter="loginClick"
          >
            <template #prefix>
              <Icon
                name="zr-unlock"
                size="20"
                color="#fff"
              />
            </template>
          </zr-input>
        </zr-form-item>
        <div class="remember">
          <zr-checkbox
            v-model="formLabelAlign.rememberMe"
            label="记住密码"
            size="large"
          />
          <p
            class="browser"
            @click="dialogTableVisible=true"
          >推荐浏览器</p>
        </div>
        <zr-form-item>
          <zr-button
            type="primary"
            size="large"
            @click="loginClick"
            @keyup.enter="loginClick"
          >登录</zr-button>
        </zr-form-item>
      </zr-form>
    </div>
    <zr-dialog
      v-model="dialogTableVisible"
      title="推荐浏览器"
      width="30%"
    >
      <div class="content">
        <div
          v-for="item in browserOption"
          :key="item.name"
          class="browser"
        >
          <img
            :src="item.url"
            alt=""
          >
          <zr-button
            style="width: 75%;"
            @click="downloadClick(item.http)"
          >获取 {{ item.name }}</zr-button>
        </div>
      </div>
    </zr-dialog>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { useCookies } from 'vue3-cookies';
import { loginApi } from '@/api/login/index';
import { acquireConfigs } from '@/api/system/configuration';
import { routes } from "@/router/getRoutes/routeList";
import { useStore } from '@/store/store';
import { storeToRefs } from 'pinia';
const { cookies } = useCookies();
const router = useRouter();
const browserOption = [
  {
    url: require('../../assets/imgs/Chrome.png'),
    name: 'Chrome',
    http: 'https://www.google.cn/chrome/'
  },
  {
    url: require('../../assets/imgs/Edge.png'),
    name: 'Edge',
    http: 'https://www.microsoft.com/zh-cn/edge?r=1&form=MA13FJ'
  },
  {
    url: require('../../assets/imgs/Firefox.png'),
    name: 'Firefox',
    http: 'http://www.firefox.com.cn/download/#product-desktop-release'
  }
];
const dialogTableVisible = ref(false);
const formLabelAlign = ref({
  username: '',
  password: '',
  rememberMe: false,
  uuid: ""
});
const sysName = ref(''); // 系统名称
const sysLogoAddr = ref(''); // logo
const route = reactive([]);
const token = ref('');
route.values = routes;

acquireConfigsFun();
function acquireConfigsFun() { // 获取系统信息
  acquireConfigs().then(res => {
    sysName.value = res.data.data.sys_name;
    localStorage.setItem('sysLocal', JSON.stringify(res.data.data));
    document.getElementById('sys-title').innerHTML = res.data.data.sys_name;
  });
  axios({
    url: window.BASE_URL + '/eos-service/api/v6/system/sys_info/logo/mfp',
    method: 'get',
    responseType: 'blob'
  }).then(({ data }) => {
    if (data.type == 'application/octet-stream') {
      const blob = new Blob([data]); // 返回的文件流数据
      const url = window.URL.createObjectURL(blob); // 将他转化为路径
      sysLogoAddr.value = url;
    } else {
      sysLogoAddr.value = '';
    }
  }
  );
}
function downloadClick(http) { // 打开推荐浏览器下载地址
  window.open(http);
}
function rememberMeFun() { // 获取登陆缓存信息
  if (localStorage.getItem('form')) {
    const obj = JSON.parse(localStorage.getItem('form'));
    if (obj.rememberMe) {
      formLabelAlign.value = { ...obj };
    } else {
      formLabelAlign.value = {
        username: '',
        password: '',
        rememberMe: false,
        uuid: ""
      };
    }
  }
}
rememberMeFun();
const address = ref('');
const loginClick = () => { // 登录
  loginApi(formLabelAlign.value).then(res => {
    address.value = sessionStorage.getItem('defaultActive');
    cookies.set('dsp_token', res.data.data.token);
    localStorage.setItem('form', JSON.stringify(formLabelAlign.value));
    token.value = res.data.data.token;
    const stores = useStore();
    const { routeBool } = storeToRefs(stores);
    routeBool.value = false;
    router.push((address.value && address.value !== '/') ? address.value : '/threat/mirror-angle');
  });
};

</script>
<style lang="scss" scoped>
#login{
    width: 100%;
    height: 100%;
    background: #1890FF;
    .el-dialog{
      .content{
        display: flex;
        justify-content: space-around;
        .browser{
          text-align: center;
          img{
            width: 50px;
            height: 50px;
          }
        }
      }
    }
    .bg{
        width: 100%;
        height: 100%;
        background: url(../../assets/imgs/rectangle.png) no-repeat 100%;
        background-size: cover;
    }
    .logo{
        width: 182px;
        height: 64px;
        position: absolute;
        top: 40px;
        left: 40px;
    }
    .register{
        position: absolute;
        width: 500px;
        right: 10%;
        // right: 13%;
        top: 12%;
        // background: #133A68D9;
        background: rgba(19, 58, 104, .7);
        text-align: center;
        :deep(.el-form-item .el-form-item__label){
          font-size: 16px;
          color: #fff;
        }
        :deep(.el-input--prefix .el-input__inner){
          height: 46px;
        }
        h2{
            font-size: 30px;
            font-weight: 700;
            line-height: 30px;
            color: #FFF;
            margin: 100px 0 16px;
        }
        h3{
            font-size: 24px;
            font-weight: 400;
            line-height: 28px;
            color: #919398;
        }
    }
    .remember{
      display: flex;
      align-items: center;
      justify-content: space-between;
      .browser{
        font-size: 16px;
        line-height: 28px;
        cursor: pointer;
        color: #1890FF;
      }
    }
    .el-form{
      margin: 40px auto 0;
      max-width: 400px;
    }
    .el-form-item{
      margin-bottom:31px;
    }
    .el-form-item--small{
      margin-bottom: 30px;
    }
    .el-button{
      width: 100%;
      height: 48px;
      font-size: 16px;
      margin: 30px 0 82px;
    }
    :deep(.el-checkbox.el-checkbox--large .el-checkbox__label){
      font-size: 16px;
      color: #FFFFFFD9;
    }
    :deep(.el-checkbox__inner){
      background: #133A68;
    }
    :deep(.el-input__inner) {
      -webkit-text-fill-color: #fff; //文字颜色
      caret-color: #fff; //光标颜色
      box-shadow: inset 0 0 0 1000px #133A68 !important; //背景颜色
    }
    :deep(input::-webkit-input-placeholder) {
      -webkit-text-fill-color: #FFFFFF99 !important; //placeholder的文字颜色
      // -webkit-text-fill-color: #5f757f !important; //placeholder的文字颜色
    }
    :deep(.el-input--large .el-input__wrapper) {
      background: #133A68D9;
    }
}
</style>
