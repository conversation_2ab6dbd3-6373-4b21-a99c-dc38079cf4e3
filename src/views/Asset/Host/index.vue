<template>
  <div class="main-content">
    <tab-pane
      :option="tabOption"
      @handleClick="handleClick"
    />
    <OfflineImport v-if="tabName=='1'" />
  </div>
</template>
<script setup>
import TabPane from '@/components/common/TabPane.vue';
import OfflineImport from './OfflineImport/index.vue';

const tabName = ref('1');
const tabOption = [
  {
    label: '离线导入资产',
    name: '1'
  }
];

function handleClick(name) { // tab切换
  tabName.value = name;
}
</script>
