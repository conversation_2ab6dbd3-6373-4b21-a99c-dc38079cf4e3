<template>
  <div>
    <zr-drawer
      v-bind="$attrs"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      @open="draOpen"
    >
      <template #header>
        <h4 style="color: #303133;">
          导入资产
        </h4>
      </template>
      <zr-upload
        action="#"
        drag
        :limit="1"
        :auto-upload="false"
        :headers="headersObj"
        :on-change="handleChange"
      >
        <template #trigger>
          <Icon
            name="zr-cloud-upload-fill-b"
            class="el-icon--upload"
          />
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        </template>
        <template #tip>
          <div class="el-upload__tip">支持csv格式文件</div>
        </template>
      </zr-upload>
      <div
        v-if="importSuccess&&importMsg"
        class="success-msg"
      >
        <p v-if="importMsg.failed_list&&importMsg.failed_list.length>0">规则 “ {{ importMsg.failed_list.join('，') }} ” 上传失败；</p>
        <p v-if="importMsg.success_count">上传成功规则 {{ importMsg.success_count }} 条；</p>
      </div>
      <template #footer>
        <div v-if="importSuccess">
          <zr-button
            type="primary"
            size="default"
            @click="cancelClick"
          >
            完成
          </zr-button>
        </div>
        <div v-else>
          <zr-button
            type="primary"
            size="default"
            @click="submit"
          >
            确定
          </zr-button>
          <zr-button
            size="default"
            @click="cancelClick"
          >
            取消
          </zr-button>
        </div>
      </template>
    </zr-drawer>
  </div>
</template>
<script setup>
import { ref } from 'vue';
import { importHost } from '@/api/asset/host/offlineImport';
import { useCookies } from 'vue3-cookies';
import { ZrMessage } from 'qianji-ui';

const { cookies } = useCookies();
const emit = defineEmits(['cancelImportClick', 'saveImportClick']);
const headersObj = { // 上传文件时传token
  'token': cookies.get('dsp_token')
};
const getFile = ref({ });
const importSuccess = ref(false);
const importMsg = ref({});

function draOpen() {
  getFile.value = {};
  importMsg.value = {};
  importSuccess.value = false;
}

const handleChange = (response, uploadFile, uploadFiles) => { // 上传文件状态改变时
  const fileType = ["text/csv"]; // csv类型
  const zipFileType = fileType.includes(response.raw.type);
  if (!zipFileType) {
    ZrMessage.error('只支持csv格式文件');
    getFile.value = {};
    return false;
  }
  getFile.value = response.raw; // 取文件信息
};

function submit() {
  if (Object.keys(getFile.value).length == 0) {
    ZrMessage.error('上传文件不能为空');
    return;
  }
  const data = new FormData();
  data.append('business_type', 'mfp');
  data.append('file', getFile.value);
  importHost(data).then(res => {
    importMsg.value = res.data.data;
    importSuccess.value = true;
    emit('saveImportClick');
  });
}

function cancelClick() {
  emit('cancelImportClick');
}
</script>
<style lang="scss" scoped>
.success-msg{
  margin-top:18px;
  color: #666;
  line-height: 24px;
}
</style>
