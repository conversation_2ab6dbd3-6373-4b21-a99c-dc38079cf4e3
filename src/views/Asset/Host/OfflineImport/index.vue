<template>
  <div class="">
    <zr-row
      :gutter="24"
      justify="space-between"
      style="white-space: nowrap"
    >
      <zr-col :span="10">
        <zr-button
          type="primary"
          size="default"
          @click="openDrawerClick('add')"
        >新建资产</zr-button>
        <zr-button
          type="primary"
          size="default"
          @click="importClick"
        >导入资产</zr-button>
        <zr-button
          type="danger"
          size="default"
          plain
          :disabled="deleteList.length<=0"
          @click="batchDeleteClick"
        >批量删除</zr-button>
        <zr-button
          type="primary"
          size="default"
          link
          @click="downloadClick"
        >
          下载模板文件</zr-button>
      </zr-col>
      <zr-col
        :span="14"
        class="search_line"
      >
        <div class="search_box">
          <zr-tag
            v-for="tag in tags"
            :key="tag.title"
            type="info"
            closable
            :disable-transitions="false"
            @close="handleClose(tag)"
          >
            {{ tag.title }}：{{ tag.value }}
          </zr-tag>
          <zr-select
            ref="mySelect"
            v-model="selectValue"
            :style="{width:selectValue?'110px':'440px'}"
            filterable
            placeholder="请选择进行过滤的标签（默认按照主机名进行过滤），可添加多个"
            @keyup.enter="searthSelectEnter"
            @blur="onBlur"
          >
            <zr-option
              v-for="item in options"
              :key="item.value"
              :label="item.value"
              :value="item.value"
            />
          </zr-select>
          <div v-if="selectValue">
            <zr-input
              v-model="inputValue"
              placeholder="请输入"
              @keyup.enter="inputSearchEnter"
            />
          </div>
          <Icon
            v-if="tags.length>0"
            class="icon_close"
            name="zr-close"
            :size="17"
            @click="closeClick"
          />
        </div>
        <zr-button
          style="height: 35px"
          @click="searchClick"
        >
          <icon name="zr-search" />
        </zr-button>
      </zr-col>

    </zr-row>
    <zr-table
      ref="tableRef"
      v-loading="loading"
      :data="tableData"
      :row-key="getRowKey"
      empty-text="暂无数据"
      @expand-change="handleExpandChange"
      @selection-change="handleSelectionChange"
    >
      <zr-table-column
        type="expand"
        width="30"
      >
        <template #default="props">
          <div class="detail-msg">
            <zr-card>
              <zr-table
                empty-text="暂无数据"
                :data="expandTableData[props.row.id]"
                style="margin-top:0px;"
              >
                <zr-table-column
                  label="数据类型"
                  prop="type"
                >
                  <template #default="scope">
                    {{ scope.row.type || '-' }}
                  </template>
                </zr-table-column>
                <zr-table-column
                  label="对象名称"
                  prop="filename"
                >
                  <template #default="scope">
                    {{ scope.row.filename || '-' }}
                  </template>
                </zr-table-column>
                <zr-table-column
                  label="所属任务"
                  prop="task_name"
                >
                  <template #default="scope">
                    {{ scope.row.task_name || '-' }}
                  </template>
                </zr-table-column>
                <zr-table-column
                  label="上传方式"
                  prop="upload_method"
                  width="120"
                >
                  <template #default="scope">
                    {{ scope.row.upload_method == 'manual' ? '手动上传' : '-' }}
                  </template>
                </zr-table-column>
                <zr-table-column
                  label="最近执行状态"
                  prop="status"
                >
                  <template #default="scope">
                    <p>{{ progressOptionList[scope.row.status] }}</p>
                    <zr-progress
                      :percentage="validatePercentage(scope.row.progress)"
                      :color="customColorMethod(scope.row.status)"
                    />
                  </template>
                </zr-table-column>
                <zr-table-column
                  label="上传平台时间"
                  prop="created_at"
                  width="180"
                >
                  <template #default="scope">
                    <div v-if="scope.row.created_time_ago || scope.row.created_at">
                      <zr-tag v-if="scope.row.created_time_ago">{{ scope.row.created_time_ago }}</zr-tag>
                      <p>{{ scope.row.created_at || '-' }}</p>
                    </div>
                    <div v-else>-</div>
                  </template>
                </zr-table-column>
                <zr-table-column
                  label="最近更新时间"
                  prop="updated_at"
                  width="180"
                >
                  <template #default="scope">
                    <div v-if="scope.row.updated_at || scope.row.updated_time_ago">
                      <zr-tag v-if="scope.row.updated_time_ago">{{ scope.row.updated_time_ago }}</zr-tag>
                      <p>{{ scope.row.updated_at || '-' }}</p>
                    </div>
                    <div v-else>-</div>
                  </template>
                </zr-table-column>
                <zr-table-column
                  label="操作"
                  width="100px"
                >
                  <template #default="scope">
                    <zr-button
                      type="primary"
                      link
                      @click="secureClick(scope.row)"
                    >解除关联</zr-button>
                  </template></zr-table-column>
              </zr-table>
            </zr-card>
          </div>
        </template>
      </zr-table-column>
      <zr-table-column
        type="selection"
        width="30"
        :selectable="isRowSelectable"
        :reserve-selection="true"
      />
      <zr-table-column
        label="主机名"
        prop="host"
      >
        <template #default="scope">
          <zr-tooltip
            v-if="scope.row.host"
            placement="top-start"
            :content="scope.row.host"
          >
            <p class="table-item-content">{{ scope.row.host }}</p>
          </zr-tooltip>
          <p v-else>-</p>
        </template>
      </zr-table-column>
      <zr-table-column
        label="IP地址"
        prop="ip"
      >
        <template #default="scope">
          {{ scope.row.ip || '-' }}
        </template>
      </zr-table-column>
      <zr-table-column
        label="操作系统"
        prop="os"
        width="110px"
        show-overflow-tooltip
      >
        <template #default="scope">
          <span :class="scope.row.os=='Linux' ?'':'system-icon-style'">
            <SystemIcon
              v-if="scope.row.os =='Linux' ? 'linux' :'windows'"
              :name="scope.row.os =='Linux' ? 'linux' :'windows'"
            />
          </span>
        </template>
      </zr-table-column>
      <zr-table-column
        label="所属用户"
        prop="owner_name"
        width="200px"
      >
        <template #default="scope">
          <zr-tooltip
            v-if="scope.row.owner_name"
            placement="top-start"
            :content="scope.row.owner_name"
          >
            <p class="table-item-content">{{ scope.row.owner_name }}</p>
          </zr-tooltip>
          <p v-else>-</p>
        </template>
      </zr-table-column>
      <zr-table-column
        label="所属组织"
        prop="organization"
      >
        <template #default="scope">
          <zr-tooltip
            v-if="scope.row.organization"
            placement="top-start"
            :content="scope.row.organization"
          >
            <p class="table-item-content">{{ scope.row.organization }}</p>
          </zr-tooltip>
          <p v-else>-</p>
        </template>
      </zr-table-column>
      <zr-table-column
        label="创建时间"
        prop="created_at"
      >
        <template #default="scope">
          <div v-if="scope.row.created_at || scope.row.created_time_ago">
            <zr-tag v-if="scope.row.created_time_ago">{{ scope.row.created_time_ago }}</zr-tag>
            <p>{{ timeFormat(scope.row.created_at) || '-' }}</p>
          </div>
          <div v-else>-</div>
        </template>
      </zr-table-column>
      <zr-table-column
        label="被关联数"
        prop="associated_count"
        width="120px"
      >
        <template #default="scope">
          <span
            class="col-history"
            @click="associatedCountClick(scope.row)"
          >{{ scope.row.associated_count }}</span>
        </template>
      </zr-table-column>
      <zr-table-column
        label="操作"
        width="120px"
      >
        <template #default="scope">
          <zr-button
            type="primary"
            link
            @click="openDrawerClick('edit', scope.row)"
          >编辑</zr-button>
          <zr-button
            type="primary"
            link
            :disabled="scope.row.associated_count!==0"
            @click="deleteClick(scope.row)"
          >删除</zr-button>
        </template>
      </zr-table-column>
    </zr-table>
    <zr-pagination
      :current-page="searchForm.page"
      :page-size="searchForm.page_size"
      :page-sizes="[20, 40, 60, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
    <!-- 添加/编辑抽屉内容 -->
    <drawer
      v-model="openDrawer"
      :title="drawerTitle"
      :row-id="rowId"
      @saveClick="saveClick"
      @cancelClick="cancelClick"
    />
    <!-- 导入资产 -->
    <importDrawer
      v-model="openImportDrawer"
      @saveImportClick="saveImportClick"
      @cancelImportClick="cancelImportClick"
    />
  </div>
</template>
<script setup>
import { ref, onBeforeUnmount } from 'vue';
import { ZrMessage, ZrMessageBox } from 'qianji-ui';
import { timeFormat } from '@/utils/formatting';
import drawer from './drawer.vue';
import importDrawer from './importDrawer.vue';
import { getHostsList, batchDelete, disassociateHost, getHostAssociatedPkg } from '@/api/asset/host/offlineImport';

const drawerTitle = ref('');
const openDrawer = ref(false);
const tableRef = ref(null);
const searchForm = ref({
  page: 1,
  'page_size': 20,
  host: '',
  ip: '',
  'owner_name': '',
  'business_type': ''
});
const SEARCH_MAP = [ // 映射关系
  { key: '主机名', value: 'host' },
  { key: 'IP地址', value: 'ip' },
  { key: '所属用户', value: 'owner_name' }
];
const loading = ref(true);
const total = ref(0);
const tableData = ref([]);
const expandTableData = ref([]);
const deleteList = ref([]);
const rowId = ref('');
const openImportDrawer = ref(false);
const progressOptionList = ref({ // 进度状态
  waiting: '等待中',
  analyzing: '分析中',
  'not_analyzed': '未分析',
  success: '成功',
  error: '分析异常'
});
const customColorMethod = (status) => { // 进度条颜色
  if (status == 'waiting') {
    return '#FFB968'; // 橙色
  } else if (status == 'not_analyzed') {
    return '#B0B0B0'; // 灰色
  } else if (status == 'analyzing') {
    return '#53A9FF'; // 蓝色
  } else if (status == 'success') {
    return '#8BCF6D'; // 绿色
  }
  return '#D72C00'; // 红色
};

onBeforeUnmount(() => { // 离开页面，销毁定时器
  clearInterval(inter.value);
});


// ---搜索框内容开始---
const selectValue = ref('');
const inputValue = ref('');
const onBlurInputValue = ref('');// select搜索框失焦后的内容
const onBlurFlag = ref(true);
const options = ref([
  {
    value: "主机名"
  },
  {
    value: "IP地址"
  },
  {
    value: "所属用户"
  }
]);
const tags = ref([]);
const mySelect = ref();
const timeData = ref();
function searthSelectEnter(event) { // 下拉框输入内容后回车触发的事件
  if (event.target.tagName.toLowerCase() === 'input') { // 判断触发的时间是否是input标签 如果是则返回true
    searchEnterChange(event.target.value);// 调用生成tag标签事件
  }
  event.target.value = ''; // 清空输入框的值
  selectValue.value = ''; // 清空选择的下拉内容
  mySelect.value.blur(); // 使mySelect元素失去焦点,隐藏下拉框
  searchClick();
}

function searchEnterChange(val) { // 直接输入内容后生成tag标签
  if (val) { // 如果input 输入框中有value值
    if (tags.value.length === 0) { //  1. 如果tags形式的内容为空时（代表是直接输入的内容而没有选择下拉条件）
      tags.value.push({ title: '主机名', value: val }); // 则默认形成的tags内容为主机名搜索
    } else { // 2. 否则走else（代表是先选择下拉框中的条件再输入内容）
      tags.value.map(item => {
        if (tags.value.filter(ite => ite.title === '主机名').length === 0) { // 如果勾选的下拉条件没有主机名默认给他主机名
          tags.value.push({ title: '主机名', value: val }); // 形成一个tags标签
        } else {
          if (item.title === '主机名') { // 有主机名则修改主机名的value
            item.value = val;
          }
        }
      });
    }
  }
}
function onBlur(event) { // 失焦事件
  if (event.target.value) {
    onBlurInputValue.value = event.target.value;// 失焦后将select下拉输入的值存起来
    timeData.value = setTimeout(() => { // 通过定时器判断失焦时间，超出时间点击查询不生成tag标签，只有点击查询按钮瞬间才生成tag标签
      onBlurFlag.value = false;
    }, 500);
  }
  console.log(event.target.value, 'event');
}
function inputSearchEnter(event) { // 下拉选择后第二个输入框输入内容的事件
  if (event.target.tagName.toLowerCase() === 'input') {
    console.log(event.target.value, "event.target.value");
    clickChange(event.target.value);
  }
  searchClick();
}
function clickChange(val) { // 勾选下拉选择输入内容后形成tags形式
  if (val) {
    if (tags.value.length === 0) {
      tags.value.push({ title: selectValue.value, value: val });
    } else {
      tags.value.map(item => {
        if (selectValue.value && tags.value.filter(ite => ite.title === selectValue.value).length === 0) { // 如果没有重复的下拉条件 则形成新的tag形式
          tags.value.push({ title: JSON.parse(JSON.stringify(selectValue.value)), value: val });
        } else {
          if (item.title === selectValue.value) { // 如果勾选的下拉条件重复 则重新赋值
            item.value = val;
          }
        }
      });
    }
  }
  selectValue.value = '';
  inputValue.value = '';
}
function closeClick() { // 清空事件
  tags.value = [];
  searchClick();
}
function handleClose(tag) { // tags标签的清空
  tags.value.splice(tags.value.indexOf(tag), 1);
  searchClick();
}
function searchClick() { // 搜索事件
  clearInterval(timeData.value);// 清除定时器
  if (onBlurFlag.value) { // 通过失焦时间点击查询是否生成tag标签
    searchEnterChange(onBlurInputValue.value);
  }
  onBlurInputValue.value = '';// 查询后清空select搜索框失焦后的内容
  onBlurFlag.value = true;// 查询后将控制失焦时间点击查询是否生成tag标签布尔值清空
  clickChange(inputValue.value);
  searchForm.value.host = searchForm.value.ip = searchForm.value['owner_name'] = '';
  tags.value.forEach((item) => {
    const mapData = SEARCH_MAP.find(map => map.key === item.title);
    searchForm.value[mapData.value] = item.value;
  }, {});
  getHostsListFun();
}
// ---搜索框内容结束---

function getRowKey(row) { // 在列表刷新之前，保存已勾选的数据
  return row.id;
}

const inter = ref();
getHostsListFun();
function getHostsListFun() { // 列表内容
  loading.value = true;
  getHostsList(searchForm.value).then(res => {
    tableData.value = res.data.data.list;
    total.value = res.data.data.count;
  }).finally(() => {
    loading.value = false;
  });
}
function associatedCountClick(row) { // 被关联数
  tableRef.value.toggleRowExpansion(row);// 通过ref调用zr-table实例下的toggleRowExpansion展开方法
}

function openDrawerClick(type, row) { // 打开任务弹窗
  drawerTitle.value = type == 'add' ? '添加主机资产' : '编辑主机资产';
  openDrawer.value = true;
  if (type == 'edit') {
    rowId.value = row.id;
  }
}

function downloadClick() { // 下载资产模板
  const csvFilePath = process.env.BASE_URL + 'host.csv'; // 文件的实际路径
  const link = document.createElement('a');// 创建一个隐藏的 <a> 标签
  document.body.appendChild(link);
  link.href = csvFilePath;// 设置链接的属性
  // link.setAttribute('download', '导出模版.csv');
  link.click();
  document.body.removeChild(link);// 移除创建的 <a> 标签
}
function importClick() { // 导入资产模板
  openImportDrawer.value = true;
}

function isRowSelectable(row) { // 当被关联数为0 就禁用勾选框
  return row.associated_count == 0;
}

function handleSelectionChange(val) {
  deleteList.value = val.map(item => item.id);
  if (deleteList.value.length > 0) { // 如果勾选复选框就清除定时器，防止刷新的时候勾选的数据回显到其他页
    if (inter.value) {
      clearInterval(inter.value);
    }
  } else {
    getHostsListFun();
  }
}

const expandedRowsNum = ref([]);
function handleExpandChange(row, expandedRows) { // 列表展开功能
  expandedRowsNum.value = expandedRows;
  if (expandedRows.length > 0) { // 只有展开的时候调用接口
    getHostAssociatedPkg({ 'host_id': expandedRows[expandedRows.length - 1].id }).then(res => { // 获取展开列表接口(只刷新最新展开的数据列表)
      if (inter.value) {
        clearInterval(inter.value);
      }
      inter.value = setInterval(() => {
        handleExpandChange(row, expandedRows);
      }, 10000);
      
      if (res.data.code == 0) {
        expandTableData.value[row.id] = res.data.data.package_detect_info;
      }
    });
  } else {
    clearInterval(inter.value);
  }
}

function batchDeleteFun(data) {
  batchDelete({ 'ids': data }).then(res => { // 批量删除接口
    if (res.data.code == 0) {
      ZrMessage.success(res.data.data);
      getHostsListFun();
    } else {
      ZrMessage.error(res.data.data);
    }
  }).catch(() => {
  });
}

function batchDeleteClick() { // 批量删除
  ZrMessageBox.confirm('确认删除勾选的所有选项?', '确认操作', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    batchDeleteFun(deleteList.value);
    tableRef.value.clearSelection();
  });
}
function deleteClick(row) { // 删除
  ZrMessageBox.confirm('确认删除' + row.host + '主机?', '确认操作', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      batchDeleteFun([row.id]);
    });
}

function secureClick(row) { // 解除关联
  ZrMessageBox.confirm('确认解绑此条数据?', '确认操作', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    disassociateHost(row.image_id, { 'host_id': '' }).then(res => {
      if (res.data.code == 0) {
        ZrMessage.success(res.data.data);
        handleExpandChange(expandedRowsNum.value.filter(item => item.id === row.host_id)[0], expandedRowsNum.value.filter(item => item.id === row.host_id)); // 只刷新当前解除关联的列表
        getHostsListFun();
      } else {
        ZrMessage.error(res.data.data);
      }
    });
  });
}
function handleSizeChange(val) {
  searchForm.value['page_size'] = val;
  searchForm.value.page = 1;
  getHostsListFun();
}
function handleCurrentChange(val) {
  searchForm.value.page = val;
  getHostsListFun();
}

function saveClick() {
  openDrawer.value = false;
  getHostsListFun();
}
function cancelClick() {
  openDrawer.value = false;
}

// 导入资产
function saveImportClick() {
  // openImportDrawer.value = false;
  getHostsListFun();
}
function cancelImportClick() {
  openImportDrawer.value = false;
}

function validatePercentage(progress) {
  if (progress >= 0 && progress <= 100) {
    return progress;
  } else {
    return 0;
  }
}
</script>
<style lang="scss" scoped>

//--- 搜索框样式
.search_line{
  display:flex;
}
.search_box {
  width: 900px;
  height: 35px;
  border:1px solid #dcdfe6;
  display:flex;
  align-items:center;
  padding-right: 10px;
  position:relative;
}
.icon_close{
  position:absolute;
  right:10px
}


:deep(.el-range-editor.is-active,
  .el-range-editor.is-active:hover,
  .el-select .el-input.is-focus .el-input__inner,
  .el-input__inner){
    border: none;
    // width: 338px;
  }
  :deep(.search_box .el-select .el-input.is-focus .el-input__wrapper){
    box-shadow:none !important;
    border: none;
  }
  :deep(.search_box .el-select:hover:not(.el-select--disabled) .el-input__wrapper){
    box-shadow:none !important;
    border: none;
  }
  :deep(.search_box .el-input__wrapper){
    box-shadow:none;
  }
  :deep(.search_box .el-select .el-input__wrapper){
    box-shadow:none !important;
    border: none;
  }
  :deep(.search_box .el-input__suffix){
    display: none;
  }
  // ---
  .col-history {
  background: #53a9ff;
  color: #fff;
  padding: 3px 8px;
  border-radius: 3px;
  cursor: pointer;
}
</style>
