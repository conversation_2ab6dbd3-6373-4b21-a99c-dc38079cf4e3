<template>
  <div>
    <zr-drawer
      v-bind="$attrs"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      @open="draOpen"
    >
      <template #header>
        <h4 style="color: #303133;">
          {{ title }}
        </h4>
      </template>
      <p
        class="title-pie"
        style="margin-bottom:10px"
      ><span />项目基本信息</p>
      <zr-form
        ref="formRef"
        label-position="top"
        :model="form"
        :rules="rules"
      >
        <zr-form-item
          prop="host"
          label="主机名"
          :rules="[
            {
              required: true,
              message: '请输入主机名',
              trigger: 'blur'
            }]"
        >
          <zr-input
            v-model="form.host"
            maxlength="50"
            show-word-limit
            placeholder="请输入主机名"
          />
        </zr-form-item>

        <zr-form-item
          prop="ip"
          label="IP地址"
        >
          <zr-input
            v-model="form.ip"
            placeholder="请输入IP地址, 多个IP地址用英文逗号隔开"
          />
        </zr-form-item>
        <zr-form-item
          prop="os"
          label="操作系统"
        >
          <zr-select
            v-model="form.os"
          >
            <zr-option
              label="Windows"
              value="Windows"
            />
            <zr-option
              label="Linux"
              value="Linux"
            />
          </zr-select>
        </zr-form-item>
        <zr-form-item
          prop="owner_name"
          label="所属用户"
        >
          <zr-input
            v-model="form.owner_name"
            maxlength="50"
            show-word-limit
            placeholder="请输入所属用户"
          />
        </zr-form-item>
        <zr-form-item
          prop="organization"
          label="所属组织"
        >
          <zr-input
            v-model="form.organization"
            maxlength="50"
            show-word-limit
            placeholder="请输入所属组织"
          />
        </zr-form-item>
        <zr-form-item
          
          label="备注信息"
        >
          <zr-input
            v-model="form.description"
            type="textarea"
            maxlength="500"
            show-word-limit
          />
        </zr-form-item>
      </zr-form>
      <template #footer>
        <zr-button
          type="primary"
          size="default"
          :loading="loading"
          @click="submit(formRef)"
        >
          确定
        </zr-button>
        <zr-button
          size="default"
          @click="cancelClick"
        >
          取消
        </zr-button>
      </template>
    </zr-drawer>
  </div>
</template>
  
<script setup>
import { ref, defineEmits, defineProps } from 'vue';
import { addHost, editHost, getDetail } from '@/api/asset/host/offlineImport';
import { ZrMessage } from 'qianji-ui';

const emit = defineEmits(['cancelClick', 'saveClick']);
const props = defineProps({
  title: {
    type: String
  },
  rowId: {
    type: String,
    default: ''
  }
});
const loading = ref(false);
const formRef = ref();
const form = ref({
  host: '',
  'owner_name': '',
  ip: '',
  os: 'Windows',
  organization: '',
  description: '',
  'business_type': 'mfp'
});

const rules = reactive({
  ip: [
    { validator: validateIPList, trigger: 'blur' }
  ]
});

function validateIPList(rule, value, callback) {
  if (value == '') {
    callback();
  }
  const ips = value.split(',');
  for (let i = 0; i < ips.length; i++) {
    const ip = ips[i].trim(); // 去掉空格
    const ipRegex = /^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    if (!ipRegex.test(ip)) {
      callback('IP地址格式错误');
    }
  }
  callback();
}


function draOpen() {
  if (props.title === '编辑主机资产') {
    getDetail(props.rowId).then(res => {
      form.value = res.data.data;
    });
  } else {
    form.value = {};
    form.value.ip = '';
    form.value.os = 'Windows';
    form.value['business_type'] = 'mfp';
  }
}

const submit = async(formEl) => {
  loading.value = true;
  if (!formEl) return;
  await formEl.validate((valid) => {
    if (valid) {
      if (props.title === '添加主机资产') {
        addHost(form.value).then(res => {
          if (res.data.code == 0) {
            ZrMessage.success(res.data.data);
            emit('saveClick');
          } else {
            ZrMessage.error(res.data.data);
          }
        }).finally(() => {
          loading.value = false;
        });
      } else {
        editHost(props.rowId, form.value).then(res => {
          if (res.data.code == 0) {
            ZrMessage.success(res.data.data);
            loading.value = false;
            emit('saveClick');
          } else {
            loading.value = false;
            ZrMessage.error(res.data.data);
          }
        }).finally(() => {
          loading.value = false;
        });
      }
    } else {
      loading.value = false;
      return false;
    }
  });
};

function cancelClick() {
  emit('cancelClick');
}
</script>
  <style lang="scss" scoped>
  .input-wrapper {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}
:deep(.el-form-item__content){
display: flow;
}

.input-wrapper .el-input {
  margin-right: 4px;
  flex: 1;
}

.input-wrapper .el-icon {
  cursor: pointer;
}
:deep(.el-input__suffix){
  display: block !important;
}
  </style>
  
