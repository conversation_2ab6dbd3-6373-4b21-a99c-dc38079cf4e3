<template>
  <div>
    <div class="basics">
      <h2 class="title-pie"><span />系统基本信息</h2>
      <zr-form
        ref="ruleFormRef"
        label-width="120"
        :model="basicsForm"
        :rules="rules"
      >
        <zr-form-item
          label="系统名称"
          prop="sys_name"
        >
          <zr-input
            v-model="basicsForm.sys_name"
            placeholder="系统名称"
            maxlength="20"
          />
        </zr-form-item>
        <zr-alert
          :closable="false"
          title="系统菜单栏、版本信息等页面中显示的系统名称。"
          show-icon
          type="info"
        />
        <zr-form-item
          label="版权信息"
          prop="sys_copyright"
        >
          <zr-input
            v-model="basicsForm.sys_copyright"
            placeholder="版权信息"
            maxlength="30"
            show-word-limit
            type="textarea"
          />
        </zr-form-item>
        <zr-alert
          :closable="false"
          title="版权信息等页面中显示的版权说明。内容为空时，将隐藏系统版权信息。"
          show-icon
          type="info"
        />

        <zr-form-item
          label="系统标志"
        >
          <zr-upload
            ref="upload"
            action="#"
            list-type="picture-card"
            :limit="1"
            :file-list="fileList"
            :on-exceed="handleExceed"
            :on-change="handleChange"
            :auto-upload="false"
          >
            <template
              #trigger
            >
              <Icon
                class="avatar-uploader-icon"
                name="zr-add"
              />
            </template>
            <template #file="{ file }">
              <div>
                <img
                  class="el-upload-list__item-thumbnail"
                  :src="file.url"
                  alt=""
                >
                <span class="el-upload-list__item-actions">
                  <span
                    class="el-upload-list__item-delete"
                    @click="handleRemove(file)"
                  >
                    <Icon
                      name="zr-delete"
                      size="17"
                    />
                  </span>
                  <span
                    class="el-upload-list__item-delete"
                    @click="handleDownload(file)"
                  >
                    <Icon
                      name="zr-download"
                      size="17"
                    />
                  </span>
                </span>
              </div>
            </template>
          </zr-upload>
        </zr-form-item>
        <zr-alert
          :closable="false"
          title="系统菜单栏、版本信息等页面中展示的系统 LOGO 图片。未设置图片时，将隐藏 LOGO 标志。支持 JPG/PNG 格式的图片文件，尺寸不超过 150x60px，大小不超过 1MB。"
          show-icon
          type="info"
        />
        <zr-form-item style="margin-top:50px;">
          <zr-button
            type="primary"
            @click="saveClick(ruleFormRef)"
          > 保 存 </zr-button>
        </zr-form-item>
        
      </zr-form>
    </div>
  </div>
</template>
<script setup>
import { ref } from 'vue';
import { genFileId } from 'element-plus';
import { ZrMessage, ZrMessageBox } from 'qianji-ui';
import { saveConfigs, acquireConfigs } from '@/api/system/configuration';
import axios from 'axios';

const basicsForm = ref({
  'sys_copyright': '',
  'sys_name': '',
  'business_type': 'mfp',
  'is_del_logo': false
});
const upload = ref();
const fileList = ref();
const ruleFormRef = ref();
const delFun = ref(false);
const sysLogoAddr = ref('');
const rules = {
  "sys_name": [
    { required: true, message: '系统名称不可为空', trigger: 'blur' }
  ]
};

function showFun(res) { // 文件上传显隐
  var t = document.getElementsByClassName('el-upload--picture-card');// 选取id为test的div元素
  t[0].style.display = res;// 隐藏选择的元素
}
onMounted(() => {
  acquireConfigsFun();
});
function acquireConfigsFun(bool) { // 获取信息
  acquireConfigs().then(res => {
    basicsForm.value['sys_copyright'] = res.data.data.sys_copyright;
    basicsForm.value['sys_name'] = res.data.data.sys_name;
    sysLogoAddr.value = res.data.data.sys_logo_addr;
    showFun(res.data.data.sys_logo_addr ? 'none' : 'inline-flex');
    localStorage.setItem('sysLocal', JSON.stringify(res.data.data));
    if (bool) {
      location.reload();
    }
  });
  sysLogoFun();
}
function sysLogoFun() {
  axios({
    url: window.BASE_URL + '/eos-service/api/v6/system/sys_info/logo/mfp',
    method: 'get',
    responseType: 'blob'
  }).then(({ data }) => {
    if (data.type == 'application/octet-stream') {
      const blob = new Blob([data]); // 返回的文件流数据
      const url = window.URL.createObjectURL(blob); // 将他转化为路径
      fileList.value = [{ name: 'logo', url: url }];
    } else {
      fileList.value = [];
    }
  }).catch(() => {
    fileList.value = [];
  });
}
const saveClick = async(formEl) => { // 保存
  if (!formEl) return;
  await formEl.validate((valid, fields) => {
    if (valid) {
      const data = new FormData();
      if (sysLogoAddr.value !== '' && (!fileList.value[0])) {
        data.append('is_del_logo', true);
      } else {
        data.append('is_del_logo', false);
      }
      data.append('sys_logo', fileList.value[0] ? fileList.value[0].raw : undefined);
      data.append('sys_copyright', basicsForm.value.sys_copyright);
      data.append('sys_name', basicsForm.value.sys_name);
      data.append('business_type', 'mfp');
      saveConfigs(data).then(res => {
        ZrMessageBox.confirm('更新系统基础配置请求刷新页面', '刷新提示', {
          confirmButtonText: '刷新',
          cancelButtonText: '稍后刷新',
          showClose: false,
          // showCancelButton: false,
          closeOnClickModal: false,
          closeOnPressEscape: false,
          type: 'warning'
        })
          .then(() => {
            acquireConfigsFun(true);
          });
      });
    } else {
      console.log('error submit!', fields);
    }
  });
};

const handleRemove = (file) => { // 删除
  delFun.value = true;
  fileList.value = [];
  showFun('inline-flex');
};

const handleDownload = (file) => { // 下载
  axios({
    url: window.BASE_URL + '/eos-service/api/v6/system/sys_info/logo/mfp',
    method: 'get',
    responseType: 'blob'
  }).then(res => {
    const blob = new Blob([res.data], {
      type: "image/png"
    });
    const url = window.URL.createObjectURL(blob);
    var a = document.createElement('a');
    // document.body.appendChild(a);
    // a.style = 'display: none';
    a.download = 'logo';
    a.href = url;
    a.click();
    // window.URL.revokeObjectURL(url); // 释放url
  });
};

const handleExceed = (files) => { // 超出限制个数时
  upload.value.clearFiles();
  const file = files[0];
  file.uid = genFileId();
  upload.value.handleStart(file);
};

const handleChange = (response, uploadFile, uploadFiles) => { // 上传
  // 判断格式
  const types = ["image/jpg", "image/png", "image/jpeg"];
  const isJPG = types.includes(response.raw.type);
  if (!isJPG) {
    ZrMessage.error("格式只允许JPG/PNG!");
    acquireConfigsFun();
  }
 
  // 判断体积大小
  const isLt2M = response.raw.size / 1024 / 1024 < 1;
  if (!isLt2M) {
    ZrMessage.error("大小不能超过1MB!");
    acquireConfigsFun();
  }
 
  // 判断尺寸大小
  new Promise(function(resolve, reject) {
    const width = 150;
    const height = 60;
    const _URL = window.URL || window.webkitURL;
    const img = new Image();
    img.onload = function() {
      const valid = img.width <= width && img.height <= height;
      valid ? resolve() : reject();
    };
    img.src = _URL.createObjectURL(response.raw);
  }).then(
    () => {
      return response.raw;
    },
    () => {
      ZrMessage.error("图片尺寸限制为150 x 60px!");
      acquireConfigsFun();
      return Promise.reject();
      // 必须加上return false; 才能阻止
      // return false;
    }
  );
  showFun('none');
  fileList.value = uploadFile;
};

</script>
    
<style lang="scss" scoped>
.basics{
    width: 50%;
    h2{
        font-size: 14px;
        font-weight: 600;
        margin-bottom: 20px;
    }
    .el-alert--info.is-light{
        margin: 0 0 24px 120px;
        width: calc(100% - 120px);
    }
    button.el-button.el-button--primary{
      width: 100px;
    }
    :deep(.el-upload-list--picture-card .el-upload-list__item){
      height: 60px;
    }
    :deep(.el-upload--picture-card){
      height: 60px;
    }
    :deep(.el-upload-list--picture-card .el-upload-list__item){
      margin: 0;
    }
    :deep(.el-form-item__content){
      line-height: 10px;
    }
    .el-form-item{
      margin-bottom: 16px;
    }
}
</style>
  
