<template>
  <div id="service-monitoring">
    <!-- 服务监控 -->
    <zr-button
      type="primary"
      :loading="loading"
      @click="getTableData"
    >刷新</zr-button>
    <zr-table
      v-loading="loading"
      :data="tableData"
      empty-text="暂无数据"
    >
      <zr-table-column
        prop="service_name"
        label="服务名"
        show-overflow-tooltip
      />
      <zr-table-column
        prop="version"
        label="版本号"
        show-overflow-tooltip
      />
      <zr-table-column
        prop="ch_name"
        label="描述"
        show-overflow-tooltip
      />
      <zr-table-column
        prop="cpu"
        label="CPU (%)"
        show-overflow-tooltip
      />
      <zr-table-column
        prop="memory"
        label="内存 (使用内存/限制内存)"
        show-overflow-tooltip
      />
      <zr-table-column
        prop="status"
        label="状态"
        width="100"
      >
        <template #default="scope">
          <zr-tag
            v-if="scope.row.status==='running'"
            type="success"
          >正常</zr-tag>
          <zr-tag
            v-else
            type="danger"
          >异常</zr-tag>
        </template>
      </zr-table-column>
      <zr-table-column
        label="操作"
        width="100"
      >
        <template #default="scope">
          <zr-button
            link
            type="primary"
            @click="restartClick(scope.row)"
          >重启</zr-button>
        </template>
      </zr-table-column>
    </zr-table>
  </div>
</template>
  
<script setup>
import { ref, onMounted } from 'vue';
import { getContainersList, containersRestart } from '@/api/system/configuration';
import { ZrMessageBox, ZrMessage } from 'qianji-ui';
  
const tableData = ref([]);
  
const loading = ref(false);
  
onMounted(() => {
  getTableData();
});
  
function getTableData() {
  loading.value = true;
  getContainersList({ 'business_type': 'mfp' }).then(res => {
    tableData.value = res.data.data;
  }).finally(() => {
    loading.value = false;
  });
}
function restartClick(row) {
  ZrMessageBox.confirm('确认重启 ' + row.service_name + ' 服务吗?', '提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      containersRestart(row.container_id).then(res => {
        if (res.data.code === 0) {
          ZrMessage.success(row.service_name + ' 服务重启成功');
          getTableData();
        } else {
          ZrMessage.error('服务重启失败');
        }
      });
    });
}
  
</script>
  <style lang="scss" scoped>
  
  </style>
  
