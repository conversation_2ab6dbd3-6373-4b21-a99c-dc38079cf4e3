<template>
  <div class="main-content">
    <TabPane
      :option="tabOption"
      @handleClick="handleClick"
    />
    <SafetyConfig v-if="tabNum=='1'" />
    <ServiceMonitoring v-else-if="tabNum=='3'" />
    <BasicsConfig v-else />
  </div>
</template>
<script setup>
import TabPane from '@/components/common/TabPane.vue';
import BasicsConfig from './BasicsConfig/index.vue';
import SafetyConfig from './SafetyConfig/index.vue';
import ServiceMonitoring from './ServiceMonitoring/index.vue';

const tabOption = [
  {
    name: '1',
    label: '安全配置'
  },
  {
    name: '2',
    label: '产品信息定制'
  },
  {
    name: '3',
    label: '服务监控'
  }
];
const tabNum = ref('1');

function handleClick(val) {
  tabNum.value = val;
}

</script>
    
  
