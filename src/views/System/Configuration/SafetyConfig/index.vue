<template>
  <div>
    <div class="safety">
      <zr-form
        ref="formRef"
        label-width="120"
        :model="safetyForm"
        :show-message="false"
        :rules="rules"
      >
        <h2 class="title-pie"><span />密码策略</h2>
        <zr-form-item
          label="密码最小长度"
          prop="min_password_len"
        >
          <zr-input v-model.number="safetyForm.min_password_len">
            <template #append>位</template>
          </zr-input>
        </zr-form-item>
        <zr-alert
          :closable="false"
          title="不能小于8位"
          show-icon
          type="info"
        />
        <zr-form-item
          label="密码最大长度"
          prop="max_password_len"
        >
          <zr-input v-model.number="safetyForm.max_password_len">
            <template #append>位</template>
          </zr-input>
        </zr-form-item>
        <zr-alert
          :closable="false"
          title="不能大于20位"
          show-icon
          type="info"
        />
        <zr-form-item
          label="密码复杂度"
          prop="password_complexity"
        >
          <zr-checkbox-group v-model="safetyForm.password_complexity">
            <zr-checkbox
              v-for="item in complexityOption"
              :key="item.value"
              :label="item.value"
            >{{ item.label }}</zr-checkbox>
          </zr-checkbox-group>
        </zr-form-item>
        <zr-alert
          :closable="false"
          title="用户账号的密码中需要包含的字符内容。"
          show-icon
          type="info"
        />
        <h2 class="title-pie"><span />会话策略</h2>
        <zr-form-item
          label="会话超时时间"
          prop="session_validity_time"
        >
          <zr-input v-model.number="safetyForm.session_validity_time">
            <template #append>分钟</template>
          </zr-input>
        </zr-form-item>
        <zr-alert
          :closable="false"
          title="用户在此时间内无任何操作，系统将自动退出至登录页面。最小时间10分钟，最大时间120分钟"
          show-icon
          type="info"
        />
        <h2 class="title-pie"><span />账号锁定策略</h2>
        <zr-form-item
          label="登陆错误次数"
          prop="max_failed_login"
        >
          <zr-input v-model.number="safetyForm.max_failed_login" />
        </zr-form-item>
        <zr-alert
          :closable="false"
          title="登录错误达到次数后，账号将锁定"
          show-icon
          type="info"
        />
        <zr-form-item
          label="锁定时间"
          prop="lock_time"
        >
          <zr-input v-model.number="safetyForm.lock_time">
            <template #append>分钟</template>
          </zr-input>
        </zr-form-item>
        <zr-alert
          :closable="false"
          title="在账号锁定时间内，用户将无法再次登录"
          show-icon
          type="info"
        />
        <zr-form-item style="margin-top:50px;">
          <zr-button
            type="primary"
            :loading="loading"
            @click="submitClick(formRef)"
          > {{ loading?'保存中':'保 存' }} </zr-button>
        </zr-form-item>
      </zr-form>
    </div>
  </div>
</template>
<script setup>
import { ref } from 'vue';
import { securityConfigs, securitySubmit } from '@/api/system/configuration';
import { ZrMessage } from 'qianji-ui';

const minPasswordLen = (rule, value, callback) => {
  if (!value) {
    return callback(new Error());
  }
  setTimeout(() => {
    if (!Number.isInteger(value)) {
      callback(new Error());
    } else {
      if (value < 8) {
        callback(new Error());
      } else {
        callback();
      }
    }
  }, 500);
};
const maxPasswordLen = (rule, value, callback) => {
  if (!value) {
    return callback(new Error());
  }
  setTimeout(() => {
    if (!Number.isInteger(value)) {
      callback(new Error());
    } else {
      if (value > 20) {
        callback(new Error());
      } else {
        callback();
      }
    }
  }, 500);
};
const sessionValidityTime = (rule, value, callback) => {
  if (!value) {
    return callback(new Error());
  }
  setTimeout(() => {
    if (!Number.isInteger(value)) {
      callback(new Error());
    } else {
      if (value < 10 || value > 120) {
        callback(new Error());
      } else {
        callback();
      }
    }
  }, 500);
};
const maxFailedLogin = (rule, value, callback) => {
  if (!value) {
    return callback(new Error());
  }
  setTimeout(() => {
    if (!Number.isInteger(value)) {
      callback(new Error());
    } else {
      callback();
    }
  }, 500);
};
const lockTime = (rule, value, callback) => {
  if (!value) {
    return callback(new Error());
  }
  setTimeout(() => {
    if (!Number.isInteger(value)) {
      callback(new Error());
    } else {
      callback();
    }
  }, 500);
};
const safetyForm = ref({});
const formRef = ref();
const rules = reactive({
  'min_password_len': [
    { validator: minPasswordLen, trigger: 'change' }
  ],
  'max_password_len': [
    { validator: maxPasswordLen, trigger: 'change' }
  ],
  'session_validity_time': [
    { validator: sessionValidityTime, trigger: 'change' }
  ],
  'max_failed_login': [
    { validator: maxFailedLogin, trigger: 'change' }
  ],
  'lock_time': [
    { validator: lockTime, trigger: 'change' }
  ]
});
const complexityOption = [
  {
    label: '字母',
    value: 'letter'
  },
  {
    label: '数字',
    value: 'number'
  },
  {
    label: '包含大小写',
    value: 'upper_and_lower'
  },
  {
    label: '特殊字符',
    value: 'special_char'
  }
];
const loading = ref(false);
onMounted(() => {
  securityConfigsFun();
});
function securityConfigsFun() {
  securityConfigs().then(res => {
    safetyForm.value = res.data.data || {};
    safetyForm.value['password_complexity'] = res.data.data.password_complexity ? Object.keys(res.data.data.password_complexity) : [];
  });
}
const submitClick = async(formEl) => {
  loading.value = true;
  if (!formEl) return;
  await formEl.validate((valid, fields) => {
    if (valid) {
      if (safetyForm.value.min_password_len >= safetyForm.value.max_password_len) {
        ZrMessage.error('密码最小长度要小于最大长度');
        securityConfigsFun();
        loading.value = false;
        return;
      }
      var complexity = {};
      safetyForm.value['password_complexity'].forEach(item => {
        complexity[item] = "";
      });
      var obj = {
        'business_type': 'mfp',
        'min_password_len': safetyForm.value.min_password_len,
        'max_password_len': safetyForm.value.max_password_len,
        'session_validity_time': safetyForm.value.session_validity_time,
        'max_failed_login': safetyForm.value.max_failed_login,
        'lock_time': safetyForm.value.lock_time,
        'password_complexity': complexity
      };
      securitySubmit(obj).then(res => {
        if (res.data.code === 0) {
          ZrMessage.success('保存成功');
        } else {
          ZrMessage.error(res.data.msg);
        }
      }).finally(() => {
        securityConfigsFun();
        loading.value = false;
      });
    } else {
      loading.value = false;
    }
  });
};

</script>
    
<style lang="scss" scoped>
.safety{
    width: 50%;
    h2{
        font-size: 14px;
        font-weight: 600;
        margin-bottom: 20px;
    }
    .el-alert--info.is-light{
        margin: 0 0 20px 120px;
        width: calc(100% - 120px);
    }
    button.el-button.el-button--primary{
      width: 100px;
    }
    // .el-form-item.asterisk-left{
    //   width: 60%;
    // }
    .el-select{
      width: 100%;
    }
}
</style>
  
