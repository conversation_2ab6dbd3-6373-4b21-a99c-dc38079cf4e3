<template>
  <div>
    <zr-row
      :gutter="24"
      style="margin:0"
    >
      <zr-col
        v-for="(item,index) in list"
        :key="index"
        style="padding: 0 20px 20px 0"
        :span="screenWidth>=1600 ? 8:12"
      >
        <zr-card>
          <div class="content-card-top">
            <zr-row>
              <zr-col :span="6">
                <Icon
                  name="zr-module-fill"
                  :size="40"
                />
              </zr-col>
              <zr-col :span="8">
                <zr-tooltip
                  class="box-item"
                  effect="dark"
                  :content="item.basic.name"
                  placement="top"
                >
                  <h5 class="table-item-content">{{ item.basic.name }}</h5>
                </zr-tooltip>
              </zr-col>
              <zr-col :span="8">
                <h4 class="basicStyle">cpu: {{ item.basic.cpu }} 核</h4>
                <h4 class="basicStyle">内存: {{ numDelivery(item.basic.ram) }} G</h4>
                <h4>硬盘: {{ numDeliveryTB(item.basic.disk) }} TB</h4>
              </zr-col>
            </zr-row>
          </div>
          <div class="content-card-pie">
            <div class="chart">
              <PieChart
                :charts-id="'cpuChart' + index"
                :pie-data="{name:'CPU',value:item.resource.cpu_used_percent}"
              />
              <PieChart
                :charts-id="'ramChart' + index"
                :pie-data="{name:'内存',value:item.resource.ram_used_percent}"
              />
              <PieChart
                :charts-id="'diskChart' + index"
                :pie-data="{name:'硬盘',value:item.resource.disk_used_percent}"
              />

            </div>
          </div>
        </zr-card>
      </zr-col>
    </zr-row>
  </div>
</template>
<script setup>
import { ref, onMounted } from 'vue';
import { getNodesList } from '@/api/system/monitor';
import PieChart from '@/components/common/Echarts/PercentagePieCharts.vue';
const screenWidth = ref(window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth);
const loading = ref(false);
const list = ref([]);

onMounted(() => {
  window.onresize = () => {
    return (() => {
      screenWidth.value = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;
    })();
  };
  getNodesListFun();
});

function getNodesListFun() {
  loading.value = true;
  getNodesList().then(res => {
    list.value = res.data.data || [];
  }).finally(() => {
    loading.value = false;
  });
}


function numDelivery(num) { // 从B转化为GB
  var size = num / 1024 / 1024 / 1024;
  var result = parseFloat(size);
  if (isNaN(result)) {
    return false;
  }
  result = Math.round(size * 100) / 100;
  return result;
}

function numDeliveryTB(num) { // 从B转化为TB
  var size = num / 1024 / 1024 / 1024 / 1024;
  var result = parseFloat(size);
  if (isNaN(result)) {
    return false;
  }
  result = Math.round(size * 100) / 100;
  return result;
}
</script>
<style lang="scss" scoped>
.table-item-content{
  font-size: 20px;
  width: 120px;
}
.basicStyle{
  margin-bottom: 5px;
}
.chart{
  width: 100%;
  height: 150px;
  display: flex;
  >div {
      width: calc(100% / 3);
      height: 100%;
      text-align: center;
      display: flex;
      justify-content: center;
  }
 }
</style>
