<template>
  <div>
    <zr-row
      :gutter="24"
      justify="space-between"
    >
      <zr-col :span="6">
        <zr-button
          type="primary"
          @click="handleClick(false)"
        >添加角色</zr-button>
        <zr-button
          type="danger"
          plain
          @click="batchDeleteClick"
        >批量删除</zr-button>
      </zr-col>
      <zr-col
        :span="18"
        style="text-align: right;"
      >
        <zr-form inline>
          <zr-form-item>
            <zr-select
              v-model="searchForm.origin_type"
              class="m-2"
              @change="searchClick"
            >
              <zr-option
                label="全部来源"
                value=""
              />
              <zr-option
                v-for="item in originOption"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </zr-select>
          </zr-form-item>
          <zr-form-item>
            <zr-input
              v-model="searchForm.name"
              placeholder="角色名称"
            >
              <template #append>
                <zr-button @click="searchClick">
                  <zr-icon
                    name="Search"
                  />
                </zr-button>
              </template>
            </zr-input>
          </zr-form-item>
        </zr-form>
      </zr-col>
    </zr-row>
    <zr-table
      :data="tableData"
      empty-text="暂无数据"
      @selection-change="handleSelectionChange"
    >
      <zr-table-column
        type="selection"
        width="55"
      />
      <zr-table-column
        prop="username"
        label="角色名称"
        width="180"
      />
      <zr-table-column
        prop="origin_type"
        label="角色来源"
        width="180"
      >
        <template #default="scope">
          {{ optionFormat(originOption,scope.row.origin_type) }}
        </template>
      </zr-table-column>
      <zr-table-column
        prop="description"
        label="描述"
      />
      <zr-table-column
        prop="status"
        label="状态"
      >
        <template #default="scope">
          <zr-switch
            v-model="scope.row.status"
            @change="statusChange(scope.row)"
          />
        </template>
      </zr-table-column>
      <zr-table-column
        prop="deleted_at"
        label="最近更新时间"
      >
        <template #default="scope">
          {{ timeFormat(scope.row.deleted_at) }}
        </template>
      </zr-table-column>
      <zr-table-column
        label="操作"
      >
        <template #default="scope">
          <zr-button
            link
            type="primary"
            @click="allocationClick(scope.row)"
          >分配</zr-button>
          <zr-button
            link
            type="primary"
            @click="handleClick(scope.row)"
          >编辑</zr-button>
          <zr-button
            link
            type="primary"
            @click="delClick(scope.row)"
          >删除</zr-button>
        </template>
      </zr-table-column>
    </zr-table>
    <zr-pagination
      v-if="total>0"
      :current-page="searchForm.page"
      :page-size="searchForm.page_size"
      :page-sizes="[20, 40, 60, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
    <RoleDrawer
      v-model="drawer"
      :title="drawerTitle"
      :edit-data="editData"
      @saveClick="saveClick"
      @cancelClick="cancelClick"
    />
    <AllocationUser
      v-model="dialog"
      @cancelClick="cancelResetClick"
      @saveClick="saveResetClick"
    />
  </div>
</template>
<script setup>
import { ref } from 'vue';
import RoleDrawer from './RoleDrawer.vue';
import AllocationUser from './AllocationUser.vue';
import { ZrMessage, ZrMessageBox } from 'qianji-ui';
import { optionFormat, timeFormat } from '@/utils/formatting';
  
const originOption = [
  {
    label: '内置',
    value: '0'
  },
  {
    label: '自定义',
    value: '1'
  }
];
const total = ref(0);
const searchForm = ref({
  page: 1,
  'page_size': 20,
  'origin_type': '',
  name: ''
});
const tableData = ref([]);
const drawer = ref(false);
const dialog = ref(false);
const drawerTitle = ref('');
const editData = ref({});

userListFun();
function userListFun() {
  // userList(searchForm.value).then(res => {
  //   tableData.value = res.data.data;
  //   total.value = res.data.extra.total;
  // });
}
function statusChange() { // 列表状态修改

}
function saveClick(form) { // 角色保存
  // addUser(form).then(res => {
  //   console.log(res);
  // });
}
function saveResetClick() { // 修改密码保存

}
function handleClick(row) { // 添加角色
  console.log(row);
  editData.value = row || {};
  drawerTitle.value = row ? '编辑' : '添加';
  drawer.value = true;
}
function allocationClick(row) { // 设置密码
  dialog.value = true;
}
function delClick(row) { // 删除
  ZrMessageBox.confirm('确认删除' + row.username + '角色?', '确认操作', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      // assetsDelApi(row.db_id).then(res => {
      //   console.log(res);
      //   ZrMessage.success(res.data.msg);
      //   tableList();
      // });
    })
    .catch(() => {
    });
}
const deleteList = ref([]);
function handleSelectionChange(list) { // 列表勾选项

}
function batchDeleteClick() { // 批量删除
  ZrMessageBox.confirm('确认删除勾选的所有选项?', '确认操作', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      // batchDelete({ "db_ids": deleteList.value }).then(res => {
      //   console.log(res);
      //   ZrMessage.success(res.data.msg);
      //   tableList();
      // });
    })
    .catch(() => {
    });
}
function cancelResetClick() {
  dialog.value = false;
}
function cancelClick() {
  drawer.value = false;
}
function searchClick() { // 查询
  searchForm.value.page = 1;
  userListFun();
}
function handleSizeChange(val) { // 每页条数
  searchForm.value["page_size"] = val;
  searchForm.value.page = 1;
}
function handleCurrentChange(val) { // 当前第几页
  searchForm.value.page = val;
}

</script>
    
  
