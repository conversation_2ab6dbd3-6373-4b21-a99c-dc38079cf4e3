<template>
  <div>
    <zr-drawer
      v-bind="$attrs"
      :close-on-click-modal="false"
      destroy-on-close
      @open="draOpen"
      @close="$emit('cancelClick')"
    >
      <template #header>
        <h4
          style="color: #303133;"
        >{{ title }}角色</h4>
      </template>
      <zr-form
        ref="accountFormRef"
        :rules="rules"
        :model="accountForm"
        label-position="top"
      >
        <zr-row
          :gutter="24"
        >
          <zr-col :span="24">
            <zr-form-item
              label="角色名称"
              prop="username"
            >
              <zr-input
                v-model="accountForm.username"
                maxlength="30"
                placeholder="角色名称"
                size="default"
              />
            </zr-form-item>
          </zr-col>
          <zr-col :span="24">
            <zr-form-item
              label="角色描述"
              prop="description"
            >
              <zr-input
                v-model="accountForm.description"
                maxlength="100"
                show-word-limit
                type="textarea"
              />
            </zr-form-item>
          </zr-col>
          <zr-col :span="24">
            <zr-form-item label="启用状态">
              <zr-switch v-model="accountForm.status" />
            </zr-form-item>
          </zr-col>
        </zr-row>
      </zr-form>
      <template #footer>
        <div style="flex: auto">
          <zr-button
            type="primary"
            size="default"
            @click="saveClick(accountFormRef)"
          >
            确定
          </zr-button>
          <zr-button
            size="default"
            @click="$emit('cancelClick')"
          >
            取消
          </zr-button>
        </div>
      </template>
    </zr-drawer>
  </div>
</template>
<script setup>
import { reactive, ref, defineProps, defineEmits } from "vue";
const props = defineProps({
  title: {
    type: String
  },
  editData: {
    type: Object
  }
});
const accountFormRef = ref();
const accountForm = ref({
    
});
const roleOptions = ref([]);


function draOpen() {
  accountForm.value = {};
  console.log(accountForm.value);
}
const emits = defineEmits(['saveClick', 'cancelClick']);

const rules = reactive({
  "username": [
    { required: true,
      message: '角色名称不能为空',
      trigger: 'bulr' }
  ]
});
const saveClick = async(formEl) => {
  if (!formEl) return;
  await formEl.validate((valid, fields) => {
    if (valid) {
      emits('saveClick', accountForm.value);
    } else {
      console.log('error submit!', fields);
    }
  });
};
</script>
  <style lang="scss" scoped>
  </style>
  
