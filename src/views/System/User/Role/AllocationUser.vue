<template>
  <div>
    <zr-dialog
      v-bind="$attrs"
      :title="'设置'+'--'+'角色'"
      :width="470"
      destroy-on-close
    >
     
      <template #footer>
        <span class="dialog-footer">
          <zr-button
            type="primary"
            @click="saveClick(resetFormRef)"
          >确定</zr-button>
          <zr-button @click="$emit('cancelClick')">取消</zr-button>
        </span>
      </template>
    </zr-dialog>
  </div>
</template>
<script setup>
import { ref, defineEmits } from 'vue';
const emits = defineEmits(['saveClick', 'cancelClick']);
                                     
const resetForm = ref({});
const resetFormRef = ref();

const saveClick = async(formEl) => {
  if (!formEl) return;
  await formEl.validate((valid, fields) => {
    if (valid) {
      emits('saveClick', resetForm.value);
    } else {
      console.log('error submit!', fields);
    }
  });
};


</script>
<style lang="scss" scoped>
</style>
