<template>
  <div>
    <zr-drawer
      v-bind="$attrs"
      :close-on-click-modal="false"
      destroy-on-close
      @open="draOpen"
      @close="$emit('cancelClick')"
    >
      <template #header>
        <h4
          style="color: #303133;"
        >{{ title }}用户</h4>
      </template>
      <zr-form
        ref="accountFormRef"
        :rules="rules"
        :model="accountForm"
        label-position="top"
      >
        <!-- justify="space-between" -->
        <zr-row
          :gutter="24"
        >
          <zr-col :span="24">
            <zr-form-item
              label="用户名"
              prop="username"
            >
              <zr-input
                v-model="accountForm.username"
                maxlength="30"
                placeholder="用户名"
                size="default"
                :disabled="title=='编辑'"
              />
            </zr-form-item>
          </zr-col>
          <zr-col :span="24">
            <zr-form-item
              label="显示名称"
              prop="show_name"
            >
              <zr-input
                v-model="accountForm.show_name"
                maxlength="30"
                placeholder="显示名称"
                size="default"
              />
            </zr-form-item>
          </zr-col>
          <zr-col
            v-if="!editData"
            :span="24"
          >
            <zr-form-item
              label="密码"
              prop="password"
            >
              <zr-input
                v-model="accountForm.password"
                type="password"
                maxlength="64"
                autocomplete="new-password"
                show-password
                placeholder="密码"
                size="default"
              />
            </zr-form-item>
          </zr-col>
          <zr-col
            v-if="!editData"
            :span="24"
          >
            <zr-form-item
              label="确认密码"
              prop="password_re"
            >
              <zr-input
                v-model="accountForm.password_re"
                type="password"
                autocomplete="new-password"
                show-password
                maxlength="64"
                placeholder="确认密码"
                size="default"
              />
            </zr-form-item>
          </zr-col>
          <!-- <zr-col :span="24">
            <zr-form-item
              v-if="accountForm.role_ids=='超级管理员'"
              label="用户角色"
              prop="role_ids"
            >
              <zr-input
                v-model="accountForm.role_ids"
                placeholder="用户角色"
                disabled
                size="default"
              />
            </zr-form-item>
            <zr-form-item
              v-else
              label="用户角色"
              prop="role_ids"
            >
              <zr-select
                v-model="accountForm.role_ids"
                placeholder="用户角色"
                class="m-4"
                :options="roleOptions"
                multiple
              />
            </zr-form-item>
            
          </zr-col> -->
          <zr-col :span="24">
            <zr-form-item
              label="手机号"
              prop="phone"
            >
              <zr-input
                v-model="accountForm.phone"
                maxlength="30"
                placeholder="手机号"
                size="default"
              />
            </zr-form-item>
          </zr-col>
          <zr-col :span="24">
            <zr-form-item
              label="邮箱"
              prop="email"
            >
              <zr-input
                v-model="accountForm.email"
                maxlength="30"
                placeholder="邮箱"
                size="default"
              />
            </zr-form-item>
          </zr-col>
          <zr-col :span="24">
            <zr-form-item
              label="用户描述"
              prop="description"
            >
              <zr-input
                v-model="accountForm.description"
                placeholder="用户描述"
                show-word-limit
                maxlength="100"
                type="textarea"
              />
            </zr-form-item>
          </zr-col>
          <zr-col :span="24">
            <zr-form-item label="启用状态">
              <zr-switch
                v-model="accountForm.status"
                active-value="running"
                inactive-value="stopped"
              />
            </zr-form-item>
          </zr-col>
        </zr-row>
      </zr-form>
      <template #footer>
        <div style="flex: auto">
          <zr-button
            type="primary"
            size="default"
            @click="saveClick(accountFormRef)"
          >
            确定
          </zr-button>
          <zr-button
            size="default"
            @click="$emit('cancelClick')"
          >
            取消
          </zr-button>
        </div>
      </template>
    </zr-drawer>
  </div>
</template>
<script setup>
import { reactive, ref, defineProps, defineEmits } from "vue";
import { getValidRole } from '@/api/system/user';
import { regMustPhone, regMustEmail } from '@/utils/validate';

const props = defineProps({
  title: {
    type: String
  },
  editData: {
    type: Object
  }
});
const accountFormRef = ref();
const accountForm = ref({
  username: '',
  'show_name': '',
  password: '',
  'password_re': '',
  'role_ids': [],
  phone: '',
  email: '',
  description: '',
  status: 'running'
});
const roleOptions = ref([]);

function getValidRoleFun() {
  getValidRole().then(res => {
    roleOptions.value = res.data.data.map(item => {
      return {
        value: item.role_id,
        label: item.name
      };
    });
  }).finally(() => {
    accountForm.value = props.editData || {
      username: '',
      'show_name': '',
      password: '',
      'password_re': '',
      'role_ids': [],
      phone: '',
      email: '',
      description: '',
      status: 'running'
    };
    if (props.editData && props.editData.role_ids[0] == 'super-manager') {
      accountForm.value['role_ids'] = props.editData.role_names[0];
    }
  });
}
function draOpen() {
  getValidRoleFun();
}
const emits = defineEmits(['saveClick', 'cancelClick']);

const rules = reactive({
  "username": [
    { required: true,
      message: '用户名不能为空',
      trigger: 'bulr' }
  ],
  "show_name": [
    { required: true,
      message: '显示名称不能为空',
      trigger: 'bulr' }
  ],
  "password": [
    { required: true,
      message: '密码不能为空',
      trigger: 'bulr' }
  ],
  "password_re": [
    { required: true,
      message: '确认密码不能为空',
      trigger: 'bulr' }
  ],
  'role_ids': [
    { required: true,
      message: '用户角色不能为空',
      trigger: 'bulr' }
  ],
  "email": [
    {
      validator: regMustEmail().test, trigger: ['blur', 'change']
    }
  ],
  "phone": [
    {
      validator: regMustPhone().test, trigger: ['blur', 'change']
    }
  ]
  
});

const saveClick = async(formEl) => {
  if (!formEl) return;
  await formEl.validate((valid, fields) => {
    if (valid) {
      emits('saveClick', accountForm.value);
    } else {
      console.log('error submit!', fields);
    }
  });
};
</script>
  <style lang="scss" scoped>
    .el-select{
      width: 100%;
    }
  </style>
  
