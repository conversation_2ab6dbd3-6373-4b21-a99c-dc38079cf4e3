<template>
  <div>
    <zr-row
      :gutter="24"
      justify="space-between"
    >
      <zr-col :span="6">
        <zr-button
          type="primary"
          @click="handleClick(false)"
        >添加用户</zr-button>
        <zr-button
          type="danger"
          plain
          :disabled="deleteList.length<1"
          @click="batchDeleteClick"
        >批量删除</zr-button>
      </zr-col>
      <zr-col
        :span="18"
        style="text-align: right;"
      >
        <zr-form inline>
          <zr-form-item>
            <zr-select
              v-model="searchForm.is_origin"
              class="m-2"
              @change="searchClick"
            >
              <zr-option
                label="全部来源"
                value=""
              />
              <zr-option
                v-for="item in originOption"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </zr-select>
          </zr-form-item>
          <zr-form-item>
            <zr-input
              v-model="searchForm.name"
              placeholder="用户名"
              @keyup.enter="searchClick"
            >
              <template #append>
                <zr-button @click="searchClick">
                  <zr-icon
                    name="Search"
                  />
                </zr-button>
              </template>
            </zr-input>
          </zr-form-item>
        </zr-form>
      </zr-col>
    </zr-row>
    <zr-table
      v-loading="loading"
      :data="tableData"
      empty-text="暂无数据"
      @selection-change="handleSelectionChange"
    >
      <zr-table-column
        type="selection"
        width="55"
        :selectable="selectableFun"
      />
      <zr-table-column
        prop="username"
        label="用户名"
        width="180"
      />
      <zr-table-column
        prop="show_name"
        label="显示名称"
        width="180"
      />
      <zr-table-column
        prop="is_origin"
        label="用户来源"
        width="120"
      >
        <template #default="scope">
          {{ optionFormat(originOption,scope.row.is_origin) }}
        </template>
      </zr-table-column>
      <zr-table-column
        prop="phone"
        label="手机号码"
        width="120"
      >
        <template #default="scope">
          {{ scope.row.phone||'-' }}
        </template>
      </zr-table-column>
      <!-- <zr-table-column
        prop="role_names"
        label="用户角色"
      >
        <template #default="scope">
          <zr-tag
            v-for="item in scope.row.role_names"
            :key="item"
            style="margin: 0 0 5px 5px;"
          >{{ item }}</zr-tag>
        </template>
      </zr-table-column> -->
      <zr-table-column
        prop="description"
        label="描述"
      >
        <template #default="scope">
          <zr-tooltip
            v-if="scope.row.description&&scope.row.description!==''"
            placement="top-start"
            :content="scope.row.description"
          >
            <p class="table-item-content">{{ scope.row.description }}</p>
          </zr-tooltip>
          <p v-else>-</p>
        </template>
      </zr-table-column>
      <zr-table-column
        prop="locked_status"
        label="锁定状态"
        width="120"
      >
        <template #default="scope">
          {{ scope.row.locked_status?'已锁定':'未锁定' }}
        </template>
      </zr-table-column>
      <zr-table-column
        prop="status"
        label="启用状态"
        width="100"
      >
        <template #default="scope">
          <zr-switch
            v-model="scope.row.status"
            active-value="running"
            inactive-value="stopped"
            @click="statusChange(scope.row)"
          />
        </template>
      </zr-table-column>
      <zr-table-column
        prop="deleted_at"
        label="最近登录时间"
        width="180"
      >
        <template #default="scope">
          <zr-tag>{{ scope.row.update_time_desc }}</zr-tag>
          <p v-if="scope.row.update_time_desc!=='未登录过'">{{ timeFormat(scope.row.login_at) }}</p>
        </template>
      </zr-table-column>
      <zr-table-column
        label="操作"
        width="180"
      >
        <template #default="scope">
          <zr-button
            v-if="scope.row.locked_status"
            link
            type="primary"
            @click="lockedClick(scope.row)"
          >解锁</zr-button>
          <zr-button
            link
            type="primary"
            @click="setPassClick(scope.row)"
          >设置密码</zr-button>
          <zr-button
            link
            type="primary"
            @click="handleClick(scope.row)"
          >编辑</zr-button>
          <zr-tooltip
            class="box-item"
            effect="dark"
            :disabled="!scope.row.is_origin"
            content="内置用户不可删除"
            placement="top"
          >
            <span
              style="margin-left: 12px;"
            >
              <zr-button
                link
                type="primary"
                :disabled="scope.row.is_origin"
                @click="delClick(scope.row)"
              >删除</zr-button>
            </span>
          </zr-tooltip>
        </template>
      </zr-table-column>
    </zr-table>
    <zr-pagination
      v-if="total>0"
      :current-page="searchForm.page"
      :page-size="searchForm.page_size"
      :page-sizes="[20, 40, 60, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
    <account-drawer
      v-model="drawer"
      :title="drawerTitle"
      :edit-data="editData"
      @saveClick="saveClick"
      @cancelClick="cancelClick"
    />
    <reset-passwords
      v-model="dialog"
      :set-pass-data="setPassData"
      @cancelClick="cancelResetClick"
      @saveClick="saveResetClick"
    />
  </div>
</template>
<script setup>
import { ref } from 'vue';
import AccountDrawer from './AccountDrawer.vue';
import ResetPasswords from './ResetPasswords.vue';
import { ZrMessage, ZrMessageBox } from 'qianji-ui';
import { userList, addUser, editUser, editresetPassword, batchDelete, unlockUser } from '@/api/system/user';
import { optionFormat, timeFormat } from '@/utils/formatting';
  
const originOption = [
  {
    label: '内置',
    value: true
  },
  {
    label: '自定义',
    value: false
  }
];
const total = ref(0);
const searchForm = ref({
  'page_index': 1,
  'page_size': 20,
  'is_origin': '',
  name: ''
});
const loading = ref(false);
const tableData = ref([]);
const drawer = ref(false);
const dialog = ref(false);
const drawerTitle = ref('');
const editData = ref({});
const setPassData = ref({});

userListFun();
function userListFun() {
  loading.value = true;
  userList(searchForm.value).then(res => {
    tableData.value = res.data.data;
    total.value = res.data.extra.total;
  }).finally(() => {
    loading.value = false;
  });
}
function lockedClick(row) {
  unlockUser(row.uid).then(res => {
    ZrMessage.success('解锁成功');
    userListFun();
  });
}
function statusChange(row) { // 列表状态修改
  editUserFun(row);
}
function editUserFun(form) { // 编辑接口
  const query = {
    uid: form.uid,
    username: form.username,
    'show_name': form.show_name,
    phone: form.phone,
    email: form.email,
    description: form.description,
    status: form.status,
    'role_ids': form.role_ids
  };
  editUser(form.uid, query).then(res => {
    drawer.value = false;
    ZrMessage.success(res.data.msg);
  }).finally(() => {
    userListFun();
  });
}
function saveClick(form) { // 用户保存
  if (editData.value) {
    editUserFun(form);
  } else {
    addUser(form).then(res => {
      drawer.value = false;
      ZrMessage.success(res.data.msg);
      userListFun();
    });
  }
}
function saveResetClick(form) { // 修改密码保存
  const query = {
    username: setPassData.value.username,
    ...form
  };
  editresetPassword(query).then(res => {
    ZrMessage.success(res.data.msg);
    dialog.value = false;
    userListFun();
  });
}
function handleClick(row) { // 添加/编辑用户
  editData.value = row ? { ...row } : null;
  drawerTitle.value = row ? '编辑' : '添加';
  drawer.value = true;
}
function setPassClick(row) { // 设置密码
  setPassData.value = row;
  dialog.value = true;
}
function delClick(row) { // 删除
  ZrMessageBox.confirm('确认删除' + row.username + '用户?', '确认操作', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      batchDeleteFun([row.uid]);
    })
    .catch(() => {
    });
}

function selectableFun(row) {
  return !row.is_origin;
}
const deleteList = ref([]);
function handleSelectionChange(list) { // 列表勾选项
  deleteList.value = [];
  list.map(item => {
    deleteList.value.push(item.uid);
  });
}
function batchDeleteClick() { // 批量删除
  ZrMessageBox.confirm('确认删除勾选的所有选项?', '确认操作', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      batchDeleteFun(deleteList.value);
    })
    .catch(() => {
    });
}
function batchDeleteFun(list) {
  batchDelete({ userList: list }).then(res => {
    ZrMessage.success(res.data.msg);
    userListFun();
  });
}
function cancelResetClick() {
  dialog.value = false;
}
function cancelClick() {
  drawer.value = false;
}
function searchClick() { // 查询
  searchForm.value['page_index'] = 1;
  userListFun();
}
function handleSizeChange(val) { // 每页条数
  searchForm.value["page_size"] = val;
  searchForm.value['page_index'] = 1;
  userListFun();
}
function handleCurrentChange(val) { // 当前第几页
  searchForm.value['page_index'] = val;
  userListFun();
}

</script>

