<template>
  <div>
    <zr-dialog
      v-bind="$attrs"
      :title="'设置'+setPassData.username+'用户的密码'"
      :width="515"
      :close-on-click-modal="false"
      :destroy-on-close="true"
      @open="open"
    >
      <zr-form
        ref="resetFormRef"
        inline
        :rules="rules"
        :model="resetForm"
        label-position="top"
      >
        <zr-form-item
          label="密码"
          prop="password"
        >
          <zr-input
            v-model="resetForm.password"
            type="password"
            maxlength="64"
            show-password
            placeholder="密码"
            size="default"
          />
        </zr-form-item>
        <zr-form-item
          label="确认密码"
          prop="password_re"
        >
          <zr-input
            v-model="resetForm.password_re"
            type="password"
            maxlength="64"
            show-password
            placeholder="确认密码"
            size="default"
          />
        </zr-form-item>
      </zr-form>
      <template #footer>
        <span class="dialog-footer">
          <zr-button
            type="primary"
            @click="saveClick(resetFormRef)"
          >确定</zr-button>
          <zr-button @click="$emit('cancelClick')">取消</zr-button>
        </span>
      </template>
    </zr-dialog>
  </div>
</template>
<script setup>
import { ref, defineEmits, defineProps } from 'vue';
const emits = defineEmits(['saveClick', 'cancelClick']);
const props = defineProps({
  setPassData: {
    type: Object
  }
});

const resetForm = ref({});
const resetFormRef = ref();
const rules = reactive({
  "password": [
    { required: true,
      message: '密码不能为空',
      trigger: 'bulr' }
  ],
  "password_re": [
    { required: true,
      message: '确认密码不能为空',
      trigger: 'bulr' }
  ]
});

function open() {
  resetForm.value = {};
}
const saveClick = async(formEl) => {
  if (!formEl) return;
  await formEl.validate((valid, fields) => {
    if (valid) {
      emits('saveClick', resetForm.value);
    } else {
      console.log('error submit!', fields);
    }
  });
};


</script>
<style lang="scss" scoped>
</style>
