<template>
  <div class="main-content">
    <TabPane
      :option="tabOption"
      @handleClick="handleClick"
    />
    <Account v-if="tabNum=='1'" />
    <Role v-else />
  </div>
</template>
<script setup>
import TabPane from '@/components/common/TabPane.vue';
import Account from './Account/index.vue';
import Role from './Role/index.vue';

const tabOption = [
  {
    name: '1',
    label: '用户管理'
  }
  // {
  //   name: '2',
  //   label: '角色管理'
  // }
];
const tabNum = ref('1');

function handleClick(val) {
  tabNum.value = val;
}

</script>
    
  
