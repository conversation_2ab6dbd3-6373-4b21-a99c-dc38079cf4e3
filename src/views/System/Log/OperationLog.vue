<template>
  <div>
    <zr-row
      :gutter="24"
      style="white-space: nowrap"
    >
      <zr-col :span="4">
        <!-- <zr-button
          type="primary"
          size="default"
        >
          导出日志
        </zr-button> -->
        <!-- <zr-button
          type="warning"
          plain
          @click="batchDeleteClick"
        >删除日志</zr-button> -->
        <zr-button
          type="danger"
          plain
          @click="clearClick"
        >清空日志</zr-button>
      </zr-col>
      <zr-col
        :span="20"
        style="text-align:right"
      >
        <zr-form :inline="true">
          <zr-form-item>
            <zr-date-picker
              v-model="timeArr"
              type="datetimerange"
              :shortcuts="shortcuts"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              style="width:360px"
              @change="dateChange"
            />
          </zr-form-item>
          <zr-form-item>
            <zr-select
              v-model="searchForm.user_name"
              @change="searchClick"
            >
              <zr-option
                label="全部用户"
                value=""
              />
              <zr-option
                v-for="item in userName"
                :key="item"
                :value="item.name"
                :label="item.name"
              />
            </zr-select>
          </zr-form-item>
          <zr-form-item>
            <zr-select
              v-model="searchForm.operation"
              @change="searchClick"
            >
              <zr-option
                label="全部操作"
                value=""
              />
              <zr-option
                v-for="item in operation"
                :key="item"
                :value="item.name"
                :label="item.name"
              />
            </zr-select>
          </zr-form-item>
          <zr-form-item>
            <zr-select
              v-model="searchForm.is_success"
              @change="searchClick"
            >
              <zr-option
                label="全部操作结果"
                value=""
              />
              <zr-option
                v-for="item in isSuccess"
                :key="item"
                :value="item.name"
                :label="item.name==true?'成功':'失败'"
              />
            </zr-select>
          </zr-form-item>
          <!-- <zr-form-item>
            <zr-input
              v-model="searchForm.ip_addr"
              placeholder="登录IP地址"
            >
              <template #append>
                <zr-button
                  @click="searchClick"
                >
                  <icon name="zr-search" />
                </zr-button>
              </template>
            </zr-input>
          </zr-form-item> -->
        </zr-form>
      </zr-col>
    </zr-row>
    <!-- 列表 -->
    <zr-table
      v-loading="loading"
      :data="tableData"
      empty-text="暂无数据"
      size="default"
      style="width: 100%"
    >
      <!-- <zr-table-column
        type="selection"
        width="55"
      /> -->
      <zr-table-column
        prop="operation_time"
        label="时间"
      />
      <zr-table-column
        prop="user_name"
        label="用户名"
      />
      <zr-table-column
        prop="ip_addr"
        label="IP地址"
      />
      <zr-table-column
        prop="operation"
        label="操作"
      />
      <zr-table-column
        prop="description"
        label="说明"
        width="450"
      />
      <zr-table-column
        prop="is_success"
        label="结果"
        width="200"
      >
        <template #default="scope">
          <zr-tag
            :type="scope.row.is_success==true?'success':'danger'"
          >
            <Icon
              :name="scope.row.is_success==true?'zr-circle-seleted-fill':'zr-circle-close'"
            />
            {{ scope.row.is_success==true?'成功':'失败' }}
          </zr-tag>
        </template>
      </zr-table-column>
    </zr-table>
    <zr-pagination
      :current-page="searchForm.page"
      :page-size="searchForm.page_size"
      :page-sizes="[20, 40, 60, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>
<script setup>
import { ref, onMounted } from 'vue';
import { ZrMessageBox, ZrMessage } from 'qianji-ui';
import { logsList, batchDelete, clearAll, options } from '@/api/system/log';
  
const total = ref(0);
const loading = ref(false);
const tableData = ref([]); // 列表数据
const searchForm = ref({
  'user_name': '',
  operation: '',
  'is_success': '',
  'ip_addr': '',
  page: 1,
  'page_size': 20,
  'start_time': '',
  'end_time': '',
  'business_type': 'mfp'
});

// 时间
const formPicker = ref({});
const timeArr = ref([]);
onMounted(() => {
  formPicker.value = sessionStorage.getItem('formPicker');
  const start = new Date();
  start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
  const startDate = start.getTime();
  const endDate = new Date().getTime();
  timeArr.value = formPicker.value ? JSON.parse(formPicker.value).time : [startDate, endDate];
  searchForm.value['start_time'] = parseInt(timeArr.value[0] / 1000);
  searchForm.value['end_time'] = parseInt(timeArr.value[1] / 1000);
});
const shortcuts = [
  {
    text: '最近7天',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    }
  },
  {
    text: '最近30天',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    }
  },
  {
    text: '最近90天',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
      return [start, end];
    }
  }
];

function dateChange(val) { // 时间组件：点击确定时触发
  searchForm.value['start_time'] = parseInt(val[0].getTime() / 1000);
  searchForm.value['end_time'] = parseInt(val[1].getTime() / 1000);
  logsListFun();
}

// 查询筛选项
const isSuccess = ref([]);
const operation = ref([]);
const userName = ref([]);
optionsFun();
function optionsFun() {
  options({ 'business_type': 'mfp' }).then(res => {
    isSuccess.value = res.data.data.is_success;
    operation.value = res.data.data.operation;
    userName.value = res.data.data.user_name;
  });
}


// 查询日志列表
logsListFun();
function logsListFun() {
  loading.value = true;
  logsList(searchForm.value).then(res => {
    tableData.value = res.data.data.list;
    total.value = res.data.data.count;
  }).finally(() => {
    loading.value = false;
  });
}


// 批量删除日志
// const deleteList = ref([]);
// function handleSelectionChange(val) { // 列表勾选框
//   deleteList.value = [];
//   val.map(item => {
//     deleteList.value.push(item.log_id);
//   });
// }

// function batchDeleteClick() {
//   ZrMessageBox.confirm('确认删除勾选的所有选项?', '确认操作', {
//     confirmButtonText: '确定',
//     cancelButtonText: '取消',
//     type: 'warning'
//   }).then(() => {
//     batchDelete({ 'ids': deleteList.value, 'business_type': 'mfp' }).then(res => { // 批量删除日志接口
//       ZrMessage.success("删除日志成功");
//       logsListFun();
//     });
//   }).catch(() => {
//     ZrMessage.error(res.data.msg);
//   });
// }

function clearClick() { // 清空日志
  ZrMessageBox.confirm('确认清空所有的日志？', '确认操作', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    clearAll({ 'business_type': 'mfp' }).then(res => { // 清空日志接口
      ZrMessage.success("清空日志成功");
      logsListFun();
    });
  }).catch(() => {
    ZrMessage.error(res.data.msg);
  });
}

function searchClick() { // 搜索
  logsListFun();
}
  
function handleSizeChange(val) { // 每页多少条
  searchForm.value['page_size'] = val;
  searchForm.value.page = 1;
  logsListFun();
}
  
function handleCurrentChange(val) { // 当前第几页
  searchForm.value.page = val;
  logsListFun();
}
</script>
<style lang="scss" scoped>
:deep(.el-table--enable-row-transition .el-table__body td.el-table__cell){
  height:48px
}


</style>
  
