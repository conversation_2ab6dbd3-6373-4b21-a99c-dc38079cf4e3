<template>
  <div class="main-content">
    <TabPane
      :option="tabOption"
      @handleClick="handleClick"
    />
    <LoginLog v-if="tabName == '1'" />
    <OperationLog v-else />
  </div>
</template>
<script setup>
import TabPane from '@/components/common/TabPane.vue';
import LoginLog from './LoginLog.vue';
import OperationLog from './OperationLog.vue';

const tabName = ref('1');
const tabOption = [
  {
    label: '登录日志',
    name: '1'
  },
  {
    label: '操作日志',
    name: '2'
  }
];

function handleClick(activeName) {
  tabName.value = activeName;
}
</script>
<style lang="scss" scoped>
.main-content{
  white-space: nowrap;
}
</style>
    
  
