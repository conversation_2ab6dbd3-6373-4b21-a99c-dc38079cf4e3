<template>
  <div>
    <zr-drawer
      v-bind="$attrs"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      @open="draOpen"
    >
      <template #header>
        <h4 style="color: #303133;">
          {{ title }}
        </h4>
      </template>
      <p
        class="titleStyle"
      >基本信息</p>
      <zr-form
        ref="formRef"
        label-position="top"
        :model="form"
        :rules="rules"
      >
        <zr-form-item
          label="名称"
          prop="name"
        >
          <zr-input
            v-model="form.name"
            maxlength="50"
            show-word-limit
            placeholder="请输入名称,1-50位"
          />
        </zr-form-item>
        <zr-form-item
          prop="os"
          label="操作系统"
        >
          <zr-select
            v-model="form.os"
          >
            <zr-option
              label="Windows"
              value="Windows"
            />
            <zr-option
              label="Linux"
              value="Linux"
            />
          </zr-select>
        </zr-form-item>
        <zr-form-item
          prop="kernel_version"
          label="内核版本"
        >
          <zr-input
            v-model="form.kernel_version"
            maxlength="50"
            show-word-limit
            placeholder="请输入内核版本,1-50位"
          />
        </zr-form-item>
        <zr-form-item
          prop="description"
          label="描述"
        >
          <zr-input
            v-model="form.description"
            type="textarea"
            maxlength="500"
            show-word-limit
            placeholder="请输入描述,0-500位"
          />
        </zr-form-item>
      </zr-form>
      <p
        class="titleStyle"
      >上传文件</p>
      <zr-upload
        ref="upload"
        drag
        multiple
        :limit="1"
        action="#"
        :auto-upload="false"
        :on-exceed="handleExceed"
        :on-change="handleChange"
        :on-remove="handleRemove"
        :headers="headersObj"
      >
        <template #trigger>
          <Icon
            name="zr-cloud-upload-fill-b"
            class="el-icon--upload"
          />
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        </template>
        <template #tip>
          <div class="el-upload__tip">支持zip文件</div>
        </template>
      </zr-upload>
      <template #footer>
        <zr-button
          :loading="loading"
          type="primary"
          size="default"
          @click="submit(formRef)"
        >
          确定
        </zr-button>
        <zr-button
          size="default"
          @click="cancelClick"
        >
          取消
        </zr-button>
      </template>
    </zr-drawer>
  </div>
</template>
  
<script setup>
import { ref, reactive, defineEmits, defineProps } from 'vue';
import { ZrMessage } from 'qianji-ui';
import { addSymbols, editsymbols } from '@/api/rule/symbol';
import { genFileId } from 'element-plus';
import { useCookies } from 'vue3-cookies';
const { cookies } = useCookies();
// const uploadUrl = window.BASE_URL + '/mfp-service/api/v1/symbols'; // 拼接url地址
  

const emit = defineEmits(['cancelClick', 'saveClick']);
const props = defineProps({
  title: {
    type: String
  },
  rowID: {
    type: String
  },
  drawerData: {
    type: Object
  }
});
const loading = ref(false);
const formRef = ref();
const rules = reactive({
  name: [
    { required: true, message: '请输入名称', trigger: 'blur' },
    { min: 1, max: 50, message: '长度不超过50', trigger: 'blur' }
    // { pattern: /^[\u4e00-\u9fa5a-zA-Z0-9]+$/, message: '只能输入字母、数字、汉字', trigger: 'blur' }
  ],
  'kernel_version': [
    { required: true, message: '请输入内核版本', trigger: 'blur' },
    { min: 1, max: 50, message: '长度不超过50', trigger: 'blur' }
    // { pattern: /^[a-zA-Z0-9.-]+$/, message: '只能输入字母 数字 . -', trigger: 'blur' }
  ]
});
const form = ref({
  name: '',
  os: 'Windows',
  'kernel_version': '',
  description: '',
  file: {}
});

watch(() => props.drawerData, (newVal) => { // 监听打开弹窗数据（防止第一次打开无数据，第二次打开才有数据）
  form.value.name = newVal.Name;
  form.value.os = newVal.Os || 'Windows';
  form.value['kernel_version'] = newVal.KernelVersion;
  form.value.description = newVal.Description;
}, {
  deep: true,
  immediate: true
}
);

const getFile = ref({});
function draOpen() { // 打开弹窗
  loading.value = false;
  getFile.value = {}; // 添加时,上传文件为空； 编辑时：如果不重新上传文件则传空，如果重新上传则传文件
}
const headersObj = { // 上传文件时传token
  'token': cookies.get('dsp_token')
};

const handleChange = (response, uploadFile, uploadFiles) => { // 上传文件状态改变时
  getFile.value = response.raw; // 取文件信息
};

function handleRemove() { // 移除文件
  getFile.value = {};
}

const upload = ref();
const handleExceed = (files) => { // 上传的文件超出限制的个数
  upload.value.clearFiles();
  const file = files[0];
  file.uid = genFileId();
  upload.value.handleStart(file);
};

function validateFun() {
  const fileType = ["application/x-zip-compressed", "application/zip"]; // zip类型
  const zipFileType = fileType.includes(getFile.value.type);
  if (!zipFileType) {
    ZrMessage.error('只支持zip格式文件');
    loading.value = false;
    return false;
  }
  return true;
}

const submit = (formEl) => {
  if (!formEl) return;
  formEl.validate(async(valid) => {
    if (valid) {
      loading.value = true;
      const data = new FormData();
      data.append('file', getFile.value);
      data.append('name', form.value.name);
      data.append('os', form.value.os);
      data.append('kernel_version', form.value.kernel_version);
      data.append('description', form.value.description || '');
      if (props.title == '添加Symbol文件') {
        if (Object.keys(getFile.value).length == 0) {
          ZrMessage.error('请上传symbol文件');
          loading.value = false;
          return;
        }
        if (!validateFun()) {
          return;
        }
        addSymbols(data).then(res => { // 添加接口
          if (res.data.code == 0) {
            ZrMessage.success('添加Symbol文件成功');
          }
          emit('saveClick');
        }).catch(() => {
          loading.value = false;
        });
      } else {
        if (Object.keys(getFile.value).length !== 0) {
          if (!validateFun()) {
            return;
          }
        }
        editsymbols(props.rowID, data).then(res => { // 编辑接口
          if (res.data.code == 0) {
            ZrMessage.success('修改Symbol文件成功');
          } else {
            ZrMessage.error(res.data.message);
          }
          loading.value = false;
          emit('saveClick');
        }).catch(() => {
          loading.value = false;
        });
      }
    } else {
      return false;
    }
  });
};

function cancelClick() {
  emit('cancelClick');
}
</script>
  <style lang="scss" scoped>
  .titleStyle{
  font-size: 14px;
  font-weight: 800;
  margin-bottom: 10px;
}

  </style>
  
