<template>
  <!-- Symbol文件管理 -->
  <div class="main-content">
    <zr-row
      :gutter="24"
      justify="space-between"
      style="white-space: nowrap"
    >
      <zr-col :span="8">
        <zr-button
          type="primary"
          size="default"
          @click="openDrawerClick('add')"
        >添加Symbol文件</zr-button>
      </zr-col>
      <zr-col
        :span="16"
        style="text-align:right"
      >
        <zr-form
          :inline="true"
          :model="searchForm"
        >
          <zr-form-item>
            <zr-select
              v-model="searchForm.os"
              size="default"
              @change="searchClick"
            >
              <zr-option
                v-for="item in OsOptions"
                :key="item"
                :label="item.label + '('+ item.num + ')' "
                :value="item.value"
              >{{ item.label }}({{ item.num }})
              </zr-option>
            </zr-select>
          </zr-form-item>
          <zr-form-item>
            <zr-select
              v-model="searchForm.is_built_in"
              size="default"
              @change="searchClick"
            >
              <zr-option
                v-for="item in isBuiltInOptions"
                :key="item"
                :label="item.label + '('+ item.num + ')' "
                :value="item.value+''"
              >{{ item.label }}({{ item.num }})
              </zr-option>
            </zr-select>
          </zr-form-item>
          <zr-form-item>
            <zr-input
              v-model="searchForm.params"
              placeholder="名称/内核版本"
              size="default"
              @keydown.enter.prevent="searchClick"
            >
              <template #append>
                <zr-button
                  @click="searchClick"
                >
                  <icon name="zr-search" />
                </zr-button>
              </template>
            </zr-input>
          </zr-form-item>
        </zr-form>

      </zr-col>
    </zr-row>
    <zr-table
      v-loading="loading"
      :data="tableData"
      :default-sort="{ prop: 'created_at', order: 'descending' }"
      empty-text="暂无数据"
      @sort-change="sortChange"
    >
      <zr-table-column
        label="名称"
        prop="Name"
      />
      <zr-table-column
        label="操作系统"
        prop="Os"
        width="120"
        show-overflow-tooltip
      >
        <template #default="scope">
          <span :class="scope.row.os=='Linux' ?'':'system-icon-style'">
            <SystemIcon
              v-if="scope.row.Os =='Linux' ? 'linux' :'windows'"
              :name="scope.row.Os =='Linux' ? 'linux' :'windows'"
            />
          </span>
          
        </template>
      </zr-table-column>
      <zr-table-column
        label="内核版本"
        prop="KernelVersion"
      />
      <zr-table-column
        label="描述"
        prop="Description"
        width="250"
      >
        <template #default="scope">
          <zr-tooltip
            v-if="scope.row.Description&&scope.row.Description!==''"
            placement="top-start"
            :content="scope.row.Description"
          >
            <p class="table-item-content">{{ scope.row.Description }}</p>
          </zr-tooltip>
          <p v-else>-</p>
        </template>
      </zr-table-column>
      <zr-table-column
        label="上传时间"
        prop="created_at"
        width="180px"
        sortable
      >
        <template #default="scope">
          <div v-if="scope.row.created_at || scope.row.created_at_ago">
            <zr-tag v-if="scope.row.created_at_ago">{{ scope.row.created_at_ago }}</zr-tag>
            <p>{{ timeFormat(scope.row.created_at) }}</p>
          </div>
          <div v-else>-</div>
        </template>
      </zr-table-column>
      <zr-table-column
        label="修改时间"
        prop="updated_at"
        width="180px"
        sortable
      >
        <template #default="scope">
          <div v-if="scope.row.updated_at || scope.row.updated_at_ago">
            <zr-tag v-if="scope.row.updated_at_ago">{{ scope.row.updated_at_ago }}</zr-tag>
            <p>{{ timeFormat(scope.row.updated_at) }}</p>
          </div>
          <div v-else>-</div>
        </template>
      </zr-table-column>
      <zr-table-column
        label="来源"
        prop="IsBuiltIn"
        width="100px"
      >
        <template #default="scope">
          {{ scope.row.IsBuiltIn ? '内置':'自定义' }}
        </template>
      </zr-table-column>
      <zr-table-column
        label="操作"
        prop=""
        width="150px"
      >
        <template #default="scope">
          <zr-button
            type="primary"
            link
            :disabled="scope.row.IsBuiltIn"
            @click="openDrawerClick('edit',scope.row)"
          >编辑</zr-button>
          <zr-button
            type="primary"
            link
            :disabled="scope.row.IsBuiltIn"
            @click="downLoadClick(scope.row)"
          >下载</zr-button>
          <zr-button
            type="primary"
            link
            :disabled="scope.row.IsBuiltIn||!scope.row.CanDelete"
            @click="deleteClick(scope.row)"
          >删除</zr-button>
        </template>
      </zr-table-column>
    </zr-table>
    <zr-pagination
      :current-page="searchForm.page_index"
      :page-size="searchForm.page_size"
      :page-sizes="[20, 40, 60, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
    <!-- 添加/编辑内容 -->
    <drawer
      v-model="openDrawer"
      :title="drawerTitle"
      :row-i-d="rowID"
      :drawer-data="drawerData"
      @saveClick="saveClick"
      @cancelClick="cancelClick"
    />
  </div>
</template>
<script setup>
import { ref } from 'vue';
import drawer from './drawer.vue';
import { SystemIcon, ZrMessage, ZrMessageBox } from 'qianji-ui';
import { timeFormat } from '@/utils/formatting';
import axios from 'axios';
import { useCookies } from 'vue3-cookies';
import { getOsOptions, getTypeOptions, symbolsList, symbolsDetail, deletesymbols, downloadsymbol } from '@/api/rule/symbol';
const { cookies } = useCookies();

const drawerTitle = ref('');
const openDrawer = ref(false);
const drawerData = ref({}); // 弹窗数据
const searchForm = ref({
  os: '',
  params: '',
  'is_built_in': '',
  sort: '-created_at',
  'page_index': 1,
  'page_size': 20
});
const isBuiltInOptions = ref([]);
const loading = ref(false);
const total = ref(0);
const rowID = ref('');
const tableData = ref([]);
const OsOptions = ref([]);

// 搜索项下拉内容
getOsOptionsFun();
function getOsOptionsFun() {
  getOsOptions().then(res => {
    OsOptions.value = res.data.data.data;
  });
}
getTypeOptionsFun();
function getTypeOptionsFun() {
  getTypeOptions().then(res => {
    isBuiltInOptions.value = res.data.data.data;
  });
}

SymbolsListFun();
function SymbolsListFun() { // 列表
  symbolsList(searchForm.value).then(res => {
    tableData.value = res.data.data.list;
    total.value = res.data.data.total;
  });
}
function openDrawerClick(type, row) { // 添加/编辑任务
  drawerTitle.value = type == 'add' ? '添加Symbol文件' : '编辑Symbol文件';
  openDrawer.value = true;
  if (type == 'edit') {
    nextTick(() => {
      symbolsDetail(row.Id).then(res => {
        if (res.data.code == 0) {
          drawerData.value = res.data.data;
          rowID.value = res.data.data.Id;
        } else {
          drawerData.value = {};
        }
      });
    });
  } else {
    drawerData.value = {};
  }
}
function searchClick() { // 搜索
  searchForm.value['page_index'] = 1;
  SymbolsListFun();
}
function downLoadClick(row) { // 下载
  console.log(cookies.get('dsp_token'), 'cookies.get');
  axios({
    url: window.BASE_URL + '/mfp-service/api/v1/symbols/' + row.Id + '/download',
    method: 'get',
    responseType: 'blob',
    headers: { Token: cookies.get('dsp_token') }
  }).then((res) => {
    if (res.data.type == 'application/octet-stream') {
      const filename = row.MinioPath.substring(row.MinioPath.lastIndexOf("/") + 1);
      const blob = new Blob([res.data], { type: 'application/zip' }); // 返回的文件流数据
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', filename); // 默认使用初始文件名
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  });
}
function deleteClick(row) { // 删除
  ZrMessageBox.confirm('确认删除?', '确认操作', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      deletesymbols(row.Id).then(res => {
        ZrMessage.success('删除成功');
        SymbolsListFun();
        getOsOptionsFun();
        getTypeOptionsFun();
      });
    })
    .catch(() => {
    });
}
function handleSizeChange(val) {
  searchForm.value['page_size'] = val;
  searchForm.value['page_index'] = 1;
  SymbolsListFun();
}
function handleCurrentChange(val) {
  searchForm.value['page_index'] = val;
  SymbolsListFun();
}
function saveClick(form) {
  openDrawer.value = false;
  SymbolsListFun();
  getOsOptionsFun();
  getTypeOptionsFun();
}
function cancelClick() {
  openDrawer.value = false;
}
function sortChange(data) { // 更新时间 排序
  const { prop, order } = data;
  if (order === 'ascending') {
    searchForm.value.sort = '+' + prop;
  } else {
    searchForm.value.sort = '-' + prop;
  }
  SymbolsListFun();
}
</script>

<style lang="scss" scoped>
.zr-dropdown-link {
  cursor: pointer;
  color: var(--el-color-primary);
  display: flex;
  align-items: center;
  margin-left: 10px
}
.buttonStyle{
  display:flex;
  align-items:center ;
}
</style>
    
  
