<template>
  <div>
    <!-- 内置详情 -->
    <div
      class="main-content"
      style="background: #f5f7fa"
    >
      <div class="back">
        <zr-button
          type="primary"
          size="default"
          left-icon="zr-left"
          @click="backClick"
        >
          返回
        </zr-button>
        <h2>{{ basicList.name }}-模板详情</h2>
      </div>
      <div class="top">
        <zr-row :gutter="24">
          <zr-col
            v-for="(item,index) in basicMessage"
            :key="item"
            :span="index==7 ? 24 : 8"
          >
            <p>{{ item.name }}</p>
            <p v-if="item.value==='updated_at'">{{ timeFormat(basicList[item.value]) }} <zr-tag
              v-if="basicList['updated_time_ago']"
              style="height: 20px;"
            >{{ basicList['updated_time_ago'] }}</zr-tag></p>
            <p v-else-if="item.value==='source'">{{ basicList[item.value]==='built_in'?'内置模板':'自定义模板' }}</p>
            <p v-else>{{ basicList[item.value] }}</p>
          </zr-col></zr-row>
      </div>
    </div>
    <div class="main-content">
      <zr-row
        :gutter="24"
        justify="space-between"
        style="white-space: nowrap"
      >
        <zr-col
          style="text-align:right"
        >
          <zr-form :inline="true">
            <zr-form-item>
              <zr-select
                v-model="searchForm.level"
                size="default"
                @change="searchClick"
              >
                <zr-option
                  v-for="item in levelOptions"
                  :key="item"
                  :label="item.label + '('+ item.num + ')' "
                  :value="item.value"
                >{{ item.label }}({{ item.num }})
                </zr-option>
              </zr-select>
            </zr-form-item>
            <zr-form-item>
              <zr-input
                v-model="searchForm.name"
                placeholder="模板名称"
                size="default"
              >
                <template #append>
                  <zr-button
                    @click="searchClick"
                  >
                    <icon name="zr-search" />
                  </zr-button>
                </template>
              </zr-input>
            </zr-form-item>
          </zr-form>
        </zr-col>
      </zr-row>
      <zr-table
        v-loading="loading"
        :data="tableData"
        row-key="id"
        empty-text="暂无数据"
      >
        <zr-table-column type="expand">
          <template #default="scope">
            <div class="detail-msg">
              <MonacoEditor
                :monaco-val="scope.row.source==='built_in' ? metaFun(scope.row.meta) : scope.row.raw"
                :read-only="true"
                style="height: 480px;"
              />
            </div>
          </template>
        </zr-table-column>
        <zr-table-column
          label="规则名称"
          prop="title"
          show-overflow-tooltip
        />
        <zr-table-column
          label="级别"
          prop="level"
          width="80"
          show-overflow-tooltip
        >
          <template #default="scope">
            <span :style="'color:' + levelColor(scope.row.level)">
              {{ levelChinese(scope.row.level) }}
            </span>
          </template>
        </zr-table-column>
        <zr-table-column
          label="适用平台"
          prop="os"
          width="100"
          show-overflow-tooltip
        >
          <template #default="scope">
            <span :class="scope.row.os=='Linux' ?'':'system-icon-style'">
              <SystemIcon
                v-if="scope.row.os =='Linux' ? 'linux' :'windows'"
                :name="scope.row.os =='Linux' ? 'linux' :'windows'"
              />
            </span>
          </template>
        </zr-table-column>
        <zr-table-column
          label="标签"
          prop="tag"
        >
          <template #default="scope">
            <zr-scrollbar
              v-if="scope.row.tag"
              :wrap-class="['scrollbar-wrapper', 'wrapper-custom']"
              :wrap-style="[{ 'max-height': '50px', height: '50px'}]"
            >
              <zr-tag
                v-for="item in scope.row.tag.split(';')"
                :key="item"
              >{{ item }}</zr-tag>
            </zr-scrollbar>
          </template>
        </zr-table-column>
        <zr-table-column
          label="描述"
          prop="description"
          show-overflow-tooltip
        />
        <zr-table-column
          label="修订时间"
          prop="last_updated"
          show-overflow-tooltip
          width="120"
        >
          <template #default="scope">
            <div>
              <zr-tag v-if="scope.row.last_updated_at">{{ scope.row.last_updated_at }}</zr-tag>
              <p>{{ scope.row.last_updated }}</p>
            </div>
          </template>
        </zr-table-column>
      </zr-table>
    </div>
    <zr-pagination
      v-if="total>0"
      :current-page="searchForm.page"
      :page-size="searchForm.page_size"
      :page-sizes="[20, 40, 60, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>
<script setup>
import { ref, reactive, watch, defineEmits, defineProps } from 'vue';
import { builtInTemplate, isBuiltInLevelOptions, yaraRulesList } from '@/api/rule/yara/ruleTemplate';
import { timeFormat } from '@/utils/formatting';
import MonacoEditor from '@/components/common/MonacoEditor/index.vue';

const emit = defineEmits(['backClick']);
const props = defineProps({
  detailItem: Object
});

const basicMessage = reactive([
  {
    name: '模板名称：',
    value: 'name'
  },
  {
    name: '来源：',
    value: 'source'
  },
  {
    name: '更新时间：',
    value: 'updated_at'
  },
  {
    name: '规则数量：',
    value: 'rule_count'
  },
  {
    name: '引用次数：',
    value: 'referenced_count'
  },
  {
    name: '',
    value: ''
  },
  {
    name: '模版描述：',
    value: 'description'
  }
]);
const basicList = ref({});
const searchForm = reactive({
  name: '',
  level: '',
  source: '',
  'page_size': 20,
  page: 1
});
const loading = ref(false);
const total = ref(0);
const tableData = ref([]);
const levelOptions = ref([]);
watch(() => props.detailItem, (newValue) => {
  listFun();
  isBuiltInLevelOptionsFun();
  yaraRulesListFun();
}, {
  immediate: true
});

function yaraRulesListFun() {
  yaraRulesList(props.detailItem.id, searchForm).then(res => {
    tableData.value = res.data.data.list || [];
    total.value = res.data.data.count;
  });
}
function isBuiltInLevelOptionsFun() {
  isBuiltInLevelOptions(props.detailItem.id).then(res => {
    levelOptions.value = res.data.data.data;
  });
}
function listFun() {
  builtInTemplate(props.detailItem.id).then(res => {
    basicList.value = res.data.data;
  });
}
function searchClick() {
  searchForm.page = 1;
  yaraRulesListFun();
}
function metaFun(meta) {
  var k = meta.slice(1, -1);
  var val = k.replaceAll('","', '"\n"');
  return val;
}
function handleSizeChange(val) {
  searchForm["page_size"] = val;
  searchForm.page = 1;
  yaraRulesListFun();
}
function handleCurrentChange(val) {
  searchForm.page = val;
  yaraRulesListFun();
}
const levelColor = (level) => { // 级别颜色
  if (level == 'low') return '#409EFF';
  if (level == 'medium') return '#ffd700';
  if (level == 'high') return '#E6A23C';
  if (level == 'critical') return '#F56C6C';
};
const levelChinese = (level) => { // 级别
  if (level == 'low') return '低';
  if (level == 'medium') return '中';
  if (level == 'high') return '高';
  if (level == 'critical') return '致命';
};
function backClick() { // 返回
  emit('backClick');
}
</script>
<style lang="scss" scoped>
  .back{
    margin-bottom: 30px;
    display: flex;
    h2{
      margin-left: 30px;
      font-weight: 500;
    }
  }
  .top{
    .el-col{
        font-size: 14px;
        display: flex;
        align-items: center;
        line-height: 30px;
    }
  }
</style>
