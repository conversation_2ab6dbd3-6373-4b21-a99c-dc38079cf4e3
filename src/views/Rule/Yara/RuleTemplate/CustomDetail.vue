<template>
  <!-- 自定义规则 详情页 -->
  <div>
    <div
      class="main-content"
      style="background: #f5f7fa"
    >
      <div class="back">
        <zr-button
          type="primary"
          size="default"
          left-icon="zr-left"
          @click="backClick"
        >
          返回
        </zr-button>
        <h2>{{ basicList.name }}-模板详情</h2>
      </div>
      <div class="top">
        <zr-row :gutter="24">
          <zr-col
            v-for="(item,index) in basicMessage"
            :key="item"
            :span="index==7 ? 24 : 8"
          >
            <p>{{ item.name }}</p>
            <p v-if="item.value==='updated_at'||item.value==='created_at'">{{ timeFormat(basicList[item.value]) }} <zr-tag
              v-if="basicList['updated_time_ago']||basicList['created_time_ago']"
              style="height: 20px;"
            >{{ item.value==='updated_at'?basicList['updated_time_ago']:basicList['created_time_ago'] }}</zr-tag></p>
            <p v-else-if="item.value==='source'">{{ basicList[item.value]==='built_in'?'内置模板':'自定义模板' }}</p>
            <div
              v-else-if="item.value==='name'"
              class="edit-msg"
            >
              <zr-input
                v-if="nameBool"
                v-model="name"
              />
              <span v-else>{{ basicList.name }}</span>
              <zr-button
                type="primary"
                link
                :left-icon="!nameBool?'zr-square-edit-fill':''"
                style="margin-left: 8px;"
                @click="nameClick"
              >{{ nameBool?'保存':'' }}</zr-button>
              <zr-button
                v-if="nameBool"
                type="info"
                link
                @click="nameBool=false"
              >取消</zr-button>
            </div>
            <div
              v-else-if="item.value==='description'"
              class="edit-msg"
            >
              <zr-input
                v-if="descriptionBool"
                v-model="description"
              />
              <span v-else>{{ basicList.description }}</span>
              <zr-button
                type="primary"
                link
                style="margin-left: 8px;"
                :left-icon="!descriptionBool?'zr-square-edit-fill':''"
                @click="descriptionClick"
              >{{ descriptionBool?'保存':'' }}</zr-button>
              <zr-button
                v-if="descriptionBool"
                type="info"
                link
                @click="descriptionBool=false"
              >取消</zr-button>
            </div>
            <p v-else>{{ basicList[item.value] }}</p>
          </zr-col></zr-row>
      </div>
    </div>
    <div class="main-content">
      <zr-row
        :gutter="24"
        justify="space-between"
        style="white-space: nowrap"
      >
        <zr-col :span="8">
          <zr-button
            type="primary"
            size="default"
            @click="importRulesClick()"
          >引入规则</zr-button>
          <zr-button
            type="danger"
            size="default"
            plain
            :disabled="ids.length===0"
            @click="batchDeteleClick()"
          >批量删除</zr-button>
        </zr-col>
        <zr-col
          :span="16"
          style="text-align:right"
        >
          <zr-form :inline="true">
            <zr-form-item>
              <zr-select
                v-model="searchForm.level"
                size="default"
                @change="searchClick"
              >
                <zr-option
                  v-for="item in levelOptions"
                  :key="item"
                  :label="item.label + '('+ item.num + ')' "
                  :value="item.value"
                >{{ item.label }}({{ item.num }})
                </zr-option>
              </zr-select>
            </zr-form-item>
            <zr-form-item>
              <zr-select
                v-model="searchForm.source"
                size="default"
                @change="searchClick"
              >
                <zr-option
                  v-for="item in sourceOption"
                  :key="item"
                  :label="item.label + '('+ item.num + ')' "
                  :value="item.value"
                >{{ item.label }}({{ item.num }})
                </zr-option>
              </zr-select>
            </zr-form-item>
            <zr-form-item>
              <zr-input
                v-model="searchForm.name"
                placeholder="规则名称/描述/标签"
                size="default"
              >
                <template #append>
                  <zr-button
                    @click="searchClick"
                  >
                    <icon name="zr-search" />
                  </zr-button>
                </template>
              </zr-input>
            </zr-form-item>
          </zr-form>
        </zr-col>
      </zr-row>
      <zr-table
        v-loading="loading"
        :data="tableData"
        row-key="id"
        empty-text="暂无数据"
        @selection-change="handleSelectionChange"
      >
        <zr-table-column
          type="expand"
          width="30"
        >
          <template #default="scope">
            <div class="detail-msg">
              <MonacoEditor
                :monaco-val="scope.row.source==='built_in' ? metaFun(scope.row.meta) : scope.row.raw"
                :read-only="true"
                style="height: 480px;"
              />
            </div>
          </template>
        </zr-table-column>
        <zr-table-column
          type="selection"
          width="30"
        />
        <zr-table-column
          label="规则名称"
          prop="title"
          show-overflow-tooltip
        />
        <zr-table-column
          label="类型"
          prop="source"
          show-overflow-tooltip
          width="80"
        >
          <template #default="scope">
            {{ scope.row.source==='built_in'?'内置':'自定义' }}
          </template>
        </zr-table-column>
        <zr-table-column
          label="级别"
          prop="level"
          width="80"
          show-overflow-tooltip
        >
          <template #default="scope">
            <span :style="'color:' + levelColor(scope.row.level)">
              {{ levelChinese(scope.row.level) }}
            </span>
          </template>
        </zr-table-column>
        <zr-table-column
          label="适用平台"
          prop="os"
          width="100"
          show-overflow-tooltip
        >
          <template #default="scope">
            <span :class="scope.row.os=='Linux' ?'':'system-icon-style'">
              <SystemIcon
                v-if="scope.row.os =='Linux' ? 'linux' :'windows'"
                :name="scope.row.os =='Linux' ? 'linux' :'windows'"
              />
            </span>
          </template>
        </zr-table-column>
        <zr-table-column
          label="文件类型"
          prop="file_type"
          show-overflow-tooltip
          width="80"
        />
        <zr-table-column
          prop="tag"
          label="标签"
        >
          <template #default="scope">
            <zr-scrollbar
              v-if="scope.row.tag"
              :wrap-class="['scrollbar-wrapper', 'wrapper-custom']"
              :wrap-style="[{ 'max-height': '50px', height: '50px'}]"
            >
              <zr-tag
                v-for="item in scope.row.tag.split(';')"
                :key="item"
              >{{ item }}</zr-tag>
            </zr-scrollbar>
          </template>
        </zr-table-column>
        <zr-table-column
          label="描述"
          prop="description"
          show-overflow-tooltip
        />
        <zr-table-column
          label="修订时间"
          prop="last_updated"
          show-overflow-tooltip
          width="120"
        >
          <template #default="scope">
            <div>
              <zr-tag v-if="scope.row.last_updated_at">{{ scope.row.last_updated_at }}</zr-tag>
              <p>{{ scope.row.last_updated }}</p>
            </div>
          </template>
        </zr-table-column>
        <zr-table-column
          label="操作"
          width="80px"
        >
          <template #default="scope">
            <zr-button
              type="primary"
              link
              @click="deleteClick(scope.row)"
            >删除</zr-button>
          </template>
        </zr-table-column>
      </zr-table>
      <zr-pagination
        v-if="total>0"
        :current-page="searchForm.page"
        :page-size="searchForm.page_size"
        :page-sizes="[20, 40, 60, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <!-- 引入规则 弹窗 -->
    <zr-drawer
      v-model="rulesDrawer"
      size="45%"
      :destroy-on-close="true"
      :close-on-click-modal="false"
    >
      <template #header>
        <h4 style="color: #303133;">
          引入规则
        </h4>
      </template>
      <zr-row
        :gutter="24"
        justify="space-between"
        style="white-space: nowrap"
      >
        <zr-col
          :span="21"
          style="text-align:right"
        >
          <zr-form :inline="true">
            <zr-form-item>
              <zr-select
                v-model="drawerSearchForm.source"
                size="default"
                style="width:150px"
                @change="drawerSearchClick"
              >
                <zr-option
                  v-for="item in sourceUnOption"
                  :key="item"
                  :label="item.label + '('+ item.num + ')' "
                  :value="item.value"
                >{{ item.label }}({{ item.num }})
                </zr-option>
              </zr-select>
            </zr-form-item>
            <zr-form-item>
              <zr-select
                v-model="drawerSearchForm.os"
                size="default"
                style="width:150px"
                @change="drawerSearchClick"
              >
                <zr-option
                  v-for="item in osUnOption"
                  :key="item"
                  :label="item.label + '('+ item.num + ')' "
                  :value="item.value"
                >{{ item.label }}({{ item.num }})
                </zr-option>
              </zr-select>
            </zr-form-item>
            <zr-form-item>
              <zr-select
                v-model="drawerSearchForm.level"
                size="default"
                style="width:150px"
                @change="drawerSearchClick"
              >
                <zr-option
                  v-for="item in levelUnOption"
                  :key="item"
                  :label="item.label + '('+ item.num + ')' "
                  :value="item.value"
                >{{ item.label }}({{ item.num }})
                </zr-option>
              </zr-select>
            </zr-form-item>
            <zr-form-item>
              <zr-input
                v-model="drawerSearchForm.name"
                placeholder="规则名称"
                size="default"
                style="width:220px"
              >
                <template #append>
                  <zr-button
                    @click="drawerSearchClick"
                  >
                    <icon name="zr-search" />
                  </zr-button>
                </template>
              </zr-input>
            </zr-form-item>
          </zr-form>
        </zr-col>
      </zr-row>
      <zr-table
        :data="drawerTableData"
        empty-text="暂无数据"
        @selection-change="drawerSelectionChange"
      >
        <zr-table-column
          type="selection"
          width="55"
        />
        <zr-table-column
          label="规则名"
          prop="title"
          show-overflow-tooltip
        />
        <zr-table-column
          label="级别"
          prop="level"
          width="80"
          show-overflow-tooltip
        >
          <template #default="scope">
            <span :style="'color:' + levelColor(scope.row.level)">
              {{ levelChinese(scope.row.level) }}
            </span>
          </template>
        </zr-table-column>
        <zr-table-column
          label="适用平台"
          prop="os"
          width="100"
          show-overflow-tooltip
        >
          <template #default="scope">
            <span :class="scope.row.os=='Linux' ?'':'system-icon-style'">
              <SystemIcon
                v-if="scope.row.os =='Linux' ? 'linux' :'windows'"
                :name="scope.row.os =='Linux' ? 'linux' :'windows'"
              />
            </span>
          </template>
        </zr-table-column>
        <zr-table-column
          label="修订时间"
          prop="last_updated"
          show-overflow-tooltip
          width="120"
        >
          <template #default="scope">
            <div>
              <zr-tag v-if="scope.row.last_updated_at">{{ scope.row.last_updated_at }}</zr-tag>
              <p>{{ scope.row.last_updated }}</p>
            </div>
          </template>
        </zr-table-column>
      </zr-table>
      <zr-pagination
        v-if="drawerTotal>0"
        :current-page="drawerSearchForm.page"
        :page-size="drawerSearchForm.page_size"
        :page-sizes="[50, 100, 150, 200]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="drawerTotal"
        @size-change="drawerHandleSizeChange"
        @current-change="drawerHandleCurrentChange"
      />
      <template #footer>
        <zr-button
          type="primary"
          size="default"
          @click="submit"
        >
          确定
        </zr-button>
        <zr-button
          size="default"
          @click="cancelClick"
        >
          取消
        </zr-button>
      </template>
    </zr-drawer>
  </div>
</template>
<script setup>
import { ref, reactive, watch, defineEmits, defineProps } from 'vue';
import { builtInTemplate, isBuiltInLevelOptions, yaraRulesList, templateEdit, bindingSourceOptions,
  getUnBindingSourceOptions, getUnBindingOsOptions, getUnBindingLevelOptions, getUnBindingRules,
  rulesAdd, rulesDel } from '@/api/rule/yara/ruleTemplate';
import { timeFormat } from '@/utils/formatting';
import MonacoEditor from '@/components/common/MonacoEditor/index.vue';
import { ZrMessage, ZrMessageBox } from 'qianji-ui';

const emit = defineEmits(['backClick']);
const props = defineProps({
  detailItem: Object
});
    
const basicMessage = reactive([
  {
    name: '模板名称：',
    value: 'name'
  },
  {
    name: '来源：',
    value: 'source'
  },
  {
    name: '创建时间：',
    value: 'created_at'
  },
  {
    name: '规则数量：',
    value: 'rule_count'
  },
  {
    name: '引用次数：',
    value: 'referenced_count'
  },
  {
    name: '更新时间：',
    value: 'updated_at'
  },
  {
    name: '模版描述：',
    value: 'description'
  }
]);
const nameBool = ref(false);
const name = ref('');
const description = ref('');
const descriptionBool = ref(false);
const searchForm = reactive({
  name: '',
  level: '',
  source: '',
  'page_size': 20,
  page: 1
});
  
const drawerSearchForm = reactive({
  name: '',
  level: '',
  source: '',
  os: '',
  'page_size': 100,
  page: 1
});
  
  
const drawerTableData = ref([]);
const drawerTotal = ref(0);
const drawerIds = ref([]);
const rulesDrawer = ref(false);
const loading = ref(false);
const total = ref(0);
const tableData = ref([]);
const ids = ref([]);
const levelOptions = ref([]);
const sourceOption = ref([]);
const basicList = ref({});
const sourceUnOption = ref([]);
const levelUnOption = ref([]);
const osUnOption = ref([]);

watch(() => props.detailItem, (newValue) => {
  createFun();
}, {
  immediate: true
});

function createFun() {
  listFun();
  optionsFun();
  yaraRulesListFun();
}

function metaFun(meta) {
  var k = meta.slice(1, -1);
  var val = k.replaceAll('","', '"\n"');
  return val;
}
function yaraRulesListFun() {
  yaraRulesList(props.detailItem.id, searchForm).then(res => {
    tableData.value = res.data.data.list || [];
    total.value = res.data.data.count;
  });
}
function optionsFun() {
  isBuiltInLevelOptions(props.detailItem.id).then(res => {
    levelOptions.value = res.data.data.data;
  });
  bindingSourceOptions(props.detailItem.id).then(res => {
    sourceOption.value = res.data.data.data;
  });
}
var editForm = {
  group: 'common',
  'business_type': 'mfp'
};
function nameClick() {
  if (nameBool.value) {
    templateEditFun({ name: name.value, description: basicList.value.description, ...editForm });
  } else {
    name.value = basicList.value.name;
    nameBool.value = true;
  }
}
function descriptionClick() {
  if (descriptionBool.value) {
    templateEditFun({ name: basicList.value.name, description: description.value, ...editForm });
  } else {
    description.value = basicList.value.description;
    descriptionBool.value = true;
  }
}
function templateEditFun(obj) {
  templateEdit(props.detailItem.id, obj).then(res => {
    if (res.data.code === 0) {
      ZrMessage.success('修改成功');
      descriptionBool.value = false;
      nameBool.value = false;
      listFun();
    } else {
      ZrMessage.error(res.data.message);
    }
  });
}
function listFun() {
  builtInTemplate(props.detailItem.id).then(res => {
    basicList.value = res.data.data;
  });
}
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
}
function searchClick() {
  searchForm.page = 1;
  yaraRulesListFun();
}
  
function batchDeteleClick() { // 批量删除
  ZrMessageBox.confirm('确认删除所选项?', '提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      rulesDel(props.detailItem.id, { 'delete_rule_ids': ids.value }).then(res => {
        if (res.data.code === 0) {
          ZrMessage.success('删除成功');
          createFun();
        } else {
          ZrMessage.error(res.data.message);
        }
      });
    });
}
function deleteClick(row) { // 删除
  ZrMessageBox.confirm('确认删除该项?', '提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      rulesDel(props.detailItem.id, { 'delete_rule_ids': [row.id] }).then(res => {
        if (res.data.code === 0) {
          ZrMessage.success('删除成功');
          createFun();
        } else {
          ZrMessage.error(res.data.message);
        }
      });
    });
}
function handleSizeChange(val) {
  searchForm["page_size"] = val;
  searchForm.page = 1;
  yaraRulesListFun();
}
function handleCurrentChange(val) {
  searchForm.page = val;
  yaraRulesListFun();
}
    
// 引入规则弹窗
  
function getUnBindingRulesFun() {
  getUnBindingRules(props.detailItem.id, drawerSearchForm).then(res => {
    drawerTableData.value = res.data.data.list || [];
    drawerTotal.value = res.data.data.count;
  });
}
function importRulesClick() { // 引入规则
  getUnBindingSourceOptions(props.detailItem.id).then(res => {
    sourceUnOption.value = res.data.data.data;
  });
  getUnBindingOsOptions(props.detailItem.id).then(res => {
    osUnOption.value = res.data.data.data;
  });
  getUnBindingLevelOptions(props.detailItem.id).then(res => {
    levelUnOption.value = res.data.data.data;
  });
  getUnBindingRulesFun();
  rulesDrawer.value = true;
}
function drawerSearchClick() {
  searchForm.page = 1;
  getUnBindingRulesFun();
}
  
function drawerSelectionChange(selection) {
  drawerIds.value = selection.map(item => item.id);
}
  
function submit() {
  if (drawerIds.value.length === 0) {
    ZrMessage.error('请选择引入项');
    return;
  }
  rulesAdd(props.detailItem.id, { 'rule_ids': drawerIds.value }).then(res => {
    if (res.data.code === 0) {
      rulesDrawer.value = false;
      createFun();
      ZrMessage.success('引入成功');
    } else {
      ZrMessage.error(res.data.message);
    }
  });
}
  
function cancelClick() {
  rulesDrawer.value = false;
}
function drawerHandleSizeChange(val) {
  drawerSearchForm["page_size"] = val;
  drawerSearchForm.page = 1;
  getUnBindingRulesFun();
}
function drawerHandleCurrentChange(val) {
  drawerSearchForm.page = val;
  getUnBindingRulesFun();
}

const levelColor = (level) => { // 级别颜色
  if (level == 'low') return '#409EFF';
  if (level == 'medium') return '#ffd700';
  if (level == 'high') return '#E6A23C';
  if (level == 'critical') return '#F56C6C';
};
const levelChinese = (level) => { // 级别
  if (level == 'low') return '低';
  if (level == 'medium') return '中';
  if (level == 'high') return '高';
  if (level == 'critical') return '致命';
};
  
function backClick() { // 返回
  emit('backClick');
}
</script>
    <style lang="scss" scoped>
      .back{
        margin-bottom: 30px;
        display: flex;
        h2{
          margin-left: 30px;
          font-weight: 500;
        }
      }
      .top{
        .el-col{
            font-size: 14px;
            display: flex;
            align-items: center;
            line-height: 30px;
        }
      }
      .edit-msg{
        display: flex;
        align-items: center;
      }
    </style>
  
