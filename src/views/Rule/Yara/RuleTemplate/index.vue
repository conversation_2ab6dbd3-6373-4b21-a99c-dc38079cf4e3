<template>
  <div
    class=""
  >
    <zr-row
      :gutter="24"
      justify="space-between"
      style="white-space: nowrap"
    >
      <zr-col :span="8">
        <zr-button
          type="primary"
          size="default"
          @click="addTemplateClick"
        >添加模板</zr-button>
      </zr-col>
      <zr-col
        :span="16"
        style="text-align:right"
      >
        <zr-form :inline="true">
          <zr-form-item>
            <zr-select
              v-model="searchForm.source"
              size="default"
              @change="searchClick"
            >
              <zr-option
                v-for="item in sourcesOptions"
                :key="item"
                :label="item.label + '('+ item.num + ')' "
                :value="item.value"
              >{{ item.label }}({{ item.num }})
              </zr-option>
            </zr-select>
          </zr-form-item>
          <!-- <zr-form-item>
            <zr-select
              v-model="searchForm.os"
              size="default"
              @change="searchClick"
            >
              <zr-option
                v-for="item in OsOptions"
                :key="item"
                :label="item.label + '('+ item.num + ')' "
                :value="item.value"
              >{{ item.label }}({{ item.num }})
              </zr-option>
            </zr-select>
          </zr-form-item> -->
          <zr-form-item>
            <zr-input
              v-model="searchForm.name"
              placeholder="模板名称"
              size="default"
              @keydown.enter.prevent="searchClick"
            >
              <template #append>
                <zr-button
                  @click="searchClick"
                >
                  <icon name="zr-search" />
                </zr-button>
              </template>
            </zr-input>
          </zr-form-item>
        </zr-form>

      </zr-col>
    </zr-row>
    <span>
      <zr-alert
        title="系统内置模板只可引用，不可删除和修改；用户可复制模板进行自定义配置。"
        show-icon
        :closable="false"
      />
    </span>
    <div
      v-if="listData.length>0"
      class="content"
    >
      <zr-row
        :gutter="24"
        style="margin:0"
      >
        <zr-col
          v-for="item in listData"
          :key="item"
          style="padding: 0 20px 20px 0"
          :span="screenWidth>=1600?6:8"
        >
          <zr-card class="">
            <div class="content-card-top">
              <h5>{{ item.name }}</h5>
              <p class="content-card-description">{{ item.description }}</p>
              <div class="content-card-message">
                <p>模板来源：{{ item.source==='built_in'? '内置':'自定义' }}</p>
                <p>规则数量：{{ item.rule_count }}</p>
                <p>更新时间：{{ timeFormat(item.updated_at) }}</p>
              </div>
            </div>
            <div class="content-card-bottom">
              <span
                class="col-history"
                :style="{background:item.referenced_count===0?'#919398':'#53a9ff'}"
              >
                {{ item.referenced_count }}
              </span>
              <div class="btn">
                <zr-button
                  type="primary"
                  link
                  @click="detailsClick(item)"
                >详情</zr-button>
                <zr-button
                  type="primary"
                  link
                  @click="copyTemplate(item)"
                >复制</zr-button>
                <zr-tooltip
                  :disabled="item.source!=='built_in' && item.referenced_count== 0"
                  placement="top"
                  :content="item.source==='built_in' ? '内置模版不可删除' : '该模版已被任务引用，无法删除'"
                >
                  <span>
                    <zr-button
                      type="primary"
                      link
                      style="margin-left: 12px;"
                      :disabled="item.source==='built_in' || item.referenced_count!==0"
                      @click="deleteTemplate(item)"
                    >删除</zr-button>
                  </span>
                </zr-tooltip>
              </div>
            </div>
          </zr-card>
        </zr-col>
      </zr-row>
    </div>
    <div v-else>
      <zr-empty description="暂无数据" />
    </div>
    <zr-pagination
      v-if="total>0"
      style="margin-top: 0;"
      :current-page="searchForm.page"
      :page-size="searchForm.page_size"
      :page-sizes="[12, 24, 48, 120]"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
    <!-- 添加模板 -->
    <AddTemplate
      v-model="addDrawer"
      @saveClick="saveClick"
      @cancelClick="cancelClick"
    />
  </div>
</template>
<script setup>
import { reactive, ref } from 'vue';
import { sourceOptions, osOptions, templatesList, templatesAdd, templatesCopy, templatesDel } from '@/api/rule/yara/ruleTemplate';
import { timeFormat } from '@/utils/formatting';
import AddTemplate from './AddTemplate.vue';
import { ZrMessage, ZrMessageBox } from 'qianji-ui';
const emit = defineEmits(['detailClick']);

const screenWidth = ref(window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth);
onMounted(() => {
  window.onresize = () => {
    return (() => {
      screenWidth.value = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;
    })();
  };
});
// const OsOptions = ref([]);
const sourcesOptions = ref([]);
const listData = ref([]);
const total = ref(0);
const searchForm = reactive({
  // os: '',
  source: '',
  name: '',
  page: 1,
  'page_size': 12
});
const addDrawer = ref(false);
sourcesOptionsFun();
// osOptionsFun();
function sourcesOptionsFun() {
  sourceOptions().then(res => {
    sourcesOptions.value = res.data.data.data;
  });
}
// function osOptionsFun() {
//   osOptions().then(res => {
//     OsOptions.value = res.data.data.data;
//   });
// }
templatesListFun();
function templatesListFun() {
  templatesList(searchForm).then(res => {
    listData.value = res.data.data.data || [];
    total.value = res.data.data.count;
  });
}
function searchClick() {
  searchForm.page = 1;
  searchForm['page_size'] = 12;
  templatesListFun();
}
function addTemplateClick() { // 添加模板
  addDrawer.value = true;
}

function detailsClick(item) { // 详情
  emit('detailClick', item);
}
function copyTemplate(row) { // 复制模板
  ZrMessageBox.confirm('确定复制 “' + row.name + '” 模板?', '复制模板', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      templatesCopy(row.id).then(res => {
        if (res.data.code === 0) {
          ZrMessage.success('复制成功');
          templatesListFun();
          sourcesOptionsFun();
        } else {
          ZrMessage.error(res.data.msg);
        }
      });
    })
    .catch(() => {
    });
}
function deleteTemplate(row) { // 删除
  ZrMessageBox.confirm('确定删除 “' + row.name + '” 模板?', '删除模板', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      templatesDel(row.id).then(res => {
        if (res.data.code === 0) {
          ZrMessage.success('删除成功');
          templatesListFun();
          sourcesOptionsFun();
        } else {
          ZrMessage.error(res.data.msg);
        }
      });
    })
    .catch(() => {
    });
}
function saveClick(form) {
  templatesAdd(form).then(res => {
    console.log(res);
    if (res.data.code === 0) {
      addDrawer.value = false;
      ZrMessage.success('添加成功');
      templatesListFun();
      sourcesOptionsFun();
    } else {
      ZrMessage.error(res.data.msg);
    }
  });
}
function cancelClick() {
  addDrawer.value = false;
}
function handleSizeChange(val) {
  searchForm["page_size"] = val;
  searchForm.page = 1;
  templatesListFun();
}
function handleCurrentChange(val) {
  searchForm.page = val;
  templatesListFun();
}
</script>
  <style lang="scss" scoped>
   :deep(.el-alert__content .el-alert__title) {
      font-size: 14px;
      color: #1890FF;
      font-weight: 400;
      font-size: 13px;
    }
    :deep(.el-alert .el-alert__icon) {
      color:#1890ff;
    }
       .el-alert.el-alert--info.is-light {
      background-color: #ECF5FF;
    }
    .el-alert {
      margin: 20px 0 0;
    }
    :deep(.el-card__body) {
    padding:0 !important;
    background: #fff;
  }

    .content{
      margin-top: 20px;
      .content-card-top{
        padding: 20px 20px 20px 20px;
      }
      .content-card-description{
        width:350px;
        height: 22px;
        margin: 8px 0 20px 0;
      }
      P {
        color: #919398;
        font-size: 14px;
        line-height: 22px;
      }
      .content-card-message{
        // border-bottom: 1px solid #DCDFE6;

      }
      .content-card-bottom{
      border-top: 1px solid #DCDFE6;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 20px;
        .col-history {
          // background: #53a9ff;
          color: #fff;
          padding: 3px 8px;
          border-radius: 3px;
        }
        .btn {
          height: 48px;
        line-height: 48px;
        }
      }
    }
  </style>
