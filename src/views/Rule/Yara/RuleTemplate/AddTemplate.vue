<template>
  <div>
    <zr-drawer
      v-bind="$attrs"
      destroy-on-close
      :close-on-click-modal="false"
      @open="draOpen"
    >
      <template #header>
        <h4 style="color: #303133;">
          添加模板
        </h4>
      </template>
      <zr-form
        ref="formRef"
        label-position="top"
        :model="form"
        :rules="rules"
      >
        <zr-form-item
          label="名称"
          prop="name"
        >
          <zr-input
            v-model="form.name"
            maxlength="50"
            show-word-limit
            placeholder="请输入名称,1-50位"
          />
        </zr-form-item>
        <zr-form-item
          prop="description"
          label="描述"
        >
          <zr-input
            v-model="form.description"
            type="textarea"
            maxlength="500"
            show-word-limit
            placeholder="请输入描述,0-500位"
          />
        </zr-form-item>
      </zr-form>
      <template #footer>
        <zr-button
          type="primary"
          size="default"
          @click="submit(formRef)"
        >
          确定
        </zr-button>
        <zr-button
          size="default"
          @click="cancelClick"
        >
          取消
        </zr-button>
      </template>
    </zr-drawer>
  </div>
</template>
<script setup>
import { ref, reactive } from 'vue';
const emit = defineEmits(['saveClick', 'cancelClick']);

const formRef = ref();
const form = ref({
  name: '',
  description: ''
});
const rules = reactive({
  name: [
    { required: true, message: '请输入名称', trigger: 'blur' }
  ]
});
function draOpen() {
  form.value = {
    group: 'common',
    'business_type': 'mfp'
  };
}
const submit = (formEl) => {
  if (!formEl) return;
  formEl.validate(async(valid) => {
    if (valid) {
      emit('saveClick', form.value);
    }
  });
};
function cancelClick() {
  emit('cancelClick');
}
</script>
<style lang="scss" scoped>
</style>
