<template>
  <div class="">
    <zr-row
      :gutter="24"
      justify="space-between"
      style="white-space: nowrap"
    >
      <zr-col :span="10">
        <zr-button
          type="primary"
          size="default"
          @click="addRulesClick('add')"
        >添加规则</zr-button>
        <zr-button
          type="primary"
          size="default"
          @click="importRulesClick"
        >导入规则</zr-button>
        <zr-button
          type="danger"
          size="default"
          plain
          :disabled="deleteList.length <=0"
          @click="batchDeleteClick"
        >批量删除</zr-button>
        <zr-button
          type="primary"
          plain
          @click="downloadYara"
        >下载规则模板文件</zr-button>
      </zr-col>
      <zr-col
        :span="14"
        style="text-align:right"
      >
        <zr-form :inline="true">
          <zr-form-item>
            <zr-select
              v-model="searchForm.os"
              size="default"
              @change="searchClick"
            >
              <zr-option
                v-for="item in osOption"
                :key="item"
                :label="item.label + '('+ item.num + ')' "
                :value="item.value"
              >{{ item.label }}({{ item.num }})
              </zr-option>
            </zr-select>
          </zr-form-item>
          <zr-form-item>
            <zr-select
              v-model="searchForm.level"
              size="default"
              @change="searchClick"
            >
              <zr-option
                v-for="item in levelOption"
                :key="item"
                :label="item.label + '('+ item.num + ')' "
                :value="item.value"
              >{{ item.label }}({{ item.num }})
              </zr-option>
            </zr-select>
          </zr-form-item>
          <zr-form-item>
            <zr-input
              v-model="searchForm.name"
              placeholder="规则名称/描述/标签"
              size="default"
              @keydown.enter.prevent="searchClick"
            >
              <template #append>
                <zr-button
                  @click="searchClick"
                >
                  <icon name="zr-search" />
                </zr-button>
              </template>
            </zr-input>
          </zr-form-item>
        </zr-form>

      </zr-col>
    </zr-row>
    <zr-table
      v-loading="loading"
      :data="tableData"
      empty-text="暂无数据"
      @selection-change="handleSelectionChange"
    >
      <zr-table-column
        type="expand"
        width="30"
      >
        <template #default="props">
          <div class="detail-msg">
            <zr-card>
              <monaco-editor
                :monaco-val="props.row.raw"
                :read-only="true"
                style="height: 480px;"
              />
            </zr-card>
          </div>
        </template>
      </zr-table-column>
      <zr-table-column
        type="selection"
        width="30"
      />
      <zr-table-column
        label="名称"
        prop="title"
        show-overflow-tooltip
      />
      
      <zr-table-column
        label="级别"
        prop="level"
        width="80"
        show-overflow-tooltip
      >
        <template #default="scope">
          <p :class="'tag-custom ' + scope.row.level">
            {{ levelChinese(scope.row.level) }}
          </p>
        </template>
      </zr-table-column>
      <zr-table-column
        label="适用平台"
        prop="os"
        width="120"
        show-overflow-tooltip
      >
        <template #default="scope">
          <span :class="scope.row.os=='Linux' ?'':'system-icon-style'">
            <SystemIcon
              v-if="scope.row.os =='Linux' ? 'linux' :'windows'"
              :name="scope.row.os =='Linux' ? 'linux' :'windows'"
            />
          </span>
          
        </template>
      </zr-table-column>
      <zr-table-column
        label="适用对象"
        prop="object_type"
        show-overflow-tooltip
        width="100"
      />
      <zr-table-column
        label="文件类型"
        prop="file_type"
        width="80"
        show-overflow-tooltip
      />
      <zr-table-column
        label="标签"
        prop="tag"
      >
        <template #default="scope">
          <zr-scrollbar
            :wrap-class="['scrollbar-wrapper', 'wrapper-custom']"
            :wrap-style="[{ 'max-height': '50px', height: '50px'}]"
          >
            <p
              v-for="item in scope.row.tag.split(';')"
              :key="item"
              :class="'tag-custom ' + scope.row.level"
            >{{ item }}</p>
          </zr-scrollbar>
        </template>
      </zr-table-column>
      <zr-table-column
        label="创建用户"
        prop="user_name"
        show-overflow-tooltip
        width="100"
      />
      <zr-table-column
        label="模板引用"
        width="100px"
        prop="referenced_count"
        show-overflow-tooltip
      >
        <template #default="scope">
          <span class="col-history">{{ scope.row.referenced_count }}</span>
        </template>
      </zr-table-column>
      <zr-table-column
        label="创建时间"
        prop="created_at"
        width="180"
        show-overflow-tooltip
      >
        <template #default="scope">
          <div v-if="scope.row.created_at || scope.row.created_time_ago">
            <zr-tag v-if="scope.row.created_time_ago">{{ scope.row.created_time_ago }}</zr-tag>
            <p>{{ timeFormat(scope.row.created_at) }}</p>
          </div>
          <div v-else>-</div>
        </template>
      </zr-table-column>
      <zr-table-column
        label="操作"
        prop=""
        width="120px"
      >
        <template #default="scope">
          <zr-button
            type="primary"
            link
            @click="addRulesClick('edit',scope.row)"
          >编辑</zr-button>
          <zr-button
            type="primary"
            link
            @click="deleteClick(scope.row)"
          >删除</zr-button>
        </template>
      </zr-table-column>
    </zr-table>
    <zr-pagination
      v-if="total>0"
      :current-page="searchForm.page"
      :page-size="searchForm.page_size"
      :page-sizes="[20, 40, 60, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
    <!-- 添加规则 -->
    <AddRules
      v-model="addRulesDrawer"
      :row-id="rowId"
      :monaco-val="monacoVal"
      :drawer-title="drawerTitle"
      @saveClick="saveClick"
      @cancelClick="cancelClick"
    />
    <!-- 导入规则 -->
    <ImportRules
      v-if="importRulesDrawer"
      v-model="importRulesDrawer"
      @saveClick="importSaveClick"
      @cancelClick="importCancelClick"
    />
  </div>
</template>
<script setup>
import { reactive, ref, onMounted } from 'vue';
import { levelOptions, osOptions, yaraList, batchDelete } from '@/api/rule/yara/customRule';
import { timeFormat } from '@/utils/formatting';
import { ZrMessage, ZrMessageBox } from 'qianji-ui';
import AddRules from './AddRules.vue';
import ImportRules from './ImportRules.vue';
import monacoEditor from '@/components/common/MonacoEditor/index.vue';

const osOption = ref([]);
const levelOption = ref([]);
const tableData = ref([]);
const rowId = ref('');
const total = ref(0);
const loading = ref(false);
const monacoVal = ref('');
const drawerTitle = ref('');
const searchForm = reactive({
  level: '',
  os: '',
  name: '',
  source: 'custom',
  page: 1,
  'page_size': 20
});
const deleteList = ref([]);
const addRulesDrawer = ref(false);
const importRulesDrawer = ref(false);
const scrollMenuRef = ref();
const levelChinese = (level) => { // 级别
  if (level == 'safe') return '安全';
  if (level == 'information') return '提示';
  if (level == 'low') return '低';
  if (level == 'medium') return '中';
  if (level == 'high') return '高';
  if (level == 'critical') return '致命';
};
const handleSelectionChange = (val) => {
  deleteList.value = val.map(item => item.id);
};

const source = ref({
  source: 'custom'
});
onMounted(() => {
  optionsFun();
});
function optionsFun() {
  levelOptions(source.value).then(res => {
    levelOption.value = res.data.data.data;
  });
  osOptions(source.value).then(res => {
    osOption.value = res.data.data.data;
  });
}

yaraListFun();
function yaraListFun() {
  loading.value = true;
  yaraList(searchForm).then(res => {
    tableData.value = res.data.data.list;
    total.value = res.data.data.count;
  }).finally(() => {
    loading.value = false;
  });
}
function searchClick() {
  yaraListFun();
}
function downloadYara() { // 下载模板
  const a = document.createElement('a');
  a.href = new URL('@/assets/file/yara.yar', import.meta.url);
  a.download = 'yara.yar';
  a.click();
}
function batchDeleteFun(data) {
  batchDelete({ 'ids': data }).then(res => { // 批量删除接口
    if (res.data.code == 0) {
      ZrMessage.success(res.data.data);
      optionsFun();
      yaraListFun();
    } else {
      ZrMessage.error(res.data.data);
    }
  });
}
function batchDeleteClick() { // 批量删除
  ZrMessageBox.confirm('确认删除勾选的所有选项?', '确认操作', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    batchDeleteFun(deleteList.value);
  });
}

function deleteClick(row) {
  ZrMessageBox.confirm('确认删除' + row.user_name + '规则?', '确认操作', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      batchDeleteFun([row.id]);
    });
}

// 添加、编辑 规则
function addRulesClick(type, row) {
  if (type == 'edit') {
    rowId.value = row.id;
    drawerTitle.value = '编辑规则';
  } else {
    drawerTitle.value = '添加规则';
  }
  addRulesDrawer.value = true;
}
function saveClick(form) {
  addRulesDrawer.value = false;
  optionsFun();
  yaraListFun();
}
function cancelClick() {
  addRulesDrawer.value = false;
}

// 导入规则模板
function importRulesClick() {
  importRulesDrawer.value = true;
}
function importSaveClick(bool) {
  importRulesDrawer.value = false;
  if (bool) {
    optionsFun();
    yaraListFun();
  }
}
function importCancelClick() {
  importRulesDrawer.value = false;
}
function handleSizeChange(val) {
  searchForm["page_size"] = val;
  searchForm.page = 1;
  yaraListFun();
}
function handleCurrentChange(val) {
  searchForm.page = val;
  yaraListFun();
}

</script>
  <style lang="scss" scoped>
  </style>

