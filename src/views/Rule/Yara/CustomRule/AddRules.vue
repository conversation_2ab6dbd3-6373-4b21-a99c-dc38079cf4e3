<template>
  <div>
    <zr-drawer
      v-bind="$attrs"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      @open="drawerOpen"
    >
      <template #header>
        <h4 style="color: #303133;">
          {{ drawerTitle }}
        </h4>
      </template>
      <div class="ruleStyle">
        <span class="content-style">规则内容</span>
        <span class="check-style">
          <zr-button
            size="small"
            @click="checkIsYara"
          >规则合法性校验</zr-button>
          <zr-button
            size="small"
            type="primary"
            link
            style="font-size:12px;float:right"
            @click="explainDialogClick"
          >  <zr-icon
            name="QuestionFilled"
            color="#1890ff"
            size="16"
          />规则编写规范</zr-button>
        </span>
      </div>
      <monacoEditor
        :key="keyRom"
        ref="monacoRef"
        :monaco-val="monacoVal"
        :read-only="false"
        style="height: 600px;"
        @monacoValClick="monacoValClick"
      />
      <template #footer>
        <zr-button
          type="primary"
          size="default"
          @click="submit"
        >
          确定
        </zr-button>
        <zr-button
          size="default"
          @click="cancelClick"
        >
          取消
        </zr-button>
      </template>
      <!-- 编写规范说明 弹窗 -->
      <zr-dialog
        v-model="openExplainDialog"
        title="格式说明"
        style="width:80%"
        top="4vh"
      >
        <ExplainDialog />
      </zr-dialog>
    </zr-drawer>
  </div>
</template>
<script setup>
import { ref, defineProps, watch } from 'vue';
import { ZrMessage } from 'qianji-ui';
import ExplainDialog from './ExplainDialog.vue';
import monacoEditor from '@/components/common/MonacoEditor/index.vue';
import { yaraDeatil, isYara, addYara, editYara } from '@/api/rule/yara/customRule';
import { Base64 } from 'js-base64';
const emit = defineEmits(['saveClick', 'cancelClick']);
const props = defineProps({
  rowId: {
    type: String,
    default: ''
  },
  drawerTitle: {
    type: String,
    default: ''
  }
});
watch(() => props.rowId, (val) => {
  if (props.drawerTitle == '编辑规则') {
    yaraDeatil(props.rowId).then(res => { // 获取详情
      monacoVal.value = res.data.data.raw;
      keyRom.value = Math.random();
    });
  }
});
const monacoRef = ref();
const monacoVal = ref('');
const keyRom = ref("");
const openExplainDialog = ref(false);


function drawerOpen() {
  if (props.drawerTitle == '添加规则') {
    monacoVal.value = '';
    keyRom.value = Math.random();
  }
}

function checkIsYara() { // 规则合法性校验
  isYara({ raw: Base64.encode(monacoVal.value) }).then(res => {
    if (res.data.code == 0) {
      ZrMessage.success(res.data.data);
    } else {
      ZrMessage.error(res.data.data);
    }
  });
}

function explainDialogClick() { // 编写规范
  openExplainDialog.value = true;
}
function monacoValClick(val) {
  monacoVal.value = val;
}

function submit() {
  var form = {
    raw: Base64.encode(monacoVal.value),
    group: 'common',
    'business_type': 'mfp'
  };
  if (props.drawerTitle == '添加规则') {
    addYara(form).then(res => {
      if (res.data.code == 0) {
        ZrMessage.success('添加成功');
        emit('saveClick');
      } else {
        ZrMessage.error(res.data.msg);
      }
    });
  } else {
    editYara(props.rowId, form).then(res => {
      if (res.data.code == 0) {
        ZrMessage.success('编辑成功');
        emit('saveClick');
      } else {
        ZrMessage.error(res.data.msg);
      }
    });
  }
}

function cancelClick() {
  emit('cancelClick');
}
</script>
<style lang="scss" scoped>
.ruleStyle{
  margin-bottom: 12px;

    .content-style{
        font-size: 14px;
        margin-right: 10px;
        justify-content: space-between;
    }
    .check-style{
    }
}
:deep(.el-drawer.rtl){
  width: 50% !important;
}
</style>
