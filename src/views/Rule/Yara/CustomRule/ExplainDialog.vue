<template>
  <div class="explain">
    <table>
      <tr>
        <td
          class="title"
          rowspan="1"
          colspan="4"
        >Yara规则定义</td>
      </tr>
      <tr
        v-for="(item,index) in explainOption"
        :key="item.rule"
      >
        <td
          v-if="item.classify||item.classify==''"
          class="title"
          :rowspan="index==1?'19':'1'"
        >{{ item.classify }}</td>
        <td :calss="!item.classify?'classify':''">{{ item.key }}</td>
        <td>{{ item.label }}</td>
        <td>{{ item.rule }}</td>
      </tr>
    </table>
  </div>
</template>
  
<script setup>
import { reactive } from 'vue';
const explainOption = reactive([
  // {
  //   classify: '',
  //   key: 'id',
  //   label: '规则id',
  //   rule: '自动生成， 01开头，表示Yara规则，“-”后用随机字符串，示例：01-cG7UUka2xEiApfRKAokBU，'
  // },
  {
    classify: '名称',
    key: 'title（必填）',
    label: '规则名称',
    rule: '【显示，告警详情中显示】系统自动提取rule后面的字符填入，可手动'
  },
  {
    classify: 'meta',
    key: 'tag（必填）',
    label: '规则标签',
    rule: '【显示，用于在界面的威胁标签中显示】用户自定义，至少包含一个，多个标记用分号“；”隔开，用于显示'
  },
  {
    key: 'description（必填）',
    label: '规则描述',
    rule: '【显示，告警详情中显示】规则描述信息'
  },
  {
    key: 'author（可选）',
    label: '规则作者',
    rule: '规则编写作者'
  },
  {
    key: 'reference1-N（可选）',
    label: '规则参考链接',
    rule: '【显示，告警详情中显示】URL地址，未填写默认为空，可能对应多个参考链接，每个参考链接后面加数字'
  },
  // {
  //   key: 'actors（可选）',
  //   label: '所属组织',
  //   rule: '样本所属组织，未填写默认为空'
  // },
  {
    key: 'published_date（必填）',
    label: '规则首次发布时间',
    rule: '规则首次发布时间'
  },
  {
    key: 'last_updated（必填）',
    label: '规则最近更新时间',
    rule: '规则最近一次修订时间'
  },
  {
    key: 'version（可选）',
    label: '规则版本',
    rule: 'V1.0，未填写默认使用1.0'
  },
  {
    key: 'level（必填）',
    label: '规则级别',
    rule: '【显示，提供告警级别】五个级别 (information, low, medium, high, critical)'
  },
  {
    key: 'os（必填）',
    label: '适用操作系统',
    rule: '适用操作系统，如Windows、Linux'
  },
  {
    key: 'arch（必填）',
    label: '适用架构',
    rule: '适用的系统架构，如x86 arm mips ,可以配置多个，用分号“；”隔开'
  },
  {
    key: 'object_type（必填）',
    label: '适用对象',
    rule: '适用的检测对象，包括file，memory，traffic，多个用分号“；”隔开'
  },
  {
    key: 'file_type（必填）',
    label: '检测文件类型',
    rule: '检测文件类型，PE、ELF、Web、JS，可以配置多个，用分号“；”隔开'
  },
  {
    key: 'file_subtype（必填）',
    label: '检测文件子类型',
    rule: '检测文件子类，代表文件后缀，如web编程中的具体语言：jsp、asp、php；PE文件中exe、dll、sys、com等，如果适用于全部子类型，用关键字"all"'
  },
  {
    key: 'mitre_attack（必填）',
    label: 'Att&ck映射',
    rule: '【显示，告警详情中和ATT&CK映射中显示】对应ATT&CKT中的技术（Techniques）ID,例如：1059;T1059.001,T1059.002，可以配置多个，用分号“；”隔开'
  },
  {
    key: 'kill_chain（可选）',
    label: 'kill_chain映射',
    rule: '【显示，告警详情中和kill_chain映射中显示】对应kill chain7个阶段：Reconnaissance、Weaponization、Delivery、Exploitation、Installation、Command and Control、Actions on Objective，可以配置多个，用分号“；”隔开'
  },
  {
    key: 'threat_actors（可选）',
    label: '关联组织',
    rule: '【显示，告警详情中显示】威胁关联组织，可以配置多个，用分号“；”隔开'
  },
  {
    key: 'threat_type（可选）',
    label: '威胁类型',
    rule: '【显示，告警详情中显示】常见的恶意软件类型：backdoor（后门）、trojan（木马）、rootkit、ransomware（勒索软件）、Cryptocurrency Miner（挖矿软件）、botnet（僵尸网络程序）、spyware（间谍软件）、Worm（蠕虫）、adware(广告软件)、virus(病毒)， 可以配置多个，用分号“；”隔开'
  },
  {
    key: 'families_labs（可选）',
    label: '威胁家族',
    rule: '【显示，告警详情中显示】可以配置多个，用分号“；”隔开'
  },
  {
    key: 'hash1-N（可选）',
    label: '恶意软件HASH值1',
    rule: '【显示，告警详情中显示】恶意软件样本对应的HASH值一个检测规则可能对应一个样本的多个HASH值或多个样本的HASH值，如果摘要算法相同，在hash后面用数字1开始标号，并在HASH后面跟算法类型'
  },

  // {
  //   key: 'malware_type（可选）',
  //   label: '恶意软件类型',
  //   rule: '常见的恶意软件类型：backdoor（后门）、trojan（木马）、rootkit、ransomware（勒索软件）、Cryptocurrency Miner（挖矿软件）（、botnet（僵尸网络程序）、spyware（间谍软件）、Worm（蠕虫）、adware(广告软件)、virus(病毒)， 可以配置多个，用分号“；”隔开'
  // },
  // {
  //   key: 'malware_families（可选）',
  //   label: '恶意软件家族',
  //   rule: '可以配置多个，用分号“；”隔开'
  // },
  // {
  //   key: 'hash1-N（可选）',
  //   label: '恶意软件HASH值1',
  //   rule: '恶意软件样本对应的HASH值一个检测规则可能对应一个样本的多个HASH值或多个样本的HASH值，如果摘要算法相同，在hash后面用数字1开始标号，并在HASH后面跟算法类型'
  // },
  {
    classify: 'strings',
    key: '',
    label: '',
    rule: 'YARA中有三种类型的字符串：十六进制字符串，文本字符串和正则表达式'
  },
  {
    classify: 'condition',
    key: '',
    label: '',
    rule: '条件是布尔表达式'
  }
]);
</script>
  
  <style lang="scss" scoped>
  .explain {
      margin: 0px 10px;
      width: calc(100% - 20px);
      text-align: center;
      background-color: white;
      table {
          width: 100%;
          border-right: 1px solid #000;
          border-bottom: 1px solid #000;
          /* 设置边缘间距0 */
          border-spacing: 0;
          /* 用于表格属性, 表示表格的两边框合并为一条 */
          border-collapse: collapse;
          tr{
              td:nth-child(1) {
                  width: 8%;
              }
              td:nth-child(2) {
                  width: 20%;
              }
              td:nth-child(3) {
                  width: 20%;
              }
              td:nth-child(4) {
                  width: 52%;
              }
              .classify{
                text-align: left;
              }
              .title{
                text-align: center;
              }
          }
          td {
              border-left: 1px solid #000;
              border-top: 1px solid #000;
              text-align: left;
              font-size: 12px;
              font-weight: bold;
              border-right: 1px solid #000;
              padding: 6px 4px;
          }
      }
  }
  </style>
  
