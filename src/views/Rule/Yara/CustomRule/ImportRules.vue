<template>
  <div>
    <zr-drawer
      ref="upload"
      v-bind="$attrs"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      title="导入规则"
      @close="confirmClick(true)"
    >
      <template #default>
        <div>
          <zr-upload
            ref="upload"
            action="#"
            drag
            multiple
            :auto-upload="false"
            :limit="1"
            :on-change="uploadChange"
            :on-exceed="handleExceed"
          >
            <template #trigger>
              <Icon
                name="zr-cloud-upload-fill-b"
                class="el-icon--upload"
              />
              <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em>
                <p class="upload-msg">支持.yar格式文件类型</p>
              </div>
            </template>
          </zr-upload>
          <div
            v-if="importSuccess&&importMsg"
            class="success-msg"
          >
            <p v-if="importMsg.failed_list&&importMsg.failed_list.length>0">规则 “ {{ importMsg.failed_list.join('，') }} ” 上传失败；</p>
            <p>上传成功规则 {{ importMsg.success_count }} 条；</p>
          </div>
        </div>
      </template>
      <template #footer>
        <div style="flex: auto">
          <zr-button
            v-if="!importSuccess"
            type="primary"
            :loading="btnLoading"
            @click="importFun"
          >确 定</zr-button>
          <zr-button
            v-if="!importSuccess"
            @click="cancelClick"
          >取 消</zr-button>
          <zr-button
            v-if="importSuccess"
            @click="confirmClick"
          >关 闭</zr-button>
        </div>
      </template>
    </zr-drawer>
  </div>
</template>
<script setup>
import { ref, defineEmits } from 'vue';
import { ZrMessage } from 'qianji-ui';
import { genFileId } from 'element-plus';
import { importRule } from '@/api/rule/yara/customRule';

const emit = defineEmits(['saveClick', 'cancelClick']);
const btnLoading = ref(false);
const upload = ref();
const importSuccess = ref(false);
const fileList = ref({});
const importMsg = ref({});

function uploadChange(file) {
  const fileName = file.name;
  const pos = fileName.lastIndexOf('.');
  const lastName = fileName.substring(pos, fileName.length);
  var name = lastName.toLowerCase();
  var format = ['.yar'];
  if (format.indexOf(name) === -1) {
    ZrMessage.error('文件必须为.yar格式');
    upload.value.clearFiles();
    return;
  }
  fileList.value = file;
  // importFun();
}
const handleExceed = (files) => {
  upload.value.clearFiles();
  const file = files[0];
  file.uid = genFileId();
  upload.value.handleStart(file);
};
function importFun() {
  btnLoading.value = true;
  const params = new FormData();
  params.append('file', fileList.value.raw);
  params.append('group', 'common');
  params.append('business_type', 'mfp');
  importRule(params).then(res => {
    if (res.data.code === 0) {
      importMsg.value = res.data.data;
      importSuccess.value = true;
      // ZrMessage.success('上传成功');
    } else {
      ZrMessage.error(res.data.msg);
    }
  }).finally(() => {
    btnLoading.value = false;
  });
}
function confirmClick(bool) {
  emit('saveClick', bool);
}
function cancelClick() {
  emit('cancelClick');
}
</script>
<style lang="scss" scoped>
  .upload-msg{
    font-size: 14px;
    margin-top: 8px;
}
.success-msg{
  margin-top:18px;
  color: #666;
  line-height: 24px;
}
</style>
  
