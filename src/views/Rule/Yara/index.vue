<template>
  <div class="main-content">
    <div
      v-if="!openDetail"
    >
      <tab-pane
        :option="tabOption"
        @handleClick="handleClick"
      />
      <RuleTemplate
        v-if="tabName=='1'"
        @detailClick="detailClick"
      />
      <CustomRule v-else />
    </div>
    <!-- 内置模板详情  -->
    <IsBuiltInDetail
      v-else-if="openDetail && source=='built_in'"
      :detail-item="detailItem"
      @backClick="backClick"
    />
    <!-- 自定义规则详情  -->
    <CustomDetail
      v-else-if="openDetail && source=='custom'"
      :detail-item="detailItem"
      @backClick="backClick"
    />
  </div>
</template>
<script setup>
import TabPane from '@/components/common/TabPane.vue';
import RuleTemplate from './RuleTemplate/index.vue';
import CustomRule from './CustomRule/index.vue';
import IsBuiltInDetail from './RuleTemplate/IsBuiltInDetail.vue';
import CustomDetail from './RuleTemplate/CustomDetail.vue';

const openDetail = ref(false);

const tabName = ref('1');
const tabOption = [
  {
    label: '规则模版',
    name: '1'
  },
  {
    label: '自定义规则',
    name: '2'
  }
];
const detailItem = ref({});

const source = ref('');
function detailClick(item) {
  source.value = item.source;
  detailItem.value = item;
  openDetail.value = true;
}

function backClick() {
  openDetail.value = false;
}

function handleClick(name) { // tab切换
  tabName.value = name;
}
</script>
