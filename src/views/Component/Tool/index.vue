<template>
  <!-- 工具管理 -->
  <div class="tool main-content">
    <zr-row
      :gutter="24"
      justify="space-between"
      style="white-space: nowrap"
    >
      <zr-col :span="8">
        <zr-button
          type="primary"
          size="default"
          @click="drawerClick"
        >添加工具</zr-button>
      </zr-col>
      <zr-col
        :span="16"
        style="text-align:right"
      >
        <zr-form :inline="true">
          <zr-form-item>
            <zr-input
              v-model="searchForm.name"
              placeholder="工具名称"
              size="default"
              @keydown.enter.prevent="searchClick"
            >
              <template #append>
                <zr-button
                  @click="searchClick"
                >
                  <icon name="zr-search" />
                </zr-button>
              </template>
            </zr-input>
          </zr-form-item>
        </zr-form>
      </zr-col>
    </zr-row>
    <zr-table
      v-loading="loading"
      :data="tableData"
      :default-sort="{ prop: 'created_at', order: 'descending' }"
      empty-text="暂无数据"
    >
      <zr-table-column
        label="工具名称"
        prop="name"
      />
      <zr-table-column
        label="描述"
        show-overflow-tooltip
        prop="description"
      />
      <zr-table-column
        label="类型"
        prop="tool_type"
        width="100"
      >
        <template #default="scope">
          {{ scope.row.tool_type==='inside'?'内置':'自定义' }}
        </template>
      </zr-table-column>
      <zr-table-column
        label="创建时间"
        prop="create_time"
        width="180"
      >
        <template #default="scope">
          <div v-if="scope.row.create_time || scope.row.escape_create_time">
            <zr-tag v-if="scope.row.escape_create_time">{{ scope.row.escape_create_time }}</zr-tag>
            <p>{{ scope.row.create_time }}</p>
          </div>
          <div v-else>-</div>
        </template>
      </zr-table-column>
      <zr-table-column
        label="更新时间"
        prop="update_time"
        width="180"
      >
        <template #default="scope">
          <div v-if="scope.row.update_time || scope.row.escape_update_time">
            <zr-tag v-if="scope.row.escape_update_time">{{ scope.row.escape_update_time }}</zr-tag>
            <p>{{ scope.row.update_time }}</p>
          </div>
          <div v-else>-</div>
        </template>
      </zr-table-column>
      <zr-table-column
        label="操作"
        prop=""
        width="240px"
      >
        <template #default="scope">
          <div class="buttonStyle">
            <zr-button
              type="primary"
              link
              @click="downloadClick(scope.row)"
            >下载</zr-button>
            <zr-button
              type="primary"
              link
              @click="copyClick(scope.row)"
            >复制链接</zr-button>
            <zr-button
              type="primary"
              link
              :disabled="scope.row.tool_type==='inside'"
              @click="drawerClick(scope.row)"
            >编辑</zr-button>
            <zr-button
              type="primary"
              link
              :disabled="scope.row.tool_type==='inside'"
              @click="delClick(scope.row)"
            >删除</zr-button>
          </div>
        </template>
      </zr-table-column>
    </zr-table>
    <zr-pagination
      v-if="total>0"
      :current-page="searchForm.page"
      :page-size="searchForm.page_size"
      :page-sizes="[20, 40, 60, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
    <zr-drawer
      v-model="drawer"
      :close-on-click-modal="false"
      :title="title"
      @closed="closedFun"
    >
      <template #default>
        <div>
          <zr-form
            ref="formRef"
            label-position="top"
            :model="form"
            :rules="rules"
          >
            <zr-form-item
              label="工具名称"
              prop="name"
            >
              <zr-input
                v-model="form.name"
                maxlength="30"
                show-word-limit
                placeholder="字母、数字、汉字，长度不超过30"
              />
            </zr-form-item>
            <zr-form-item
              prop="description"
              label="描述"
            >
              <zr-input
                v-model="form.description"
                type="textarea"
                maxlength="500"
                :rows="5"
                show-word-limit
                placeholder="请输入描述,0-500位"
              />
            </zr-form-item>
          </zr-form>
          <div>
            <p
              class="titleStyle"
            >上传文件</p>
            <zr-upload
              ref="upload"
              drag
              multiple
              :limit="1"
              action="#"
              :auto-upload="false"
              :on-exceed="handleExceed"
              :on-change="handleChange"
            >
              <template #trigger>
                <Icon
                  name="zr-cloud-upload-fill-b"
                  class="el-icon--upload"
                />
                <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
              </template>
              <template #tip>
                <div class="el-upload__tip">大小不超过100M</div>
              </template>
            </zr-upload>
          </div>
        </div>
      </template>
      <template #footer>
        <div style="flex: auto">
          <zr-button
            type="primary"
            @click="saveClick(formRef)"
          >确定</zr-button>
          <zr-button @click="drawer=false">取消</zr-button>
        </div>
      </template>
    </zr-drawer>
  </div>
</template>
<script setup>
import { ref, reactive, getCurrentInstance } from 'vue';
// import { timeFormat } from '@/utils/formatting';
import { listApi, addApi, editApi, delApi } from '@/api/component/tool';
import { genFileId } from 'element-plus';
import { ZrMessage, ZrMessageBox } from 'qianji-ui';
import { downloadFun } from '@/utils/downloadUtils';
const { proxy } = getCurrentInstance();

const searchForm = reactive({
  page: 1,
  'page_size': 20,
  name: ''
});
const tableData = ref([]);
const loading = ref(false);
const total = ref(0);
const drawer = ref(false);
const title = ref('');
const upload = ref();
const rowId = ref('');
const uploadList = ref({});
const form = ref({});
const formRef = ref();
const rules = reactive({
  name: [
    {
      required: true,
      message: '请输入工具名称',
      trigger: 'blur'
    }
  ]
});
onMounted(() => {
  listFun();
});
function listFun() {
  listApi(searchForm).then(res => {
    tableData.value = res.data.data.list || [];
    total.value = res.data.data.count || 0;
  });
}
function drawerClick(row) {
  title.value = row.id ? '编辑工具' : '添加工具';
  form.value = row.id ? { ...row } : {};
  rowId.value = row.id || '';
  drawer.value = true;
}
const saveClick = async(formEl) => {
  if (!formEl) return;
  await formEl.validate((valid, fields) => {
    if (valid) {
      const data = new FormData();
      data.append('name', form.value.name);
      data.append('description', form.value.description || '');
      data.append('tool_type', form.value.tool_type || 'custom');
      data.append('file', uploadList.value.raw || null);
      if (rowId.value === '') {
        if (!uploadList.value.raw) {
          ZrMessage.error('请上传文件');
          return;
        }
        addApi(data).then(res => {
          ZrMessage.success('添加成功');
          drawer.value = false;
          listFun();
        });
      } else {
        editApi(rowId.value, data).then(res => {
          ZrMessage.success('修改成功');
          drawer.value = false;
          listFun();
        });
      }
    } else {
      console.log('error submit!', fields);
    }
  });
};
function copyClick(row) { // 复制链接
  proxy.copyFun(window.BASE_URL + '/eos-service/api/v0/tools/download/' + row.id);
}
function downloadClick(row) { // 下载
  downloadFun('/eos-service/api/v1/tools/download/' + row.id);
}
function delClick(row) {
  ZrMessageBox.confirm('确认删除?', '确认操作', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      delApi(row.id).then(res => {
        ZrMessage.success('删除成功');
        listFun();
      });
    })
    .catch(() => {
    });
}
function handleChange(uploadFile) {
  if (uploadFile.size > 104857600) {
    ZrMessage.error('上传文件不能大于100MB！');
    upload.value.clearFiles();
    return;
  }
  uploadList.value = uploadFile;
}
const handleExceed = (files) => {
  upload.value.clearFiles();
  const file = files[0];
  file.uid = genFileId();
  upload.value.handleStart(file);
};
function closedFun() {
  uploadList.value = {};
  upload.value.clearFiles();
}
function searchClick() {
  searchForm.page = 1;
  searchForm['page_size'] = 20;
  listFun();
}
function handleSizeChange(val) {
  searchForm.page = 1;
  searchForm['page_size'] = val;
  listFun();
}
function handleCurrentChange(val) {
  searchForm.page = val;
  listFun();
}
</script>
<style lang="scss" scoped>
  .titleStyle{
    font-size: 14px;
    font-weight: 800;
    margin-bottom: 10px;
  }
</style>
