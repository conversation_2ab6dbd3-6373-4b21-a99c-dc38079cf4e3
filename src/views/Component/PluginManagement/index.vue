<template>
  <!-- 分析插件管理 -->
  <div class="main-content">
    <zr-row
      :gutter="24"
      justify="space-between"
      style="white-space: nowrap"
    >
      <zr-col :span="8">
        <zr-button
          type="primary"
          size="default"
          @click="drawerClick('add')"
        >添加分析插件</zr-button>
      </zr-col>
      <zr-col
        :span="16"
        style="text-align:right"
      >
        <zr-form :inline="true">
          <zr-form-item>
            <zr-input
              v-model="searchForm.name"
              placeholder="插件名称"
              size="default"
              @keydown.enter.prevent="searchClick"
            >
              <template #append>
                <zr-button
                  @click="searchClick"
                >
                  <icon name="zr-search" />
                </zr-button>
              </template>
            </zr-input>
          </zr-form-item>
        </zr-form>
      </zr-col>
    </zr-row>
    <zr-table
      v-loading="loading"
      :data="tableData"
      :default-sort="{ prop: 'created_at', order: 'descending' }"
      empty-text="暂无数据"
      @sort-change="sortChange"
    >
      <zr-table-column
        label="插件名称"
        prop="name"
      >
        <template #default="scope">
          <zr-tooltip
            v-if="scope.row.name!==''"
            placement="top-start"
            :content="scope.row.name"
          >
            <p class="table-item-content">{{ scope.row.name }}</p>
          </zr-tooltip>
          <p v-else>-</p>
        </template>
      </zr-table-column>
      <zr-table-column
        label="描述"
        prop="description"
      >
        <template #default="scope">
          <zr-tooltip
            v-if="scope.row.description!==''"
            placement="top-start"
            :content="scope.row.description"
          >
            <p class="table-item-content">{{ scope.row.description }}</p>
          </zr-tooltip>
          <p v-else>-</p>
        </template>
      </zr-table-column>
      <zr-table-column
        label="类型"
        prop="plugin_type"
        width="150"
      >
        <template #default="scope">
          {{ scope.row.plugin_type==='built_in'?'内置':'自定义' }}
        </template>
      </zr-table-column>
      <zr-table-column
        label="运行参数"
        prop="params"
      >
        <template #default="scope">
          <zr-tooltip
            v-if="scope.row.params!==''"
            placement="top-start"
            :content="scope.row.params"
          >
            <p class="table-item-content">{{ scope.row.params }}</p>
          </zr-tooltip>
          <p v-else>-</p>
        </template>
      </zr-table-column>
      <zr-table-column
        label="适用操作系统"
        show-overflow-tooltip
        prop="os"
        width="150px"
      >
        <template #default="scope">
          {{ scope.row.os=='windows' ? 'Windows' : 'Linux' }}
        </template>
      </zr-table-column>
      <zr-table-column
        label="创建时间"
        prop="created_at"
        sortable
        width="180"
      >
        <template #default="scope">
          <div v-if="scope.row.created_at || scope.row.created_time_ago">
            <zr-tag v-if="scope.row.created_time_ago">{{ scope.row.created_time_ago }}</zr-tag>
            <p>{{ timeFormat(scope.row.created_at) || '-' }}</p>
          </div>
          <div v-else>-</div>
        </template>
      </zr-table-column>
      <zr-table-column
        label="操作"
        prop=""
        width="150px"
      >
        <template #default="scope">
          <div class="buttonStyle">
            <zr-button
              type="primary"
              link
              :disabled="scope.row.plugin_type==='built_in'"
              @click="downloadClick(scope.row)"
            >下载</zr-button>
            <zr-button
              type="primary"
              link
              :disabled="scope.row.plugin_type==='built_in'"
              @click="drawerClick('edit',scope.row)"
            >编辑</zr-button>
            <zr-button
              type="primary"
              link
              :disabled="scope.row.plugin_type==='built_in'"
              @click="delClick(scope.row)"
            >删除</zr-button>
          </div>
        </template>
      </zr-table-column>
    </zr-table>
    <zr-pagination
      v-if="total>0"
      :current-page="searchForm.page_index"
      :page-size="searchForm.page_size"
      :page-sizes="[20, 40, 60, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
    <zr-drawer
      v-model="drawer"
      :close-on-click-modal="false"
      :destroy-on-close="true"
      :title="title"
      @open="draOpen"
    >
      <template #default>
        <div>
          <zr-form
            ref="formRef"
            label-position="top"
            :model="form"
            :rules="rules"
          >
            <zr-form-item
              label="插件名称"
              prop="name"
            >
              <zr-input
                v-model="form.name"
                maxlength="50"
                show-word-limit
                placeholder="长度不超过50"
              />
            </zr-form-item>
            <zr-form-item
              label="适用操作系统"
              prop="os"
            >
              <zr-select
                v-model="form.os"
              >
                <zr-option
                  label="Windows"
                  value="windows"
                />
                <zr-option
                  label="Linux"
                  value="linux"
                />
              </zr-select>
            </zr-form-item>
            <zr-form-item
              prop="params"
              label="运行参数"
            >
              <zr-input
                v-model="form.params"
                type="textarea"
                maxlength="100"
                :rows="3"
                show-word-limit
                placeholder="请输入运行参数,0-100位"
              />
            </zr-form-item>
            <zr-form-item
              prop="description"
              label="描述"
            >
              <zr-input
                v-model="form.description"
                type="textarea"
                maxlength="100"
                :rows="3"
                show-word-limit
                placeholder="请输入描述,0-100位"
              />
            </zr-form-item>
          </zr-form>
          <div>
            <p
              class="titleStyle"
            >上传文件</p>
            <zr-upload
              ref="upload"
              drag
              multiple
              :limit="1"
              action="#"
              :auto-upload="false"
              :on-exceed="handleExceed"
              :on-change="handleChange"
            >
              <template #trigger>
                <Icon
                  name="zr-cloud-upload-fill-b"
                  class="el-icon--upload"
                />
                <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
              </template>
            </zr-upload>
          </div>
        </div>
      </template>
      <template #footer>
        <div style="flex: auto">
          <zr-button
            :loading="saveLoading"
            type="primary"
            @click="saveClick(formRef)"
          >确定</zr-button>
          <zr-button @click="drawer=false">取消</zr-button>
        </div>
      </template>
    </zr-drawer>
  </div>
</template>
<script setup>
import { ref } from 'vue';
import { ZrMessage, ZrMessageBox } from 'qianji-ui';
import { addPlugins, pluginsList, pluginsDetail, editPlugins, deletePlugins, downloadPlugins } from '@/api/component/pluginManagement';
import { genFileId } from 'element-plus';
import { timeFormat } from '@/utils/formatting';
import { useCookies } from 'vue3-cookies';
const { cookies } = useCookies();
const tableData = ref([]);
const loading = ref(false);
const total = ref(0);
const searchForm = ref({
  name: '',
  'page_size': 20,
  'page_index': 1,
  sort: '-created_at'
});
const form = ref({
  name: '',
  os: 'windows',
  params: '',
  description: ''
});
const rules = reactive({
  name: [
    {
      required: true,
      message: '请输入插件名称',
      trigger: 'blur'
    }
  ]
});
const drawer = ref(false);
const rowId = ref('');
const title = ref('');
const formRef = ref();
const upload = ref();
const uploadList = ref({});
const saveLoading = ref(false);
onMounted(() => {
  listFun();
});
function draOpen() {
  uploadList.value = {};
  upload.value.clearFiles();
}

function listFun() {
  loading.value = true;
  pluginsList(searchForm.value).then(res => {
    tableData.value = res.data.data.list || [];
    total.value = res.data.data.total || 0;
  }).finally(() => {
    loading.value = false;
  });
}
function searchClick() {
  searchForm.value['page_index'] = 1;
  listFun();
}
function downloadClick(row) { // 下载
  axios({
    url: window.BASE_URL + '/mfp-service/api/v1/plugins/' + row.id + '/download',
    method: 'get',
    responseType: 'blob',
    headers: { Token: cookies.get('dsp_token') }
  }).then((res) => {
    if (res.data.type == 'application/octet-stream') {
      const blob = new Blob([res.data], { type: 'application/py' }); // 返回的文件流数据下载成py格式
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      var name = row.file_name;
      link.download = name || '';
      link.href = url;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  });
}
function drawerClick(type, row) { // 打开弹窗
  title.value = type == 'add' ? '添加分析插件' : '编辑分析插件';
  if (type == 'edit') {
    rowId.value = row.id;
    pluginsDetail(row.id).then(res => {
      if (res.data.code == 0) {
        form.value = res.data.data;
      } else {
        form.value = {};
      }
    });
  } else {
    form.value = {};
    form.value.os = "windows";
  }
  drawer.value = true;
}

function delClick(row) {
  ZrMessageBox.confirm('确认删除?', '确认操作', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    deletePlugins(row.id).then(res => {
      ZrMessage.success('删除成功');
      listFun();
    });
  });
}

function sortChange(data) { // 排序
  const { prop, order } = data;
  if (order === 'ascending') {
    searchForm.value.sort = '+' + prop;
  } else {
    searchForm.value.sort = '-' + prop;
  }
  listFun();
}

// --添加\编辑

const handleExceed = (files) => {
  upload.value.clearFiles();
  const file = files[0];
  file.uid = genFileId();
  upload.value.handleStart(file);
};

function handleChange(response) {
  const fileType = ["py"]; // py类型
  const zipFileType = fileType.includes(response.name.split('.').pop().toLowerCase());
  if (!zipFileType) {
    ZrMessage.error('只支持py格式文件');
    uploadList.value = {};
    return false;
  }
  uploadList.value = response;
}

const saveClick = async(formEl) => { // 添加、编辑分析插件
  if (!formEl) return;
  await formEl.validate((valid, fields) => {
    if (valid) {
      const data = new FormData();
      data.append('name', form.value.name);
      data.append('os', form.value.os);
      data.append('description', form.value.description || '');
      data.append('params', form.value.params || '');
      data.append('file', uploadList.value.raw || null);
      saveLoading.value = true;
      if (title.value === '添加分析插件') {
        if (!uploadList.value.raw) {
          ZrMessage.error('请上传文件');
          saveLoading.value = false;
          return;
        }
        addPlugins(data).then(res => {
          ZrMessage.success('添加成功');
          drawer.value = false;
          listFun();
        }).finally(() => {
          saveLoading.value = false;
        });
      } else {
        editPlugins(rowId.value, data).then(res => {
          ZrMessage.success('修改成功');
          drawer.value = false;
          listFun();
        }).finally(() => {
          saveLoading.value = false;
        });
      }
    } else {
      console.log('error submit!', fields);
    }
  });
};

function handleSizeChange(val) {
  searchForm.value['page_index'] = 1;
  searchForm.value['page_size'] = val;
  listFun();
}
function handleCurrentChange(val) {
  searchForm.value['page_index'] = val;
  listFun();
}

</script>
<style lang="scss" scoped>
  .titleStyle{
    font-size: 14px;
    font-weight: 800;
    margin-bottom: 10px;
  }
</style>
