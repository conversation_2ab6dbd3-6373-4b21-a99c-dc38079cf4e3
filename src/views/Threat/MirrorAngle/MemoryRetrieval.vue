<template>
  <!-- 内存检索 -->
  <div class="data-search">
    <div class="search-zone">
      <zr-radio-group
        v-model="tabType"
        size="default"
        @change="radioChange"
      >
        <zr-radio-button label="binary">二进制检索</zr-radio-button>
        <zr-radio-button label="str">字符串检索</zr-radio-button>
      </zr-radio-group>
      <zr-form
        ref="form"
        :model="searchForm"
        labzr-width="120px"
        size="default"
        style="margin: 20px 0;"
        @submit.prevent
      >
        <zr-row :gutter="24">
          <zr-col :span="18">
            <!-- <zr-form-item
              v-if="searchType=='str'"
              label="字符串/正则"
            >
              <zr-input
                v-model="searchForm.name"
                :placeholder="searchType!=='str'?'例：6D 69 67 72 61 74 69 6F 6E':''"
                @keyup.enter="searchClick"
              />
            </zr-form-item> -->
            <zr-form-item
              v-if="searchType=='str' || searchType=='regex'"
              class="hexStyle"
            >
              <zr-select
                v-model="searchType"
                @change="searchTypeChange"
              >
                <zr-option
                  label="字符串"
                  value="str"
                />
                <zr-option
                  label="正则表达式"
                  value="regex"
                />
              </zr-select>
              <zr-input
                v-model="searchForm.name"
                style="margin-left: 10px;"
                :placeholder="searchType =='str'?'请输入字符串':'请输入正则表达式'"
                @keyup.enter="searchClick"
              />
            </zr-form-item>
            <zr-form-item
              v-else
              class="hexStyle"
            >
              <zr-select
                v-model="searchType"
                @change="searchTypeChange"
              >
                <zr-option
                  label="二进制数值"
                  value="binary"
                />
                <zr-option
                  label="十六进制数值"
                  value="hex"
                />
              </zr-select>
              <zr-input
                v-model="searchForm.name"
                style="margin-left: 10px;"
                :placeholder="searchType=='hex'?'例：6D 69 67 72 61 74 69 6F 6E':'例：1110'"
                @keyup.enter="searchClick"
              />
            </zr-form-item>
          </zr-col>
          <zr-col
            :span="6"
            style="display: flex;justify-content: space-between;"
          >
            <zr-form-item>
              <zr-button
                type="primary"
                @click="btnLoading ? stopClick() : searchClick()"
              >{{ btnLoading?'停止搜索':'搜 索' }}</zr-button>
            </zr-form-item>
            <!-- <zr-button
              v-if="btnLoading"
              type="danger"
              style="float: right;"
              link
              @click="stopClick"
            >停止搜索</zr-button> -->
          </zr-col>
        </zr-row>
      </zr-form>
      <p
        v-if="totalBool"
        class="num-all"
      >共搜索到 {{ tableData.length }} 个</p>
      <zr-table
        v-loading="loading"
        :data="tableData"
        :empty-text="btnLoading?'搜索中':'暂无数据'"
        max-height="500"
        style="width: 100%"
        highlight-current-row
        @current-change="handleCurrentChange"
      >
        <zr-table-column
          type="index"
          width="100"
        />
        <zr-table-column
          prop="offset"
          label="地址偏移量"
        />
        <zr-table-column
          prop="pid"
          label="进程"
          width="150"
        />
        <zr-table-column
          prop="match_data"
          label="匹配结果"
        />
      </zr-table>
    </div>
    <div
      class="content-zone"
    >
      <HexPanel
        :hex-data="hexData"
      />
    </div>
  </div>
</template>
<script setup>
import { ref, reactive, defineProps, onBeforeUnmount } from 'vue';
import HexPanel from '@/components/common/HexPanel.vue';
import { sendWebsocket, closeWebsocket } from '@/utils/websocket.js';
import { ZrMessage } from 'qianji-ui';
const props = defineProps({
  id: String,
  detailData: Object
});
const tabType = ref('binary'); // 默认tab标签为二进制检索
const searchType = ref('hex');
const searchForm = reactive({
});
const tableData = ref([]);
const hexData = ref({});
const loading = ref(false);
const btnLoading = ref(false);
const totalBool = ref(false);
const num = ref(0);
onBeforeUnmount(() => {
  closeWebsocket();
});

function stopClick() {
  closeWebsocket();
  btnLoading.value = false;
}
// ws连接成功，后台返回的ws数据，组件要拿数据渲染页面等操作
function wsMessage(data) {
  if (btnLoading.value) { // 停止搜索 前端拦截数据
    tableData.value.push(data);
  }
 
  // 这里写拿到数据后的业务代码
}
// ws连接失败，组件要执行的代码
function wsError() {
  // 比如取消页面的loading
  if (num.value === 1) { // 预防问题: 多次点击按钮，websocket响应慢的问题
    num.value = 0;
    btnLoading.value = false;
  } else {
    num.value--;
  }
}

function searchTypeChange() {
  searchForm.name = '';
}

function searchClick() {
  if (!searchForm.name) {
    ZrMessage.error('请输入搜索内容');
    return;
  }
  num.value++;
  tableData.value = [];
  hexData.value = {};
  totalBool.value = true;
  loading.value = true;
  btnLoading.value = true;
  setTimeout(() => {
    loading.value = false;
  }, 500);
  // 防止用户多次连续点击发起请求，所以要先关闭上次的ws请求。
  closeWebsocket();
  // 跟后端协商，需要什么参数数据给后台
  const obj = {
    type: searchType.value,
    name: searchForm.name,
    'analysis_id': props.id,
    bucket: props.detailData.minio_bucket,
    path: props.detailData.minio_path
  };
  // 发起ws请求
  var ip = window.BASE_URL.split('://');
  var deal = ip[0] === 'https' ? 'wss' : 'ws';
  var url = deal + '://' + ip[ip.length - 1] + '/mfp-service/api/v1/threat_detection/' + props.detailData.os + '/' + props.id + '/search' +
  '?type=' + searchType.value + '&name=' + searchForm.name + '&analysis_id=' + props.id + '&bucket=' + props.detailData.minio_bucket + '&path=' + props.detailData.minio_path;
  sendWebsocket(url, obj, wsMessage, wsError);
}
function radioChange() {
  searchForm.name = '';
  totalBool.value = false;
  tableData.value = [];
  hexData.value = {};
  if (tabType.value !== 'binary') {
    searchType.value = 'str';
  } else {
    searchType.value = 'hex';
  }
}
const handleCurrentChange = (val) => {
  hexData.value = val;
};
</script>
<style lang="scss" scoped>
.data-search{
  display: flex;
  >div{
      width: calc(40% - 10px);
      // padding-bottom: 40px;
      // height: 300px;
  }
  >div:nth-child(2){
    margin-left: 6%;
  }
  .content-zone{
    margin-top: 48px;
  }
}
.num-all{
  font-size: 14px;
  color: #606266;
  // margin: 12px 0 0 12px;
}
.el-table{
  margin-top: 8px;
}
.hexStyle{
  :deep(.el-form-item__content) {
    display: contents;
  }
}

</style>
