<template>
  <!-- 威胁检测 -->
  <div v-loading="loading">
    <div
      v-if="Object.keys(threatList).length === 0&&!filtration"
      class="data-empty"
    >
      <Icon
        name="zr-safe-a"
        :size="40"
        color="#07b630"
      />
      <p>未检出威胁</p>
    </div>
    <div v-else>
      <!-- <div class="filtration">
        <span>过滤误报信息</span>
        <zr-switch
          v-model="filtration"
          @change="filtrationChange"
        />
        <zr-button
          style="margin-left: 12px;"
          :type="studyAndJudge? 'success' : 'primary'"
          @click="studyAndJudgeClick"
        >{{ studyAndJudge?'撤销研判':'完成研判' }}</zr-button>
      </div> -->
      <div
        v-for="(item,index) in os==='Windows'?threatOption:[threatOption[0]]"
        :key="item.title"
      >
        <div
          v-if="threatList[item.key]"
        >
          <div
            class="title-pie"
            style="margin-bottom:12px"
          ><span />{{ item.title }}</div>
          <div
            v-for="(row,len) in threatData[item.key]"
            :key="len"
            style="margin-bottom: 20px;"
          >
            <zr-collapse
              accordion
            >
              <zr-collapse-item
                :name="index+1"
              >
                <template #title>
                  <div
                    class="threat-title"
                  >
                    <p
                      class="level-piece"
                      :style="'background:'+levelPiece(row.highest_level)"
                    />
                    <!-- <h2>【{{ item.title }}】</h2> -->
                    <p
                      v-if="item.name"
                      class="threat-name"
                      :style="'color:'+levelPiece(row.highest_level)"
                    >{{ row[item.name] }} <span v-if="row[item.num]">（{{ row[item.num] }}个威胁项）</span></p>
                    <div class="threat-tag">
                      <p
                        v-for="i in row[item.tag]"
                        :key="i"
                        :class="'tag-custom ' + i.threat_level"
                      >
                        {{ i.threat_tag }}
                      </p>
                    </div>
                 
                  </div>
                </template>
                <!-- 进程基本信息 -->
                <div
                  v-if="item.mag"
                  class="course-mag"
                >
                  <p
                    v-for="nape in item.mag"
                    :key="nape.key"
                  >
                    {{ nape.label }}：{{ row[nape.key]||'-' }}
                    <zr-button
                      type="primary"
                      link
                      left-icon="zr-copy-file"
                      @click="copyClick(row[nape.key])"
                    />
                  </p>
                </div>
                <!-- 详细列表信息 -->
                <div
                  v-for="(list,listIndex) in item.list"
                  :key="listIndex"
                  class="threat-list"
                >
                  <div v-if="item.key=='ps_info' ? row[list.tabelKey] : row">
                    <h2 v-if="list.typeLabel">{{ list.typeLabel }}</h2>
                    <zr-table
                      :data="filtration? (item.key=='ps_info' ? row[list.tabelKey].filter(item=>item.judge_type!=='误报'): [row].filter(item=>item.judge_type!=='误报')):( item.key=='ps_info' ? row[list.tabelKey] :[row])"
                      empty-text="暂无数据"
                    >
                      <zr-table-column
                        v-for="prop in os==='Linux'&& (list.tabelKey=='dll_list' || list.tabelKey=='net_scan_list') ? list.listLinuxOption : list.listOption"
                        :key="prop.label"
                        :prop="prop.prop"
                        :label="prop.label"
                        :width="prop.width"
                        show-overflow-tooltip
                      >
                        <template #default="scope">
                          <div>
                            {{ scope.row[prop.prop] || '-' }}
                          </div>
                        </template>
                      </zr-table-column>
                      <zr-table-column
                        prop="threat_info"
                        label="威胁标签"
                        width="350"
                      >
                        <template #default="scope">
                          <zr-scrollbar
                            v-if="scope.row.threat_info"
                            :wrap-class="['scrollbar-wrapper', 'wrapper-custom']"
                            :wrap-style="[{ 'max-height': '50px', height: '50px'}]"
                          >
                            <p
                              v-for="o in scope.row['threat_info']"
                              :key="o"
                              :class="'tag-custom ' + o.threat_level"
                            >
                              {{ o.threat_tag }}
                            </p>
                          </zr-scrollbar>
                        </template>
                      </zr-table-column>
                      <!-- <zr-table-column
                        prop="judge_type"
                        label="研判分析状态"
                        width="200"
                      >
                        <template #default="scope">
                          <div
                            class="judge_type"
                            :style="'color:' + judgeTypeFun(scope.row.judge_status,scope.row.judge_type)"
                          >
                            <Icon
                              :name="scope.row.judge_status==='研判完成' ? 'zr-circle-seleted-fill' : scope.row.judge_status==='研判中' ? 'zr-wait-fill' : 'zr-circle-reduce-fill'"
                              :size="18"
                              :color="judgeTypeFun(scope.row.judge_status,scope.row.judge_type)"
                            />
                            <p>{{ scope.row.judge_status||'未研判' }}</p>
                            <p v-if="scope.row.judge_status==='研判完成'">({{ scope.row.judge_type }})</p>
                          </div>
                        </template>
                      </zr-table-column> -->
                      <zr-table-column
                        prop="address"
                        label="操作"
                        width="160"
                      >
                        <template #default="scope">
                          <div>
                            <zr-button
                              link
                              type="primary"
                              @click="detailClick(scope.row,list.listOption)"
                            >详情</zr-button>
                            <zr-button
                              link
                              type="primary"
                              @click="downloadClick(scope.row)"
                            >下载</zr-button>
                          </div>
                        </template>
                      </zr-table-column>
                    </zr-table>
                  </div>

                </div>
              </zr-collapse-item>
            </zr-collapse>
          </div>
          <zr-pagination
            v-if="threatList[item.key].length>0"
            :current-page="item.page"
            :page-size="item.pageSize"
            :page-sizes="[10, 20, 30]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="threatList[item.key].length"
            @size-change="(val)=>handleSizeChange(item,threatList[item.key],val)"
            @current-change="(val)=>handleCurrentChange(item,threatList[item.key],val)"
          />

        </div>
      </div>
    </div>
    <zr-drawer
      v-model="threatDrawer"
      title="详情"
      :close-on-click-modal="false"
    >
      <template #default>
        <div
          v-if="threatDrawer"
          class="threat-drawer"
        >
          <!-- <div class="develop">
            <zr-dropdown
              trigger="click"
              @command="developCommand"
            >
              <zr-button :type="menaceLevelFun()">{{ commandName===''||commandName==='取消标记'?'威胁研判':commandName }}<Icon
                name="zr-down"
                class="el-icon--right"
              /> </zr-button>
              <template #dropdown>
                <zr-dropdown-menu>
                  <zr-dropdown-item
                    v-for="item in menaceLevel"
                    :key="item.color"
                    :icon="commandName===item.name?Check:''"
                    :command="item.name"
                    :style="'color:'+item.color"
                  >{{ item.name }}</zr-dropdown-item>
                </zr-dropdown-menu>
              </template>
            </zr-dropdown>
          </div> -->

          <h3 class="msg-label">基本信息</h3>
          <div
            v-for="item in detailList"
            :key="item.prop"
            class="basic"
            :label="item.label"
          >
            <span>{{ item.label }}：</span>
            <div
              v-if="apiFun(item)"
              class="system"
            >
              {{ detailRow[item.prop] }}
              <zr-button
                type="primary"
                link
                left-icon="zr-copy-file"
                @click="copyClick(detailRow[item.prop])"
              />
              <div>
                <p
                  v-for="row in systemList"
                  :key="row.id"
                  @click="systemClick(row,detailRow[item.prop])"
                >
                  <span
                    v-if="row.switch"
                  >
                    <zr-tooltip
                      class="box-item"
                      effect="dark"
                      :content="row.search_engine"
                      placement="top"
                    >
                      <img
                        class="custom-image"
                        :src="`data:image/png;base64,${row.image}`"
                      >
                    </zr-tooltip>
                
                  </span>
                </p>
              </div>

            </div>
            <span v-else>{{ detailRow[item.prop] }}</span>
            <!-- <span v-if="item.key == 'size'">{{ numDelivery(getData[item.key]) }}</span>
            <span v-else-if="getData[item.key] !==''">{{ getData[item.key] }}</span>
            <span v-else> - </span> -->
          </div>
<!--          <div class="basic">
            <h3 class="msg-label">研判备注信息</h3>
            <zr-button
              type="primary"
              link
              style="margin-left: 8px;"
              @click="isVisible = !isVisible"
            >展开/收缩</zr-button>
          </div>-->
          <transition name="collapse">
            <div
              v-if="isVisible"
              class="collapse-content"
            >
              <v-md-editor
                v-model="judgeRemark"
                height="400px"
                :disabled-menus="[]"
                @upload-image="handleUploadImage"
              />
            </div>
          </transition>
          <h3
            class="msg-label"
            style="margin: 14px 0;"
          >威胁检测信息</h3>
          <zr-collapse
            v-for="(row,index) in tableDataDetail"
            :key="row.id"
            accordion
            style="margin-bottom: 16px;"
            @change="expandChange(row)"
          >
            <zr-collapse-item
              :name="index+1"
            >
              <template #title>
                <div
                  class="threat-title tabel-list"
                >
                  <p
                    class="level-piece"
                    :style="'background:'+levelPiece(row.threat_info[0].threat_level)"
                  />
                  <p style="margin: 0 20px;">
                    <span>{{ row.rule_type=='IP'?'威胁情报告警':row.rule_type=='yara'?'Yara规则告警':'HASH威胁情报告警' }}：</span>
                    <span>{{ row.rule_name }}</span>
                    <span>{{ row.indicator }}</span>
                  </p>
                  <div class="threat-tag">
                    <p
                      v-for="i in row.threat_info"
                      :key="i"
                      :class="'tag-custom ' + i.threat_level"
                    >
                      {{ i.threat_tag }}
                    </p>
                  </div>
                  <!-- <div style="float: right;">
                    <zr-button
                      link
                      type="success"
                      :disabled="studyAndJudge"
                      @click="misdeclarationClick(row,row.judgement_result&&row.judgement_result==='确认误报'?'取消误报':'确认误报')"
                    >
                      {{ row.judgement_result&&row.judgement_result==='确认误报'?'取消误报':'确认误报' }}
                    </zr-button>
                  </div> -->
                </div>
              </template>
              <!-- 进程基本信息 -->
              <div class="detail-msg">
                <div v-if="row.rule_type==='yara'">
                  <div class="rule-detail">
                    <p
                      v-for="item in ruleDetailOption"
                      :key="item.prop"
                      class="basic"
                      :label="item.label"
                    >
                      <span>{{ item.label }}：</span>
                      <span v-if="item.prop==='threat_info'">
                        <p
                          v-for="tag in row['threat_info']"
                          :key="tag"
                          :class="'tag-custom ' + tag.threat_level"
                        >
                          {{ tag.threat_tag }}
                        </p>
                      </span>
                      <span v-else-if="item.prop==='risk_level'">
                        <span
                          class="top-tag"
                          :type="row.threat_info[0].threat_level"
                        >
                          <span class="left-icon"><Icon :name="row.threat_info[0].threat_level==='safe' ? 'zr-safe-a' : 'zr-threaten-fill'" /></span>
                          <span class="text">{{ riskLevelFun(row.threat_info[0].threat_level) }}</span>
                        </span>
                      </span>

                      <span v-else-if="item.prop==='reference'">{{ row[item.prop] || '-' }} <zr-button
                        v-if="row[item.prop]"
                        type="primary"
                        link
                        @click="urlFun(row[item.prop])"
                      >访问</zr-button></span>
                      <span v-else>{{ row[item.prop] || '-' }}</span>
                    </p>
                  </div>
                  <div class="feature">
                    <div class="feature-nape">
                      <p>匹配特征（{{ Object.keys(row.features).length || 0 }}个）</p>
                      <div>
                        <zr-radio-group
                          v-model="row.featureRadio"
                          @change="featureChange(row)"
                        >
                          <zr-radio
                            v-for="(value,key) in row.features"
                            :key="key"
                            :label="key"
                            border
                          >{{ key }}</zr-radio>
                        </zr-radio-group>
                      </div>

                    </div>
                    <div class="feature-msg">
                      <div
                        v-if="row.featureRadio!==''"
                        class="feature-cut"
                      >
                        <zr-button
                          type="primary"
                          plain
                          size="small"
                          :disabled="row.featureNum===0"
                          :loading="cutLoading"
                          @click="cutClick('top',row)"
                        >上一处</zr-button>
                        <zr-button
                          type="primary"
                          plain
                          size="small"
                          :loading="cutLoading"
                          :disabled="row.featureNum===(row.features&&row.featureRadio ? row.features[row.featureRadio].length-1 : 0)"
                          @click="cutClick('bottom',row)"
                        >下一处</zr-button>
                      </div>
                      <HexPanel
                        :hex-data="row.hexData"
                      />
                    </div>
                  </div>
                </div>
                <div v-else>
                  <div class="rule-detail">
                    <p
                      v-for="item in ruleHashDetailOption"
                      :key="item.prop"
                      class="basic"
                      :label="item.label"
                    >
                      <span>{{ item.label }}：</span>
                      <span v-if="item.prop==='threat_info'">
                        <p
                          v-for="tag in row['threat_info']"
                          :key="tag"
                          :class="'tag-custom ' + tag.threat_level"
                        >
                          {{ tag.threat_tag }}
                        </p>
                      </span>
                      <span
                        v-else-if="item.prop==='indicator'"
                        style="display: inline-flex;"
                      >
                        <zr-tooltip
                          class="box-item"
                          effect="dark"
                          :content="row[item.prop]"
                          placement="top"
                        >
                          <span class="indicatorStyle">{{ row[item.prop] || '-' }}</span>
                        </zr-tooltip>

                        <zr-button
                          type="primary"
                          link
                          left-icon="zr-copy-file"
                          @click="copyClick(row[item.prop])"
                        />
                      </span>
                      <span v-else-if="item.prop==='type'">{{ row[item.prop] || '-' }}/{{ row.subtype || '-' }}</span>
                      <span v-else>{{ row[item.prop] || '-' }}</span>
                    </p>
                  </div>
                </div>
              </div>
            </zr-collapse-item>
          </zr-collapse>
        </div>
      </template>
      <template #footer>
        <div style="flex: auto">
          <!-- <zr-button
            type="primary"
            :disabled="commandName===''||studyAndJudge"
            @click="saveClick('研判完成')"
          >确定{{ commandName==='取消标记'?'':'（研判完成）' }}</zr-button>
          <zr-button
            type="primary"
            :disabled="studyAndJudge"
            @click="saveClick('研判中')"
          >保存（研判中）</zr-button> -->
          <zr-button @click="cancelClick">关 闭</zr-button>
        </div>
      </template>
    </zr-drawer>
  </div>
</template>
<script setup>
import { ref, defineProps, defineEmits, getCurrentInstance } from 'vue';
import HexPanel from '@/components/common/HexPanel.vue';
import { threatInfoApi, threatInfoDetailApi, threatInfoDetailContentApi, fileDownload, judgement, judgementResult, rulesJudgement, threatsJudgement, threatsUpload } from '@/api/threat/mirrorAngle/index';
import { systemApiList } from '@/api/system/apiManagement';
import { ZrMessage } from 'qianji-ui';
import { Check } from '@element-plus/icons-vue';
const { proxy } = getCurrentInstance();
const emits = defineEmits(['initFun']);
const props = defineProps({
  os: String,
  id: String,
  analysisId: String,
  detailData: Object
});
const judgeRemark = ref('');
const tableDataDetail = ref([]);
const threatDrawer = ref(false);
const threatList = ref({});
const loading = ref(true);
const detailList = ref([]);
const filtration = ref(false);
const itemId = ref('');
const cutLoading = ref(false);
const studyAndJudge = ref(false);
const detailRow = ref({});
const resultList = ref({});
const commandName = ref('');
const systemList = ref([]);
const isVisible = ref(false);
const threatData = ref({});
const menaceLevel = [
  {
    name: '确认威胁',
    color: '#fd4747',
    type: 'danger'
  },
  {
    name: '可疑威胁',
    color: '#ff9900',
    type: 'warning'
  },
  {
    name: '误报',
    color: '#07b630',
    type: 'success'
  },
  {
    name: '取消标记',
    color: '#1890ff',
    type: 'primary'
  }
];
const threatOption = [
  {
    title: '进程威胁检测',
    name: 'ps_name_pid',
    tag: 'threat_info',
    num: 'num_threats',
    key: 'ps_info',
    page: 1,
    pageSize: 10,
    mag: [
      {
        label: '进程名称',
        key: 'ps_name'
      },
      {
        label: '进程ID',
        key: 'pid'
      },
      {
        label: '父进程ID',
        key: 'ppid'
      }
    ],
    list: [
      {
        typeLabel: '进程信息',
        tabelKey: 'ps_basic_list',
        listOption: [
          {
            label: '进程名',
            prop: 'ps_name',
            width: 200
          },
          {
            label: '进程ID',
            prop: 'pid',
            width: 100
          },
          {
            label: '父进程ID',
            prop: 'ppid',
            width: 100
          },
          {
            label: '文件路径',
            prop: 'file_path'
          },
          {
            label: 'UID',
            prop: 'uid'
          },
          {
            label: 'GID',
            prop: 'gid'
          },
          {
            label: '启动时间',
            prop: 'start_time'
          }
          // {
          //   label: '威胁标签',
          //   prop: 'threat_info'
          // }
        ]
      },
      {
        typeLabel: '动态链接库',
        tabelKey: 'dll_list',
        listLinuxOption: [
          {
            label: '进程',
            prop: 'process',
            width: 200
          },
          {
            label: '文件路径',
            prop: 'file_path'
          }
        ],
        listOption: [
          {
            label: '名称',
            prop: 'dll_name',
            width: 200
          },
          {
            label: '大小(KB)',
            prop: 'size',
            width: 100
          },
          {
            label: '进程',
            prop: 'process',
            width: 200
          },
          {
            label: '加载时间',
            prop: 'load_time',
            width: 200
          },
          {
            label: '文件路径',
            prop: 'file_path'
          },
          {
            label: '载入计数',
            prop: 'load_count'
          }
          // {
          //   label: '威胁标签',
          //   prop: 'threat_info'
          // }
        ]
      },
      {
        typeLabel: '网络连接',
        tabelKey: 'net_scan_list',
        listLinuxOption: [
          {
            label: '协议',
            prop: 'protocol',
            width: 100
          },
          {
            label: '进程',
            prop: 'process'
          },
          {
            label: '本地地址',
            prop: 'local_addr',
            width: 200
          },
          {
            label: '本地端口',
            prop: 'local_port',
            width: 100
          },
          {
            label: '远程地址',
            prop: 'foreign_addr',
            width: 200
          },
          {
            label: '远程端口',
            prop: 'foreign_port'
          },
          {
            label: '状态',
            prop: 'state'
          }
        ],
        listOption: [
          {
            label: '协议',
            prop: 'protocol',
            width: 100
          },
          {
            label: '进程',
            prop: 'process'
            // width: 200
          },
          {
            label: '本地地址',
            prop: 'local_addr',
            width: 200
          },
          {
            label: '本地端口',
            prop: 'local_port',
            width: 100
          },
          {
            label: '远程地址',
            prop: 'foreign_addr',
            width: 200
          },
          {
            label: '远程端口',
            prop: 'foreign_port'
            // width: 100
          },
          {
            label: '状态',
            prop: 'state'
          }
          // {
          //   label: '创建时间',
          //   prop: 'created'
          // }
          // {
          //   label: '威胁标签',
          //   prop: 'threat_info'
          // }
        ]
      }
    ]
  },
  {
    title: '注册表文件',
    name: 'path',
    tag: 'threat_info',
    key: 'registry_file',
    page: 1,
    pageSize: 10,
    list: [
      {
        tabelKey: 'registry_file_list',
        listOption: [
          {
            label: '偏移量',
            prop: 'offset'
          },
          {
            label: '完整路径',
            prop: 'path'
          }
          // {
          //   label: '威胁标签',
          //   prop: 'threat_info'
          // }
        ]
      }
    ]
  },
  {
    title: '驱动模块',
    name: 'name',
    tag: 'threat_info',
    key: 'driver_module',
    page: 1,
    pageSize: 10,
    list: [
      {
        tabelKey: 'driver_module_list',
        listOption: [
          {
            label: '名称',
            prop: 'name'
          },
          {
            label: '路径',
            prop: 'path'
          },
          {
            label: '大小(B)',
            prop: 'size'
          }
          // {
          //   label: '威胁标签',
          //   prop: 'threat_info'
          // }
        ]
      }
    ]
  }
];
const ruleDetailOption = [
  {
    label: '规则名称',
    prop: 'rule_name'
  },
  {
    label: '规则描述',
    prop: 'description'
  },
  {
    label: '威胁标签',
    prop: 'threat_info'
  },
  {
    label: '级别',
    prop: 'risk_level'
  },
  {
    label: 'ATT&CK映射',
    prop: 'mitre_attack'
  },
  {
    label: 'KillChain映射',
    prop: 'kill_chain'
  },
  {
    label: '参考链接',
    prop: 'reference'
  }
];
const ruleHashDetailOption = [
  {
    label: '类型/子类型',
    prop: 'type'
  },
  {
    label: 'IOC指标',
    prop: 'indicator'
  },
  {
    label: '关联组织',
    prop: 'actors'
  },
  {
    label: '威胁标签',
    prop: 'threat_info'
  },
  {
    label: 'ATT&CK映射',
    prop: 'mitre_attack'
  },
  {
    label: 'KillChain映射',
    prop: 'kill_chain'
  }
];

onBeforeMount(() => {
  studyAndJudge.value = props.detailData.judgement_completed;
  threatInfoFun();
});

function threatListFun(item, list) { // 静态分页数据处理
  var arr = list.slice(item.page == 1 ? 0 : ((item.page - 1) * item.pageSize), (item.pageSize * item.page));
  threatData.value[item.key] = arr;
}
function handleSizeChange(item, list, val) { // 每页条数
  item.page = 1;
  item.pageSize = val;
  threatListFun(item, list);
}
function handleCurrentChange(item, list, val) { // 第几页
  item.page = val;
  threatListFun(item, list);
}

function filtrationChange() {
  threatInfoFun();
}
function saveClick(status) { // 保存
  var obj = {
    note: judgeRemark.value,
    'judgement_result': commandName.value,
    'judgement_status': status
  };
  threatsJudgement(props.id, itemId.value, obj).then(res => {
    threatDrawer.value = false;
    ZrMessage.success('修改成功' + status);
    setTimeout(() => {
      threatInfoFun();
    }, 1000);
  });
}
function handleUploadImage(event, insertImage, files) { // 上传本地图片
  const data = new FormData();
  data.append('picture', files[0]);
  threatsUpload(props.id, itemId.value, data).then(res => {
    var url = '![Description](' + res.data.data.picture_addr + ')';
    judgeRemark.value += url;
  });
}
function misdeclarationClick(row, val) { // 规则研判
  rulesJudgement(props.id, row.id, { 'judgement_result': val }).then(res => {
    ZrMessage.success(val + '成功');
    row['judgement_result'] = val;
  });
}
function studyAndJudgeClick() { // 研判状态修改
  studyAndJudge.value = !studyAndJudge.value;
  var obj = {
    'judgement_completed': studyAndJudge.value
  };
  judgement(props.id, obj).then(res => {
    ZrMessage.success(studyAndJudge.value ? '研判成功' : '撤销研判成功');
    emits('initFun');
  });
}

function featureChange(row) { // 特征选择
  row.featureNum = 0;
  featureFun(row);
}
function featureFun(row) {
  cutLoading.value = true;
  var obj = {
    'item_id': itemId.value,
    'minio_bucket': row.minio_bucket,
    'minio_path': row.minio_path,
    ...row.features[row.featureRadio][row.featureNum]
  };
  threatInfoDetailContentApi(props.id, obj).then(res => {
    row.hexData = res.data.data;
  }).finally(() => {
    setTimeout(() => {
      cutLoading.value = false;
    }, 500);
  });
}
function judgeTypeFun(status, type) {
  var color = "";
  switch (status) {
    case '研判完成':
      switch (type) {
        case '确认威胁':
          color = "#fd4747";
          break;
        case '可疑威胁':
          color = "#ff9900";
          break;
        case '误报':
          color = "#07b630";
          break;
        default:
          color = "#909399";
      }
      break;
    case '研判中':
      color = "#1890ff";
      break;
    default:
      color = "#909399";
  }
  return color;
}
function riskLevelFun(level) {
  switch (level) {
    case 'critical':
      return '致命';
    case 'high':
      return '高';
    case 'medium':
      return '中';
    case 'low':
      return '低';
    case 'information':
      return '提示';
    case 'safe':
      return '安全';
    default:
      return '';
  }
}
function cutClick(val, row) { // 特征上下一处
  if (val === 'top') {
    row.featureNum--;
  } else {
    row.featureNum++;
  }
  featureFun(row);
}
function levelPiece(level) {
  var color = '';
  switch (level) {
    case 'critical':
      color = '#AE0000';
      break;
    case 'high':
      color = '#fd4747';
      break;
    case 'medium':
      color = '#ff9900';
      break;
    case 'low':
      color = '#909399';
      break;
    case 'information':
      color = '#1890ff';
      break;
    case 'safe':
      color = '#07b630';
      break;
  }
  return color;
}
function threatInfoFun() {
  threatInfoApi(props.id, { 'is_filter': filtration.value }).then(res => {
    threatList.value = res.data.data;
    threatOption.forEach(item => {
      if (threatList.value[item.key]) {
        threatListFun(item, threatList.value[item.key]);
      }
    });
  }).finally(() => {
    loading.value = false;
  });
}
function downloadClick(row) {
  fileDownload(row.item_id, props.analysisId).then(res => {
    location.href = res.data.data.url;
  });
}
function expandChange(row) {
  row.featureRadio = '';
  row.featureNum = 0;
}
function menaceLevelFun() {
  var type = menaceLevel.find(item => item.name === commandName.value)?.type || 'primary';
  return type;
}
function apiFun(item) {
  return ['名称', '地址', 'MD5', 'SHA1', 'SHA256'].some(keyword => item.label.includes(keyword));
}
function detailClick(row, list) {
  detailRow.value = row;
  itemId.value = row.item_id;
  var data = [];
  if (row.is_net_scan) {
    data = [...list];
  } else {
    data = [...list, {
      label: 'MD5',
      prop: 'md5'
    },
    {
      label: 'SHA1',
      prop: 'sha1'
    },
    {
      label: 'SHA256',
      prop: 'sha256'
    }];
  }

  detailList.value = data;
  threatInfoDetailFun();
  judgementResult(props.id, row.item_id).then(res => { // 获取研判信息
    resultList.value = res.data.data;
    commandName.value = res.data.data.judgement_result;
    judgeRemark.value = res.data.data.note || '';
    if (judgeRemark.value && judgeRemark.value !== '') {
      isVisible.value = true;
    } else {
      isVisible.value = false;
    }
  });
}
function threatInfoDetailFun() {
  systemApiList({ page: 1, 'page_size': 999 }).then(res => { // 获取api项
    systemList.value = res.data.data.list;
  });
  threatInfoDetailApi(props.id, itemId.value).then(res => {
    tableDataDetail.value = res.data.data;
    threatDrawer.value = true;
  });
}
function urlFun(url) {
  window.open(url);
}
function systemClick(row, text) {
  window.open(row.ip + text);
}
function developCommand(val) {
  commandName.value = val;
}
function cancelClick() {
  threatDrawer.value = false;
}
function copyClick(text) { // 复制
  proxy.copyFun(text);
}

</script>
<style lang="scss" scoped>
.threat-title{
    display: flex;
    align-items: center;
    &.tabel-list{
      width: 100%;
    }
    h2{
        font-size: 15px;
        max-width: 110px;
    }
    >.threat-name{
        font-weight: 600;
        margin-right: 20px;
        // min-width: 150px;
        margin-left:10px;
    }
    .threat-tag{
      flex: 1;
    }
}
.threat-list{
    h2{
        font-size: 14px;
        margin-top: 20px;
    }
    .el-table{
        margin-top: 8px;
    }
}
:deep(.el-collapse-item__header){
    background: #f5f7fa;
}
:deep(.el-drawer.rtl){
  width: 53% !important;
}
.data-empty{
  text-align: center;
  // line-height: 60px;
  color: #07b630;
  margin: 40px auto;
  width: 120px;
  // font-size: 14px;
  .el-icon{
    margin-bottom: 8px;
  }
}
:deep(.row-expand-cover .el-table__expand-column .cell){
  display: none;
}
.level-piece{
  width: 4px;
  height: 48px;
}
.basic{
    line-height: 30px;
    color:#606266;
    display: flex;
    align-items: center;
    font-size: 14px;
}
.rule-detail{
  padding-left: 20px;
  >p{
    width: 50%;
    display: inline-block;
  }
  .indicatorStyle{
    width: 320px;
    display: inline-block;
    text-overflow: ellipsis;
    overflow: hidden;
  }
}
.threat-drawer{
  position: relative;
  .develop{
    position: absolute;
    top: 0;
    right: 28px;
  }
}

.feature{
  display: flex;
    margin-top: 20px;
  .feature-nape{
    padding-left: 20px;
    font-size: 14px;
    margin-right: 12px;
    width: 160px;
    >div{
      max-height: 550px;
      overflow: auto;
    }
  }
}

.el-radio.is-bordered{
  margin-right: 0;
  margin-top: 8px;
}
.el-radio-group{
  flex-direction: column;
}
.filtration{
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-bottom: 20px;
  font-size: 15px;
  >span{
    margin-right: 8px;
  }
}
.feature-cut{
  .el-button{
    margin: 0;
  }
}
.judge_type{
  display: flex;
  align-items: center;
  p{
    margin-left: 4px;
  }
}
.custom-image {
  margin-right: 5px;
  width:16px;
  height: auto;
  object-fit: contain; /* 保持长宽比 */
}
.system{
  display: flex;
  align-items: center;
  >div{
    display: flex;
    align-items: center;
    margin-left: 8px;
    span{
      margin-left: 8px;
      cursor: pointer;
    }
  }
}
.course-mag{
  display: flex;
  align-items: center;
  margin-top: 20px;
  p{
    margin-right: 50px;
  }
}
:deep(.el-collapse-item__content){
  padding-bottom:0px
}
.msg-label{
  font-size: 15px;
  color: #333;
}
.detail-msg{
  >div{
    background: #fff;
  }
}
.collapse-content {
  width: 100%;
  height: 400px;
  margin-top: 8px;
  transition: height .3s; /* 添加过渡效果 */
}

/* 使用 @keyframes 定义过渡效果 */
@keyframes collapse {
  0% { height: 0px; } /* 打开时宽度从0开始 */
  100% { height: 400px; } /* 打开时宽度变为300 */
}
@keyframes collapseReverse {
  0% { height: 400px; } /* 关闭时宽度从300开始 */
  100% { height: 0px; } /* 关闭时宽度变为0 */
}

.collapse-enter-active, .collapse-leave-active {
  animation: collapse .3s forwards; /* 应用定义的动画 */
}
.collapse-leave-active {
  animation-direction: reverse; /* 设置动画反向播放 */
}

</style>
