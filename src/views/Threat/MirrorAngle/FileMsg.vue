<template>
  <div class="detail-msg">
    <zr-card>
      <div class="file-msg-content">
        <zr-row :gutter="24">
          <zr-col
            v-for="(item,index) in fileOption"
            :key="item.label"
            :span="index%2==0?7:17"
          >
            <p>{{ item.label }}</p>
            <p>
              <span>{{ fileMsg[item.key] }}</span>
              <zr-button
                v-if="fileMsg[item.key]&&index!==2&&index!==4"
                type="primary"
                link
                left-icon="zr-copy-file"
                @click="copyClick(fileMsg[item.key])"
              />
            </p>
          </zr-col>
        </zr-row>
      </div>
    </zr-card>
  </div>
</template>
<script setup>
import { getCurrentInstance } from 'vue';

const props = defineProps({
  fileMsg: Object
});

const { proxy } = getCurrentInstance();
const fileOption = [
  {
    label: '文件：',
    key: 'filename'
  },
  {
    label: 'MD5：',
    key: 'md5'
  },
  {
    label: '大小 (B)：',
    key: 'file_size'
  },
  {
    label: 'SHA1：',
    key: 'sha1'
  },
  {
    label: '',
    key: ''
  },
  {
    label: 'SHA256：',
    key: 'sha256'
  }
];

function copyClick(text) { // 复制
  proxy.copyFun(text);
}
</script>
