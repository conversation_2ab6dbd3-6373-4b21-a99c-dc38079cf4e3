<template>
  <!-- 分析报告 -->
  <div>
    <div class="export">
      <zr-dropdown
        trigger="click"
        @command="handleCommand"
      >
        <span
          class="zr-dropdown-link"
          style="color: #1890ff;"
        > 导出分析报告 <Icon
          name="zr-down"
          class="el-icon--right"
        /> </span>
        <template #dropdown>
          <zr-dropdown-menu>
            <zr-dropdown-item
              v-for="item in exportOption"
              :key="item.command"
              :command="item.command"
            >{{ item.name }}</zr-dropdown-item>
          </zr-dropdown-menu>
        </template>
      </zr-dropdown>
    </div>
    <div
      id="pdfRef"
      v-loading="loading"
      class="analysis"
    >

      <h2><Icon
        name="zr-remarks"
        :size="20"
      />内存镜像分析报告：{{ dataAll.image_name }}</h2>
      <div class="basic-msg">
        <h3 class="title-pie"><span />基本信息</h3>
        <zr-row :gutter="24">
          <zr-col
            v-for="(item,index) in basicMsgOption"
            :key="item.key"
            :span="index==7 || index==8 || index==9 ? 24 : 12 "
            style="display: flex;"
          >
            <div class="msg-detail">
              <p>{{ item.label }}</p>
              <div
                v-if="item.key==='detection_record_result'||item.key==='judge_result'"
                class="detection_record_result"
              >
                <div
                  v-for="row in item.data"
                  :key="row.value"
                >
                  <div
                    v-if="baseInfo[item.key]"
                    :style="'color:'+row.color"
                  >
                    {{ row.name }}
                    <span>【{{ baseInfo[item.key][row.value]||0 }}】</span>项
                  </div>
                </div>
              </div>
              <div
                v-else-if="item.key == 'os'"
                :class="baseInfo[item.key]=='Linux' ? '' : 'system-icon-style'"
                style="display: flex;"
              >
                <SystemIcon :name="baseInfo[item.key]=='Linux' ? 'linux' : 'windows'" />
              </div>
              <p v-else-if="item.key==='filesize'">{{ Math.floor((baseInfo[item.key]/1024/1024/1024)*100)/100 + 'G' ||'-' }}</p>
              <p v-else>{{ baseInfo[item.key]||'-' }}</p>
            </div>
          </zr-col>
        </zr-row>
      </div>
      <!-- 威胁项、可疑项 -->
      <div
        v-for="nape in threatType"
        :key="nape.key"
        :class="'warning-msg'"
      >
        <h3
          v-if="JSON.stringify(dataAll[nape.key])!=='{}'"
          class="title-pie"
        ><span :style="'background:'+nape.color" />{{ nape.label }}</h3>
        <div
          v-for="item in baseInfo.os==='Windows'?threatOption:[threatOption[0]]"
          :key="item.title"
        >
          <div
            v-if="dataAll[nape.key]&&dataAll[nape.key][item.key]"
          >
            <div
              v-for="(row,len) in threatData[nape.key][item.key]"
              :key="len"
            >
              <!-- 头部内容 -->
              <div
                :class="'threat-title ' + nape.key"
              >
                <p
                  class="level-piece"
                  :style="'background:'+levelPiece(row.highest_level)"
                />
                <h2>【{{ item.title }}】</h2>
                <p
                  v-if="item.name"
                  class="threat-name"
                  :style="'color:'+levelPiece(row.highest_level)"
                >{{ row[item.name] }}</p>
                <div class="threat-tag">
                  <p
                    v-for="i in row[item.tag]"
                    :key="i"
                    :class="'tag-custom ' + i.threat_level"
                  >
                    {{ i.threat_tag }}
                  </p>
                </div>
              </div>
              <!-- 列表内容 -->
              <div
                v-for="(list,listIndex) in item.list"
                :key="listIndex"
                class="threat-list"
              >
                <div v-if="item.key==='ps_info' ? row[list.tabelKey] :[row]">
                  <h2 v-if="list.typeLabel">{{ list.typeLabel }}</h2>
                  <zr-table
                    :data="item.key==='ps_info' ? row[list.tabelKey] :[row]"
                    empty-text="暂无数据"
                    default-expand-all
                    :row-class-name="getRowClass"
                    row-key="item_id"
                  >
<!--                    <zr-table-column
                      prop=""
                      label=""
                      width="50"
                    >
                      <template #default="scope">
                        <zr-tooltip
                          class="box-item"
                          effect="dark"
                          :content="scope.row.judge_status?'已研判':'未研判'"
                          placement="top"
                        >
                          <Icon
                            name="zr-trusted-configuration-b"
                            size="20"
                            :color="scope.row.judge_status ? nape.color : '#909399'"
                          />
                        </zr-tooltip>
                      </template>
                    </zr-table-column>-->
                     
                    <zr-table-column
                      v-for="prop in baseInfo.os==='Linux' && (list.tabelKey=='dll_list' || list.tabelKey=='net_scan_list') ? list.listLinuxOption : list.listOption"
                      :key="prop.label"
                      :prop="prop.prop"
                      :label="prop.label"
                      :width="prop.width"
                      show-overflow-tooltip
                    >
                      <template #default="scope">
                        {{ scope.row[prop.prop] || '-' }}
                      </template>
                    </zr-table-column>
                    <zr-table-column
                      prop="threat_info"
                      label="威胁标签"
                      width="400"
                    >
                      <template #default="scope">
                        <zr-scrollbar
                          v-if="scope.row.threat_info"
                          :wrap-class="['scrollbar-wrapper', 'wrapper-custom']"
                          :wrap-style="[{ 'max-height': '50px', height: '50px'}]"
                        >
                          <p
                            v-for="o in scope.row['threat_info']"
                            :key="o"
                            :class="'tag-custom ' + o.threat_level"
                          >
                            {{ o.threat_tag }}
                          </p>
                        </zr-scrollbar>
                      </template>
                    </zr-table-column>
                    <!-- <zr-table-column
                    prop="threat_info"
                    label="研判分析状态"
                    width="190"
                  >
                    <template #default="scope">
                      <div
                        class="judge_type"
                        :style="'color:' + judgeTypeFun(scope.row.judge_status,scope.row.judge_type)"
                      >
                        <Icon
                          :name="scope.row.judge_status==='研判完成' ? 'zr-circle-seleted-fill' : scope.row.judge_status==='研判中' ? 'zr-wait-fill' : 'zr-circle-reduce-fill'"
                          :size="18"
                          :color="judgeTypeFun(scope.row.judge_status,scope.row.judge_type)"
                        />
                        <p>{{ scope.row.judge_status||'未研判' }}</p>
                        <p v-if="scope.row.judge_status==='研判完成'">({{ scope.row.judge_type }})</p>
                      </div>
                    </template>
                  </zr-table-column> -->
<!--                    <zr-table-column type="expand">
                      <template #default="props">
                        <div
                          v-if="props.row.judge_status&&props.row.note&&props.row.note!==''"
                          class="detail-msg"
                        >
                          <h3 style="margin: 12px 0;">研判备注</h3>
                          <v-md-editor
                            v-model="props.row.note"
                            height="200px"
                            mode="preview"
                          />
                        </div>
                      </template>
                    </zr-table-column>-->
                  </zr-table>
                </div>
              </div>
            <!-- <zr-collapse
            >
              <zr-collapse-item
                :name="len +1"
              >
                <template #title>
                  <div
                    class="threat-title"
                  >
                    <p
                      class="level-piece"
                      :style="'background:'+levelPiece(row.highest_level)"
                    />
                    <h2>【{{ item.title }}】</h2>
                    <p
                      v-if="item.name"
                      class="threat-name"
                      :style="'color:'+levelPiece(row.highest_level)"
                    >{{ row[item.name] }}</p>
                    <div class="threat-tag">
                      <p
                        v-for="i in row[item.tag]"
                        :key="i"
                        :class="'tag-custom ' + i.threat_level"
                      >
                        {{ i.threat_tag }}
                      </p>
                    </div>
                  </div>
                </template>
                <div
                  v-for="(list,listIndex) in baseInfo.os==='Windows'?item.list:[item.list[0],item.list[2]]"
                  :key="listIndex"
                  class="threat-list"
                >
                  <div v-if="row[list.tabelKey]">
                    <h2 v-if="list.typeLabel">{{ list.typeLabel }}</h2>
                    <zr-table
                      :data="row[list.tabelKey]"
                      empty-text="暂无数据"
                      default-expand-all
                      :row-class-name="getRowClass"
                      row-key="id"
                    >
                      <zr-table-column
                        prop=""
                        label=""
                        width="50"
                      >
                        <template #default="scope">
                          <zr-tooltip
                            class="box-item"
                            effect="dark"
                            :content="scope.row.judge_status?'已研判':'未研判'"
                            placement="top"
                          >
                            <Icon
                              name="zr-trusted-configuration-b"
                              size="20"
                              :color="scope.row.judge_status ? '#fd4747' : '#909399'"
                            />
                          </zr-tooltip>
                        </template>
                      </zr-table-column>
                     
                      <zr-table-column
                        v-for="prop in list.listOption"
                        :key="prop.label"
                        :prop="prop.prop"
                        :label="prop.label"
                        :width="prop.width"
                        show-overflow-tooltip
                      />
                      <zr-table-column
                        prop="threat_info"
                        label="威胁标签"
                      >
                        <template #default="scope">
                          <zr-scrollbar
                            v-if="scope.row.threat_info"
                            :wrap-class="['scrollbar-wrapper', 'wrapper-custom']"
                            :wrap-style="[{ 'max-height': '50px', height: '50px'}]"
                          >
                            <p
                              v-for="o in scope.row['threat_info']"
                              :key="o"
                              :class="'tag-custom ' + o.threat_level"
                            >
                              {{ o.threat_tag }}
                            </p>
                          </zr-scrollbar>
                        </template>
                      </zr-table-column>
                      <zr-table-column
                        prop="threat_info"
                        label="研判分析状态"
                        width="190"
                      >
                        <template #default="scope">
                          <div
                            class="judge_type"
                            :style="'color:' + judgeTypeFun(scope.row.judge_status,scope.row.judge_type)"
                          >
                            <Icon
                              :name="scope.row.judge_status==='研判完成' ? 'zr-circle-seleted-fill' : scope.row.judge_status==='研判中' ? 'zr-wait-fill' : 'zr-circle-reduce-fill'"
                              :size="18"
                              :color="judgeTypeFun(scope.row.judge_status,scope.row.judge_type)"
                            />
                            <p>{{ scope.row.judge_status||'未研判' }}</p>
                            <p v-if="scope.row.judge_status==='研判完成'">({{ scope.row.judge_type }})</p>
                          </div>
                        </template>
                      </zr-table-column>
                      <zr-table-column type="expand">
                        <template #default="props">
                          <div
                            v-if="props.row.judge_status"
                            class="detail-msg"
                          >
                            <h3 style="margin: 12px 0;">研判备注</h3>
                            <v-md-editor
                              v-model="props.row.note"
                              height="200px"
                              mode="preview"
                            />
                          </div>
                        </template>
                      </zr-table-column>
                    </zr-table>
                  </div>
                </div>
              </zr-collapse-item>
            </zr-collapse> -->
            </div>
            <zr-pagination
              v-if="dataAll[nape.key][item.key].length>0"
              :current-page="item.page"
              :page-size="item.pageSize"
              :page-sizes="[10, 20, 30]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="dataAll[nape.key][item.key].length"
              style="margin-bottom: 20px;"
              @size-change="(val)=>handleSizeChange(item,dataAll[nape.key][item.key],nape,val)"
              @current-change="(val)=>handleCurrentChange(item,dataAll[nape.key][item.key],nape,val)"
            />

          </div>
        </div>
      </div>
      <!-- 标记项 -->
      <div
        v-if="dataAll.label&&JSON.stringify(dataAll.label)!=='{}'"
        class="mark-msg"
      >
        <h3 class="title-pie"><span style="background:#909399" />标记项</h3>
        <div
          v-for="item in dataAll.label"
          :key="item.label_name"
          class="msg-detail"
        >
          <p
            class="tag-name"
          >
            <span> {{ '<' }} </span>
            <span>{{ item.label_name }}</span>
            <span> {{ '>' }} </span>
          </p>
          <div
            v-for="row in item.content"
            :key="row.item_name"
          >
            <h3>{{ row.item_name }}</h3>
            <zr-table
              :data="row.item"
              empty-text="暂无数据"
            >
              <zr-table-column
                v-for="(value,key,index) in row.item[0]"
                :key="key"
                show-overflow-tooltip
                :prop="key"
                :label="key"
                :width="index === Object.keys(row.item[0]).length - 1 ? 448 : null"
              >
                <template #default="scope">
                  {{ scope.row[key] || '-' }}
                </template>
              </zr-table-column>
            </zr-table>
          </div>

        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, onMounted, defineProps } from 'vue';
import { analysisReport } from '@/api/threat/mirrorAngle/index';
import { downloadFun } from '@/utils/downloadUtils';
import { ZrMessage, SystemIcon } from 'qianji-ui';
import { htmlPdf } from "@/utils/htmlToPDF.js";
const props = defineProps({
  analysisId: String,
  id: String
});
const threatType = [
  {
    label: '威胁项',
    key: 'threat_warning_info',
    color: '#fd4747'
  },
  {
    label: '可疑项',
    key: 'suspicious_content',
    color: '#ff9900'
  }
];
const threatOption = [
  {
    title: '进程',
    name: 'ps_name_pid',
    tag: 'threat_info',
    key: 'ps_info',
    page: 1,
    pageSize: 10,
    list: [
      {
        typeLabel: '进程信息',
        tabelKey: 'ps_basic_list',
        listOption: [
          {
            label: '进程名称',
            prop: 'ps_name',
            width: 200
          },
          {
            label: '进程ID',
            prop: 'pid',
            width: 100
          },
          {
            label: '父进程ID',
            prop: 'ppid',
            width: 100
          },
          {
            label: '文件路径',
            prop: 'file_path'
          },
          {
            label: 'UID',
            prop: 'uid'
          },
          {
            label: 'GID',
            prop: 'gid'
          },
          {
            label: '启动时间',
            prop: 'start_time'
          }
          // {
          //   label: '威胁标签',
          //   prop: 'threat_info'
          // }
        ]
      },
      {
        typeLabel: '动态链接库',
        tabelKey: 'dll_list',
        listLinuxOption: [
          {
            label: '进程',
            prop: 'process',
            width: 200
          },
          {
            label: '文件路径',
            prop: 'file_path'
          }
        ],
        listOption: [
          {
            label: '名称',
            prop: 'dll_name',
            width: 200
          },
          {
            label: '大小(KB)',
            prop: 'size',
            width: 100
          },
          {
            label: '进程',
            prop: 'process',
            width: 200
          },
          {
            label: '加载时间',
            prop: 'load_time',
            width: 200
          },
          {
            label: '文件路径',
            prop: 'file_path'
          },
          {
            label: '载入计数',
            prop: 'load_count'
          }
          // {
          //   label: '威胁标签',
          //   prop: 'threat_info'
          // }
        ]
      },
      {
        typeLabel: '网络连接',
        tabelKey: 'net_scan_list',
        listLinuxOption: [
          {
            label: '协议',
            prop: 'protocol',
            width: 100
          },
          {
            label: '进程',
            prop: 'process'
          },
          {
            label: '本地地址',
            prop: 'local_addr',
            width: 200
          },
          {
            label: '本地端口',
            prop: 'local_port',
            width: 100
          },
          {
            label: '远程地址',
            prop: 'foreign_addr',
            width: 200
          },
          {
            label: '远程端口',
            prop: 'foreign_port'
          },
          {
            label: '状态',
            prop: 'state'
          }
        ],
        listOption: [
          {
            label: '协议',
            prop: 'protocol',
            width: 100
          },
          {
            label: '进程',
            prop: 'process'
            // width: 200
          },
          {
            label: '本地地址',
            prop: 'local_addr'
            // width: 200
          },
          {
            label: '本地端口',
            prop: 'local_port',
            width: 100
          },
          {
            label: '远程地址',
            prop: 'foreign_addr',
            width: 200
          },
          {
            label: '远程端口',
            prop: 'foreign_port'
            // width: 100
          },
          {
            label: '状态',
            prop: 'state'
          }
          // {
          //   label: '创建时间',
          //   prop: 'created'
          // }
          // {
          //   label: '威胁标签',
          //   prop: 'threat_info'
          // }
        ]
      }
    ]
  },
  {
    title: '注册表文件',
    name: 'path',
    tag: 'threat_info',
    key: 'registry_file',
    page: 1,
    pageSize: 10,
    list: [
      {
        tabelKey: 'registry_file_list',
        listOption: [
          {
            label: '偏移量',
            prop: 'offset'
          },
          {
            label: '完整路径',
            prop: 'path'
          }
          // {
          //   label: '威胁标签',
          //   prop: 'threat_info'
          // }
        ]
      }
    ]
  },
  {
    title: '驱动模块',
    name: 'name',
    tag: 'threat_info',
    key: 'driver_module',
    page: 1,
    pageSize: 10,
    list: [
      {
        tabelKey: 'driver_module_list',
        listOption: [
          {
            label: '名称',
            prop: 'name'
          },
          {
            label: '路径',
            prop: 'path'
          },
          {
            label: '大小(B)',
            prop: 'size'
          }
          // {
          //   label: '威胁标签',
          //   prop: 'threat_info'
          // }
        ]
      }
    ]
  }
];
const basicMsgOption = [
  {
    label: '文件名称',
    key: 'filename'
  },
  {
    label: '校验和（ETag）',
    key: 'etag'
  },
  {
    label: '操作系统',
    key: 'os'
  },
  {
    label: '文件大小',
    key: 'filesize'
  },
  {
    label: '关联主机',
    key: 'host'
  },
  {
    label: '所属任务',
    key: 'task'
  },
  {
    label: '所属用户',
    key: 'username'
  },
  // {
  //   label: '研判人员',
  //   key: 'judgment_operator'
  // },
  {
    label: '上传完成时间',
    key: 'upload_finish_time'
  },
  {
    label: '检测完成时间',
    key: 'detection_finish_time'
  },
  /* {
    label: '研判完成时间',
    key: 'judgment_finish_time'
  },*/
  {
    label: '威胁检测结果',
    key: 'detection_record_result',
    data: [
      {
        name: '致命',
        value: 'critical',
        color: '#AE0000'
      },
      {
        name: '高威',
        value: 'high',
        color: '#fd4747'
      },
      {
        name: '中威',
        value: 'medium',
        color: '#ff9900'
      },
      {
        name: '低威',
        value: 'low',
        color: '#909399'
      }
    ]
  }
  /* {
    label: '研判结论',
    key: 'judge_result',
    data: [
      {
        name: '确认威胁',
        value: 'verification',
        color: '#fd4747'
      },
      {
        name: '可疑威胁',
        value: 'suspicion',
        color: '#ff9900'
      },
      {
        name: '标记',
        value: 'mark',
        color: '#909399'
      }
    ]
  }*/
  // {
  //   label: '研判人员',
  //   key: 'judgment_operator'
  // },
  // {
  //   label: '研判结论',
  //   key: 'judge_result',
  //   data: [
  //     {
  //       name: '威胁',
  //       value: 'verification',
  //       color: '#fd4747'
  //     },
  //     {
  //       name: '可疑',
  //       value: 'suspicion',
  //       color: '#ff9900'
  //     },
  //     {
  //       name: '标记',
  //       value: 'mark',
  //       color: '#909399'
  //     }
  //   ]
  // }
];
const exportOption = [
  {
    name: '导出HTML',
    command: 'html'
  },
  {
    name: '导出PDF',
    command: 'pdf'
  }
];
const baseInfo = ref({});
const dataAll = ref({});
const loading = ref(false);
const threatData = ref({});

onMounted(() => {
  loading.value = true;
  analysisReport(props.analysisId, props.id).then(res => {
    dataAll.value = res.data.data;
    threatType.forEach(nape => {
      threatOption.forEach(item => {
        if (dataAll.value[nape.key][item.key]) {
          threatListFun(item, dataAll.value[nape.key][item.key], nape);
        }
      });
    });

    baseInfo.value = res.data.data.base_info;
  }).finally(() => {
    loading.value = false;
  });
});
           
function threatListFun(item, list, nape) { // 静态分页数据处理
  var arr = list.slice(item.page == 1 ? 0 : ((item.page - 1) * item.pageSize), (item.pageSize * item.page));
  threatData.value[nape.key] = threatType;
  threatData.value[nape.key][item.key] = arr;
}
function handleSizeChange(item, list, nape, val) { // 每页条数
  item.page = 1;
  item.pageSize = val;
  threatListFun(item, list, nape);
}
function handleCurrentChange(item, list, nape, val) { // 第几页
  item.page = val;
  threatListFun(item, list, nape);
}

function handleCommand(val) {
  ZrMessage.success('正在导出，请稍后...');
  if (val === 'html') {
    downloadFun('/mfp-service/api/v1/threat_detection/analysis_report/' + props.analysisId + '/' + props.id + '/analysis_report/html_export');
  } else {
    var fileName = dataAll.value.image_name + '分析报告';
    // const fileList = document.getElementsByClassName('pdfRef'); // 很重要
    htmlPdf(fileName, document.querySelector('#pdfRef'));
  }
}
function getRowClass({ row }) {
  if ((!row.judge_status) || (!row.note) || row.note === '') {
    return "row-expand-cover";
  } else {
    return "";
  }
}
function judgeTypeFun(status, type) {
  var color = "";
  switch (status) {
    case '研判完成':
      switch (type) {
        case '确认威胁':
          color = "#fd4747";
          break;
        case '可疑威胁':
          color = "#ff9900";
          break;
        case '误报':
          color = "#07b630";
          break;
        default:
          color = "#909399";
      }
      break;
    case '研判中':
      color = "#1890ff";
      break;
    default:
      color = "#909399";
  }
  return color;
}
function levelPiece(level) {
  var color = '';
  switch (level) {
    case 'critical':
      color = '#AE0000';
      break;
    case 'high':
      color = '#fd4747';
      break;
    case 'medium':
      color = '#ff9900';
      break;
    case 'low':
      color = '#909399';
      break;
    case 'information':
      color = '#1890ff';
      break;
    case 'safe':
      color = '#07b630';
      break;
  }
  return color;
}
</script>
<style lang="scss" scoped>
.export{
  text-align:right
}
.analysis{
    padding-left: 20px;
    position: relative;
    // .export{
    //   position: absolute;
    //   top: 0;
    //   right: 20px;
    // }
    .basic-msg{
        .msg-detail{
            display: flex;
            p{
                line-height: 32px;
                font-size: 15px;
                color: #333;
            }
            p:nth-child(1){
                width: 150px;
            }
        }
    }
    .mark-msg{
        h3{
          font-size: 16px;
          margin-top: 20px;
        }
        .tag-name{
          margin-top: 20px;
          font-size: 20px;
          span:nth-child(2){
            color: #1890ff;
            font-weight: 700;
          }
        }
    }
    .warning-msg{
      .threat_warning_info{
          background: rgba(253, 71, 71,.1);
      }
      .suspicious_content{
          background: rgba(255, 153, 0,.1);
      }
    }
}
.detection_record_result{
  font-size: 15px;
  line-height: 32px;
  display: flex;
  align-items: center;
  >div{
    margin-right: 20px;
    >div{
      display: flex;
      align-items: center;
    }
  }
}
.title-pie{
  font-size: 18px;
  margin: 18px 0 12px;
  >span{
    height: 20px;
    width: 6px;
  }
}
.threat-title{
    display: flex;
    align-items: center;
    line-height: 50px;
    border-top: 1px solid #ebeef5;
    h2{
        font-size: 15px;
        max-width: 110px;
    }
    >.threat-name{
        font-weight: 600;
        margin-right: 20px;
        // min-width: 150px;
        margin-left: 10px;
    }
    .threat-tag{
      flex: 1;
    }
}
.threat-list{
  margin-bottom:20px;
    h2{
        font-size: 14px;
        margin-top: 20px;
    }
    .el-table{
        margin-top: 8px;
    }
}
:deep .el-table .row-expand-cover .cell .el-table__expand-icon {
  display: none;
}
.judge_type{
  display: flex;
  align-items: center;
  p{
    margin-left: 4px;
  }
}
</style>
