<template>
  <!-- 全局检索 -->
  <div>
    <zr-tabs
      v-model="activeName"
      type="card"
      class="zr-tabs"
      @tab-change="tabChange"
    >
      <zr-tab-pane
        v-for="item in tabOption"
        :key="item.name"
        :label="item.label"
        :name="item.name"
      />
    </zr-tabs>
    <ReportRetrieval
      v-if="activeName==='1'"
      :id="id"
      :detail-data="detailData"
    />
    <MemoryRetrieval
      v-else
      :id="id"
      :detail-data="detailData"
    />
  </div>
</template>
<script setup>
import { ref, defineProps } from 'vue';
import ReportRetrieval from './ReportRetrieval.vue';
import MemoryRetrieval from './MemoryRetrieval.vue';
const props = defineProps({
  id: String,
  detailData: Object
});

const activeName = ref('1');
const tabOption = [
  {
    label: '报告检索',
    name: '1'
  },
  {
    label: '内存镜像检索',
    name: '2'
  }
];

function tabChange() {

}
</script>
