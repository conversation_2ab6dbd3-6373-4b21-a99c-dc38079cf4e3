<template>
  <!-- 密钥 -->
  <div>
    <zr-tabs
      v-model="activeName"
      type="card"
      class="zr-tabs"
      @tab-change="tabChange"
    >
      <zr-tab-pane
        v-for="item in tabOption"
        :key="item.name"
        :label="item.label"
        :name="item.name"
      />
      
      <!-- <div class="single-inspection">
        <zr-input
          v-model="searchForm.name"
          placeholder="名称"
          @keyup.enter="handleChange"
        >
          <template #append>
            <zr-button @click="handleChange">
              <zr-icon
                name="Search"
              />
            </zr-button>
          </template>
        </zr-input>
      </div> -->
  
      <!--  HashDump  -->
      <zr-table
        v-if="activeName=='1'"
        v-loading="loading"
        :data="tableDataHash"
        empty-text="暂无数据"
        class="scroll-bar"
      >
        <zr-table-column
          prop="user"
          label="用户"
          width="200"
          show-overflow-tooltip
        />
        <zr-table-column
          prop="rid"
          label="RID"
          show-overflow-tooltip
        />
        <zr-table-column
          prop="lm_hash"
          label="LM-Hash"
          show-overflow-tooltip
        >
          <template #default="scope">
            {{ scope.row.lm_hash || '-' }}
          </template>
        </zr-table-column>
        <zr-table-column
          prop="nt_hash"
          label="NT-Hash"
          show-overflow-tooltip
        >
          <template #default="scope">
            {{ scope.row.nt_hash || '-' }}
          </template>
        </zr-table-column>
        <zr-table-column
          prop="marks"
          label="标记"
          width="200"
        >
          <template #default="scope">
            <div class="tag-container">
              <span v-if="scope.row.marks && scope.row.marks.length>0">
                <zr-tag
                  v-for="item in scope.row.marks"
                  :key="item"
                  class="tag-item"
                >{{ item }}
                </zr-tag>
              </span>
              <span v-else> - </span>
            </div>
          </template>
        </zr-table-column>
        <zr-table-column
          prop=""
          label="操作"
          width="120px"
        >
          <template #default="props">
            <Mark
              :row-data="props.row"
              :analysis-id="id"
              :input-mark="inputMark"
              @openMarkDialog="openMarkDialog"
              @listFun="handleChange"
            />
          </template>
        </zr-table-column>
      </zr-table>

      <!--  LsaDump  -->
      <zr-table
        v-else
        v-loading="loading"
        :data="tableDataLsa"
        empty-text="暂无数据"
        class="scroll-bar"
      >
        <zr-table-column
          prop="key"
          label="名称"
          width="200"
          show-overflow-tooltip
        />
        <zr-table-column
          prop="secret"
          label="密钥"
          show-overflow-tooltip
        />
        <zr-table-column
          prop="hex"
          label="Hex"
          show-overflow-tooltip
        />
        <zr-table-column
          prop="marks"
          label="标记"
          width="200"
        >
          <template #default="scope">
            <div class="tag-container">
              <span v-if="scope.row.marks && scope.row.marks.length>0">
                <zr-tag
                  v-for="item in scope.row.marks"
                  :key="item"
                  class="tag-item"
                >{{ item }}
                </zr-tag>
              </span>
              <span v-else> - </span>
            </div>
          </template>
        </zr-table-column>
        <zr-table-column
          prop=""
          label="操作"
          width="120px"
        >
          <template #default="props">
            <Mark
              :row-data="props.row"
              :analysis-id="id"
              :input-mark="inputMark"
              @openMarkDialog="openMarkDialog"
              @listFun="handleChange"
            />
          </template>
        </zr-table-column>
      </zr-table>
    </zr-tabs>
    <DetailPagination
      v-if="total>0"
      :search-form="searchForm"
      :total="total"
      @handleSizeChange="handleSizeChange"
      @handleCurrentChange="handleCurrentChange"
    />
    <MarkAddDialog
      :is-open-dialog="isOpenDialog"
      :analysis-id="id"
      @submitAddMark="submitAddMark"
      @openMarkDialog="openMarkDialog"
    />
  </div>
</template>
<script setup>
import { hashDumpList, lsaDumpList } from "@/api/threat/mirrorAngle/windows";
import { ref, reactive, defineProps } from 'vue';
import DetailPagination from '@/components/common/DetailPagination.vue';
import Mark from '../Mark.vue';
import MarkAddDialog from '../MarkAddDialog.vue';

const props = defineProps({
  id: String
});
const loading = ref(false);
const activeName = ref('1');
const tabOption = [
  {
    label: 'HashDump',
    name: '1'
  },
  {
    label: 'LsaDump',
    name: '2'
  }
];
const searchForm = reactive({
  // name: '',
  page: 1,
  'page_size': 100
});
const tableDataHash = ref([]);
const tableDataLsa = ref([]);
const total = ref(0);

const inputMark = ref('');
const isOpenDialog = ref(false);
function openMarkDialog(value) {
  isOpenDialog.value = value;
}
function submitAddMark(input) {
  inputMark.value = input;
}

hashDumpListFun();
function hashDumpListFun() {
  loading.value = true;
  hashDumpList(props.id, searchForm).then(res => {
    tableDataHash.value = res.data.data.lists;
    total.value = res.data.data.count;
  }).finally(() => {
    loading.value = false;
  });
}

function lsaDumpListFun() {
  lsaDumpList(props.id, searchForm).then(res => {
    tableDataLsa.value = res.data.data.lists;
    total.value = res.data.data.count;
  }).finally(() => {
    loading.value = false;
  });
}

function handleSizeChange(val) {
  searchForm.page = 1;
  searchForm['page_size'] = val;
  handleChange();
}
function handleCurrentChange(val) {
  searchForm.page = val;
  handleChange();
}

function tabChange() {
  // searchForm.name = '';
  searchForm.page = 1;
  searchForm['page_size'] = 100;
  handleChange();
}

function handleChange() {
  if (activeName.value === '1') {
    hashDumpListFun();
  } else {
    lsaDumpListFun();
  }
}
</script>

