<template>
  <!-- 内核函数 -->
  <div>
    <zr-row
      :gutter="24"
      justify="space-between"
      style="white-space: nowrap"
    >
      <zr-col
        :span="24"
        style="text-align:right"
      >
        <zr-form
          :inline="true"
          :model="searchForm"
        >
          <zr-form-item>
            <zr-select
              v-model="searchForm.type"
              size="default"
              style="width:150px"
              @change="kernelFuncListFun"
            >
              <zr-option
                v-for="item in typeOption"
                :key="item.label"
                :label="item.label"
                :value="item.value"
              />
            </zr-select>
          </zr-form-item>
          <zr-form-item>
            <zr-select
              v-model="searchForm.filter_condition"
              size="default"
              style="width:150px"
              @change="filterConditionChange"
            >
              <zr-option
                v-for="(value,key) in filterConditionOption"
                :key="key"
                :label="value"
                :value="key"
              />
            </zr-select>
          </zr-form-item>
          <zr-form-item>
            <zr-input
              v-model="searchForm.name"
              :placeholder="searchForm.filter_condition=='function' ? '函数' : searchForm.filter_condition=='module' ? '模块' : '进程' "
              @keyup.enter="kernelFuncListFun"
            >
              <template #append>
                <zr-button @click="kernelFuncListFun">
                  <zr-icon
                    name="Search"
                  />
                </zr-button>
              </template>
            </zr-input>
          </zr-form-item>
        </zr-form>
      </zr-col>
    </zr-row>
      
    <div
      class="single-inspection"
    />
    <zr-table
      v-loading="loading"
      :data="tableData"
      :empty-text="detailData.volatility_version == 'V3' ? '分析引擎不支持该模块 或 内存镜像中不包含相关数据' : '暂无数据'"
      class="scroll-bar"
    >
      <zr-table-column
        prop="type"
        label="类型"
      >
        <template #default="scope">
          {{ scope.row.type == 'Export' ? '导出' : '导入' }}
        </template>
      </zr-table-column>
      <zr-table-column
        prop="name"
        label="名称"
      />
      <zr-table-column
        prop="module"
        label="进程/模块"
      />
      <zr-table-column
        prop="address"
        label="地址"
      />
      <zr-table-column
        prop="marks"
        label="标记"
        width="200"
      >
        <template #default="scope">
          <div class="tag-container">
            <span v-if="scope.row.marks && scope.row.marks.length>0">
              <zr-tag
                v-for="item in scope.row.marks"
                :key="item"
                class="tag-item"
              >{{ item }}
              </zr-tag>
            </span>
            <span v-else> - </span>
          </div>
       
        </template>
      </zr-table-column>
      <zr-table-column
        prop=""
        label="操作"
        width="120"
      >
        <template #default="props">
          <Mark
            :row-data="props.row"
            :analysis-id="id"
            :input-mark="inputMark"
            @openMarkDialog="openMarkDialog"
            @listFun="kernelFuncListFun"
          />
        </template>
      </zr-table-column>
    </zr-table>
    <DetailPagination
      v-if="total>0"
      :search-form="searchForm"
      :total="total"
      @handleSizeChange="handleSizeChange"
      @handleCurrentChange="handleCurrentChange"
    />
    <MarkAddDialog
      :is-open-dialog="isOpenDialog"
      :analysis-id="id"
      @submitAddMark="submitAddMark"
      @openMarkDialog="openMarkDialog"
    />
  </div>
</template>
<script setup>
import { ref, reactive, defineProps, onMounted } from 'vue';
import { kernelFuncList, getFilterCondition } from '@/api/threat/mirrorAngle/windows';
import DetailPagination from '@/components/common/DetailPagination.vue';
import Mark from '../Mark.vue';
import MarkAddDialog from '../MarkAddDialog.vue';

const props = defineProps({
  id: String,
  detailData: Object
});
const typeOption = [
  {
    label: '全部',
    value: ''
  },
  {
    label: '导出',
    value: 'Export'
  },
  {
    label: '导入',
    value: 'Import'
  }
];
const filterConditionOption = ref({});
const tableData = ref([]);
const searchForm = reactive({
  type: '',
  'filter_condition': 'function',
  name: '',
  page: 1,
  'page_size': 100
});
const total = ref(0);
const loading = ref(false);

onMounted(() => {
  kernelFuncListFun();
  getFilterConditionFun();
});

const inputMark = ref('');
const isOpenDialog = ref(false);
function openMarkDialog(value) {
  isOpenDialog.value = value;
}
function submitAddMark(input) {
  inputMark.value = input;
}

function filterConditionChange() {
  searchForm.name = '';
}

function getFilterConditionFun() {
  getFilterCondition(props.id).then(res => {
    filterConditionOption.value = res.data.data;
  });
}
function kernelFuncListFun() {
  loading.value = true;
  kernelFuncList(searchForm, props.id).then(res => {
    tableData.value = res.data.data.lists;
    total.value = res.data.data.count;
  }).finally(() => {
    loading.value = false;
  });
}
function handleSizeChange(val) {
  searchForm.page = 1;
  searchForm['page_size'] = val;
  kernelFuncListFun();
}
function handleCurrentChange(val) {
  searchForm.page = val;
  kernelFuncListFun();
}
</script>
  
