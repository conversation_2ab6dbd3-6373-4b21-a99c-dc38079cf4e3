<template>
  <!-- 未加载模块 -->
  <div>
    <zr-tabs
      v-model="activeName"
      type="card"
      class="zr-tabs"
    >
      <zr-tab-pane
        v-for="item in tabOption"
        :key="item.name"
        :label="item.label"
        :name="item.name"
      />
    </zr-tabs>
    <div
      class="single-inspection"
    >
      <zr-input
        v-model="searchForm.name"
        placeholder="模块名称"
        @keyup.enter="notLoadedModuleListFun"
      >
        <template #append>
          <zr-button @click="notLoadedModuleListFun">
            <zr-icon
              name="Search"
            />
          </zr-button>
        </template>
      </zr-input>
    </div>
    <zr-table
      v-loading="loading"
      :data="tableData"
      class="scroll-bar"
      :empty-text="detailData.volatility_version == 'V3' ? '分析引擎不支持该模块 或 内存镜像中不包含相关数据' : '暂无数据'"
    >
      <zr-table-column
        prop="name"
        label="名称"
      />
      <zr-table-column
        prop="start_address"
        label="起始地址"
      />
      <zr-table-column
        prop="end_address"
        label="终止地址"
      />
      <zr-table-column
        prop="time"
        label="时间"
      />
      <zr-table-column
        prop="marks"
        label="标记"
        width="200"
      >
        <template #default="scope">
          <div class="tag-container">
            <span v-if="scope.row.marks && scope.row.marks.length>0">
              <zr-tag
                v-for="item in scope.row.marks"
                :key="item"
                class="tag-item"
              >{{ item }}
              </zr-tag>
            </span>
            <span v-else> - </span>
          </div>
        </template>
      </zr-table-column>
      <zr-table-column
        prop=""
        label="操作"
        width="120"
      >
        <template #default="props">
          <Mark
            :row-data="props.row"
            :analysis-id="id"
            :input-mark="inputMark"
            @openMarkDialog="openMarkDialog"
            @listFun="notLoadedModuleListFun"
          />
        </template>
      </zr-table-column>
    </zr-table>
    <DetailPagination
      v-if="total>0"
      :search-form="searchForm"
      :total="total"
      @handleSizeChange="handleSizeChange"
      @handleCurrentChange="handleCurrentChange"
    />
    <MarkAddDialog
      :is-open-dialog="isOpenDialog"
      :analysis-id="id"
      @submitAddMark="submitAddMark"
      @openMarkDialog="openMarkDialog"
    />
  </div>
</template>
<script setup>
import { ref, reactive, defineProps } from 'vue';
import { notLoadedModuleList } from '@/api/threat/mirrorAngle/windows';
import DetailPagination from '@/components/common/DetailPagination.vue';
import Mark from '../Mark.vue';
import MarkAddDialog from '../MarkAddDialog.vue';

const props = defineProps({
  id: String,
  detailData: Object
});
const activeName = ref('1');
const tabOption = [
  {
    label: '已卸载驱动程序',
    name: '1'
  }
];
const tableData = ref([]);
const searchForm = reactive({
  page: 1,
  'page_size': 100
});
const total = ref(0);
const loading = ref(false);

onMounted(() => {
  notLoadedModuleListFun();
});
const inputMark = ref('');
const isOpenDialog = ref(false);
function openMarkDialog(value) {
  isOpenDialog.value = value;
}
function submitAddMark(input) {
  inputMark.value = input;
}

function notLoadedModuleListFun() {
  loading.value = true;
  notLoadedModuleList(searchForm, props.id).then(res => {
    tableData.value = res.data.data.lists;
    total.value = res.data.data.count;
  }).finally(() => {
    loading.value = false;
  });
}
function handleSizeChange(val) {
  searchForm.page = 1;
  searchForm['page_size'] = val;
  notLoadedModuleListFun();
}
function handleCurrentChange(val) {
  searchForm.page = val;
  notLoadedModuleListFun();
}
</script>
