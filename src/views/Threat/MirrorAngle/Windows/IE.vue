<template>
  <!-- IE历史 -->
  <div>
    <div
      class="single-inspection"
    >
      <zr-input
        v-model="searchForm.name"
        placeholder="访问路径"
        @keyup.enter="ieListFun"
      >
        <template #append>
          <zr-button @click="ieListFun">
            <zr-icon
              name="Search"
            />
          </zr-button>
        </template>
      </zr-input>
    </div>
    <zr-table
      v-loading="loading"
      :data="tableData"
      :empty-text="detailData.volatility_version == 'V3' ? '分析引擎不支持该模块 或 内存镜像中不包含相关数据' : '暂无数据'"
      class="scroll-bar"
    >
      <zr-table-column
        prop="process"
        label="进程"
        width="200"
        show-overflow-tooltip
      />
      <zr-table-column
        prop="base_address"
        label="基址"
        width="200"
        show-overflow-tooltip
      />
      <zr-table-column
        prop="len"
        label="长度"
        width="200"
        show-overflow-tooltip
      />
      <zr-table-column
        prop="path"
        label="访问路径"
        show-overflow-tooltip
      />
      <zr-table-column
        prop="data"
        label="数据"
        show-overflow-tooltip
      />
      <zr-table-column
        prop="marks"
        label="标记"
        width="200"
      >
        <template #default="scope">
          <div class="tag-container">
            <span v-if="scope.row.marks && scope.row.marks.length>0">
              <zr-tag
                v-for="item in scope.row.marks"
                :key="item"
                class="tag-item"
              >{{ item }}
              </zr-tag>
            </span>
            <span v-else> - </span>
          </div>
        </template>
      </zr-table-column>
      <zr-table-column
        prop=""
        label="操作"
        width="120"
      >
        <template #default="props">
          <Mark
            :row-data="props.row"
            :analysis-id="id"
            :input-mark="inputMark"
            @openMarkDialog="openMarkDialog"
            @listFun="ieListFun"
          />
        </template>
      </zr-table-column>
    </zr-table>
    <DetailPagination
      v-if="total>0"
      :search-form="searchForm"
      :total="total"
      @handleSizeChange="handleSizeChange"
      @handleCurrentChange="handleCurrentChange"
    />
    <MarkAddDialog
      :is-open-dialog="isOpenDialog"
      :analysis-id="id"
      @submitAddMark="submitAddMark"
      @openMarkDialog="openMarkDialog"
    />

  </div>
</template>
<script setup>
import { ref, reactive, defineProps } from 'vue';
import { ieList } from '@/api/threat/mirrorAngle/windows';
import DetailPagination from '@/components/common/DetailPagination.vue';
import Mark from '../Mark.vue';
import MarkAddDialog from '../MarkAddDialog.vue';

const props = defineProps({
  id: String,
  detailData: Object
});
const tableData = ref([]);
const searchForm = reactive({
  page: 1,
  'page_size': 100
});
const total = ref(0);
const loading = ref(false);

onMounted(() => {
  ieListFun();
});
const inputMark = ref('');
const isOpenDialog = ref(false);
function openMarkDialog(value) {
  isOpenDialog.value = value;
}
function submitAddMark(input) {
  inputMark.value = input;
}


function ieListFun() {
  loading.value = true;
  ieList(searchForm, props.id).then(res => {
    tableData.value = res.data.data.lists;
    total.value = res.data.data.count;
  }).finally(() => {
    loading.value = false;
  });
}
function handleSizeChange(val) {
  searchForm.page = 1;
  searchForm['page_size'] = val;
  ieListFun();
}
function handleCurrentChange(val) {
  searchForm.page = val;
  ieListFun();
}
</script>
