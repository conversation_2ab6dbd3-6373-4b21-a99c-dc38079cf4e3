<template>
  <!-- 服务SID -->
  <div>
    <div
      class="single-inspection"
    >
      <zr-input
        v-model="searchForm.name"
        placeholder="SID/名称"
        @keyup.enter="sidwListFun"
      >
        <template #append>
          <zr-button @click="sidwListFun">
            <zr-icon
              name="Search"
            />
          </zr-button>
        </template>
      </zr-input>
    </div>
    <zr-table
      v-loading="loading"
      :data="tableData"
      empty-text="暂无数据"
      class="scroll-bar"
    >
      <zr-table-column
        prop="sid"
        label="SID"
      />
      <zr-table-column
        prop="name"
        label="名称"
      />
      <zr-table-column
        prop="marks"
        label="标记"
        width="200"
      >
        <template #default="scope">
          <div class="tag-container">
            <span v-if="scope.row.marks && scope.row.marks.length>0">
              <zr-tag
                v-for="item in scope.row.marks"
                :key="item"
                class="tag-item"
              >{{ item }}
              </zr-tag>
            </span>
            <span v-else> - </span>
          </div>
        </template>
      </zr-table-column>
      <zr-table-column
        prop=""
        label="操作"
        width="120"
      >
        <template #default="props">
          <Mark
            :row-data="props.row"
            :analysis-id="id"
            :input-mark="inputMark"
            @openMarkDialog="openMarkDialog"
            @listFun="sidwListFun"
          />
        </template>
      </zr-table-column>
    </zr-table>
    <DetailPagination
      v-if="total>0"
      :search-form="searchForm"
      :total="total"
      @handleSizeChange="handleSizeChange"
      @handleCurrentChange="handleCurrentChange"
    />
    <MarkAddDialog
      :is-open-dialog="isOpenDialog"
      :analysis-id="id"
      @submitAddMark="submitAddMark"
      @openMarkDialog="openMarkDialog"
    />
  </div>
</template>
<script setup>
import { ref, reactive, defineProps } from 'vue';
import { sidwList } from '@/api/threat/mirrorAngle/windows';
import DetailPagination from '@/components/common/DetailPagination.vue';
import Mark from '../Mark.vue';
import MarkAddDialog from '../MarkAddDialog.vue';

const props = defineProps({
  id: String
});
const tableData = ref([]);
const searchForm = reactive({
  page: 1,
  'page_size': 100
});
const total = ref(0);
const loading = ref(false);

onMounted(() => {
  sidwListFun();
});

const inputMark = ref('');
const isOpenDialog = ref(false);
function openMarkDialog(value) {
  isOpenDialog.value = value;
}
function submitAddMark(input) {
  inputMark.value = input;
}

function sidwListFun() {
  loading.value = true;
  sidwList(searchForm, props.id).then(res => {
    tableData.value = res.data.data.lists;
    total.value = res.data.data.count;
  }).finally(() => {
    loading.value = false;
  });
}
function handleSizeChange(val) {
  searchForm.page = 1;
  searchForm['page_size'] = val;
  sidwListFun();
}
function handleCurrentChange(val) {
  searchForm.page = val;
  sidwListFun();
}
</script>
