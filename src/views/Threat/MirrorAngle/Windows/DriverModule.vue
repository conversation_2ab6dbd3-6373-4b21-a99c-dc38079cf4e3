<template>
  <!-- 驱动模块 -->
  <div>
    <div
      class="single-inspection"
    >
      <zr-input
        v-model="searchForm.name"
        placeholder="驱动名称"
        @keyup.enter="driverModuleListFun"
      >
        <template #append>
          <zr-button @click="driverModuleListFun">
            <zr-icon
              name="Search"
            />
          </zr-button>
        </template>
      </zr-input>
    </div>
    <zr-table
      ref="machineTable"
      v-loading="loading"
      :data="tableData"
      empty-text="暂无数据"
      :row-class-name="getRowClass"
      class="scroll-bar"
      row-key="id"
    >
      <zr-table-column
        type="expand"
        width="30"
      >
        <template #default="props">
          <FileMsg
            :file-msg="props.row"
          />
        </template>
      </zr-table-column>
      <zr-table-column
        prop="offset"
        label="偏移量"
        width="200"
        show-overflow-tooltip
      />
      <zr-table-column
        prop="base_address"
        label="基址"
        width="200"
        show-overflow-tooltip
      />
      <zr-table-column
        prop="size"
        label="大小 (B)"
        width="200"
        show-overflow-tooltip
      />
      <zr-table-column
        prop="name"
        label="名称"
        show-overflow-tooltip
      />
      <zr-table-column
        prop="path"
        label="路径"
        show-overflow-tooltip
      />
      <zr-table-column
        prop="marks"
        label="标记"
        width="200"
      >
        <template #default="scope">
          <div class="tag-container">
            <span v-if="scope.row.marks && scope.row.marks.length>0">
              <zr-tag
                v-for="item in scope.row.marks"
                :key="item"
                class="tag-item"
              >{{ item }}
              </zr-tag>
            </span>
            <span v-else> - </span>
          </div>
        </template>
      </zr-table-column>
      <zr-table-column
        label="操作"
        width="120"
      >
        <template #default="scope">
          <div class="buttonStyle">
            <zr-button
              style="margin-right: 10px;"
              link
              type="primary"
              :disabled="!scope.row.download_flag"
              @click="downloadClick(scope.row)"
            >下载</zr-button>
            <Mark
              :row-data="scope.row"
              :analysis-id="id"
              :input-mark="inputMark"
              @openMarkDialog="openMarkDialog"
              @listFun="driverModuleListFun"
            />
          </div>
        </template>
      </zr-table-column>
    </zr-table>
    <DetailPagination
      v-if="total>0"
      :search-form="searchForm"
      :total="total"
      @handleSizeChange="handleSizeChange"
      @handleCurrentChange="handleCurrentChange"
    />
    <MarkAddDialog
      :is-open-dialog="isOpenDialog"
      :analysis-id="id"
      @submitAddMark="submitAddMark"
      @openMarkDialog="openMarkDialog"
    />
  </div>
</template>
<script setup>
import { ref, reactive, defineProps } from 'vue';
import { driverModuleList } from '@/api/threat/mirrorAngle/windows';
import { fileDownload, fileDetail } from '@/api/threat/mirrorAngle/index';
import FileMsg from '../FileMsg.vue';
import DetailPagination from '@/components/common/DetailPagination.vue';
import Mark from '../Mark.vue';
import MarkAddDialog from '../MarkAddDialog.vue';

const props = defineProps({
  id: String
});
const tableData = ref([]);
const searchForm = reactive({
  page: 1,
  'page_size': 100
});
const total = ref(0);
const machineTable = ref();
const loading = ref(false);


onMounted(() => {
  driverModuleListFun();
});
// function expandChange(row, expandedRows) {
//   if (row.Lazy === undefined) {
//     fileDetail(row.id).then(res => {
//       row.Lazy = res.data.data;
//     });
//     machineTable.value.setCurrentRow(row);
//   }
// }
const inputMark = ref('');
const isOpenDialog = ref(false);
function openMarkDialog(value) {
  isOpenDialog.value = value;
}
function submitAddMark(input) {
  inputMark.value = input;
}

function driverModuleListFun() {
  loading.value = true;
  driverModuleList(searchForm, props.id).then(res => {
    tableData.value = res.data.data.lists;
    total.value = res.data.data.count;
  }).finally(() => {
    loading.value = false;
  });
}
function downloadClick(row) {
  fileDownload(row.id, props.id).then(res => {
    window.open(res.data.data.url);
  });
}
function handleSizeChange(val) {
  searchForm.page = 1;
  searchForm['page_size'] = val;
  driverModuleListFun();
}
function handleCurrentChange(val) {
  searchForm.page = val;
  driverModuleListFun();
}
function getRowClass({ row }) {
  if (!row.download_flag) {
    return "row-expand-cover";
  } else {
    return "";
  }
}
</script>
<style lang="scss" scoped>
:deep .el-table .row-expand-cover .cell .el-table__expand-icon {
    display: none;
  }

.buttonStyle{
  display:flex;
  align-items:center ;
}
</style>
