<template>
  <!-- 符号链接 -->
  <div>
    <div
      class="single-inspection"
    >
      <zr-input
        v-model="searchForm.name"
        placeholder="文件名称"
        @keyup.enter="symbolLinkListFun"
      >
        <template #append>
          <zr-button @click="symbolLinkListFun">
            <zr-icon
              name="Search"
            />
          </zr-button>
        </template>
      </zr-input>
    </div>
    <zr-table
      v-loading="loading"
      :data="tableData"
      class="scroll-bar"
      :empty-text="detailData.volatility_version == 'V3' ? '分析引擎不支持该模块 或 内存镜像中不包含相关数据' : '暂无数据'"
    >
      <zr-table-column
        prop="offset"
        label="偏移量"
      />
      <zr-table-column
        prop="create_time"
        label="创建时间"
      />
      <zr-table-column
        prop="source_file"
        label="源文件"
      />
      <zr-table-column
        prop="link_file"
        label="链接文件"
      />
      <zr-table-column
        prop="marks"
        label="标记"
        width="200"
      >
        <template #default="scope">
          <div class="tag-container">
            <span v-if="scope.row.marks && scope.row.marks.length>0">
              <zr-tag
                v-for="item in scope.row.marks"
                :key="item"
                class="tag-item"
              >{{ item }}
              </zr-tag>
            </span>
            <span v-else> - </span>
          </div>
        </template>
      </zr-table-column>
      <zr-table-column
        prop=""
        label="操作"
        width="120"
      >
        <template #default="props">
          <Mark
            :row-data="props.row"
            :analysis-id="id"
            :input-mark="inputMark"
            @openMarkDialog="openMarkDialog"
            @listFun="symbolLinkListFun"
          />
        </template>
      </zr-table-column>
    </zr-table>
    <DetailPagination
      v-if="total>0"
      :search-form="searchForm"
      :total="total"
      @handleSizeChange="handleSizeChange"
      @handleCurrentChange="handleCurrentChange"
    />
    <MarkAddDialog
      :is-open-dialog="isOpenDialog"
      :analysis-id="id"
      @submitAddMark="submitAddMark"
      @openMarkDialog="openMarkDialog"
    />
  </div>
</template>
<script setup>
import { ref, reactive, defineProps } from 'vue';
import { symbolLinkList } from '@/api/threat/mirrorAngle/windows';
import DetailPagination from '@/components/common/DetailPagination.vue';
import Mark from '../Mark.vue';
import MarkAddDialog from '../MarkAddDialog.vue';

const props = defineProps({
  id: String,
  detailData: Object
});
const tableData = ref([]);
const searchForm = reactive({
  page: 1,
  'page_size': 100
});
const total = ref(0);
const loading = ref(false);

onMounted(() => {
  symbolLinkListFun();
});
const inputMark = ref('');
const isOpenDialog = ref(false);
function openMarkDialog(value) {
  isOpenDialog.value = value;
}
function submitAddMark(input) {
  inputMark.value = input;
}
function symbolLinkListFun() {
  loading.value = true;
  symbolLinkList(searchForm, props.id).then(res => {
    tableData.value = res.data.data.lists;
    total.value = res.data.data.count;
  }).finally(() => {
    loading.value = false;
  });
}
function handleSizeChange(val) {
  searchForm.page = 1;
  searchForm['page_size'] = val;
  symbolLinkListFun();
}
function handleCurrentChange(val) {
  searchForm.page = val;
  symbolLinkListFun();
}
</script>
