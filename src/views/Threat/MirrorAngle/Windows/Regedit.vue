<template>
  <!-- 注册表 -->
  <div>
    <zr-tabs
      v-model="activeName"
      type="card"
      class="zr-tabs"
      @tab-change="tabChange"
    >
      <zr-tab-pane
        v-for="item in tabOption"
        :key="item.name"
        :label="item.label"
        :name="item.name"
      />
    </zr-tabs>
    <!-- 注册表配置文件 -->
    <div
      v-if="activeName==='1'"
    >
      <div
        class="single-inspection"
      >
        <zr-input
          v-model="searchForm.name"
          placeholder="文件路径"
          @keyup.enter="handleChange"
        >
          <template #append>
            <zr-button @click="handleChange">
              <zr-icon
                name="Search"
              />
            </zr-button>
          </template>
        </zr-input>
      </div>
      <zr-table
        ref="machineTable"
        v-loading="loading"
        :data="tableData"
        class="scroll-bar"
        empty-text="暂无数据"
        :row-class-name="getRowClass"
        row-key="id"
      >
        <zr-table-column
          type="expand"
          width="30"
        >
          <template #default="props">
            <FileMsg
              :file-msg="props.row"
            />
          </template>
        </zr-table-column>
        <zr-table-column
          prop="offset"
          label="偏移量"
          width="200"
          show-overflow-tooltip
        />
        <zr-table-column
          prop="path"
          label="完整路径"
          show-overflow-tooltip
        />
        <zr-table-column
          prop="marks"
          label="标记"
          width="200"
        >
          <template #default="scope">
            <div class="tag-container">
              <span v-if="scope.row.marks && scope.row.marks.length>0">
                <zr-tag
                  v-for="item in scope.row.marks"
                  :key="item"
                  class="tag-item"
                >{{ item }}
                </zr-tag>
              </span>
              <span v-else> - </span>
            </div>
          </template>
        </zr-table-column>
        <zr-table-column
          label="操作"
          width="180"
        >
          <template #default="scope">
            <div class="buttonStyle">
              <zr-button
                style="margin-right: 10px;"
                link
                type="primary"
                :disabled="!scope.row.download_flag"
                @click="downloadClick(scope.row)"
              >下载</zr-button>
              <Mark
                :row-data="scope.row"
                :analysis-id="id"
                :input-mark="inputMark"
                @openMarkDialog="openMarkDialog"
                @listFun="handleChange"
              />
            </div>
          </template>
        </zr-table-column>
      </zr-table>
    </div>
    <!-- 注册表内容 -->
    <div v-else>
      <zr-button
        v-if="status==='notStarted'||status==='processing'"
        :type="status==='error'? 'danger':'primary'"
        plain
        :loading="status==='processing'"
        style="width: 100%;"
        :disabled="status==='error'"
        @click="calculateClick"
      >{{ status==='notStarted'?'点击计算': status==='error' ? '解析异常' :'计算中' }}</zr-button>
      <div v-else>
        <div
          class="file-search"
        >
          <span>
            <zr-input
              v-model="searchForm.name"
              placeholder=""
              clearable
              @keyup.enter="searchFun"
            >
              <template #prepend>
                <zr-select
                  v-model="searchForm.type"
                  placeholder="请选择查询条件"
                >
                  <zr-option
                    v-for="(value,key) in filterConditionOption"
                    :key="key"
                    :label="value"
                    :value="key"
                  />
                </zr-select>
              </template>
              <template #append>
                <zr-button @click="searchFun">
                  <zr-icon
                    name="Search"
                  />
                </zr-button>
              </template>
            </zr-input>
          </span>

        </div>
        <div
          class="regedit"
        >
          <div
            class="file-tree"
          >
            <zr-card
              class="box-card"
            >
              <Tree
                :tree-loading="treeLoading"
                :data-source="dirList"
                :icon="true"
                :placeholder-msg="'文件名称'"
                @currentChange="currentChange"
              />
            </zr-card>
          </div>
          <div class="file-msg">
            <zr-table
              v-loading="loading"
              style="margin-top: 0;"
              :data="tableData"
              empty-text="暂无数据"
              class="scroll-bar"
            >
              <zr-table-column
                prop="offset"
                label="偏移量"
                width="200"
                show-overflow-tooltip
              />
              <zr-table-column
                prop="key"
                label="键"
                show-overflow-tooltip
              />
              <zr-table-column
                prop="name"
                label="名称"
                width="200"
                show-overflow-tooltip
              />
              <zr-table-column
                prop="type"
                label="数据类型"
                width="110"
                show-overflow-tooltip
              />
              <zr-table-column
                prop="data"
                label="值"
                show-overflow-tooltip
              >
                <template #default="scope">
                  {{ scope.row.data || '-' }}
                </template>
              </zr-table-column>
              <zr-table-column
                prop="last_written_time"
                label="最后写入时间"
                width="200"
                show-overflow-tooltip
              />
              <zr-table-column
                prop="marks"
                label="标记"
                width="200"
              >
                <template #default="scope">
                  <div class="tag-container">
                    <span v-if="scope.row.marks && scope.row.marks.length>0">
                      <zr-tag
                        v-for="item in scope.row.marks"
                        :key="item"
                        class="tag-item"
                      >{{ item }}
                      </zr-tag>
                    </span>
                    <span v-else> - </span>
                  </div>
                </template>
              </zr-table-column>
              <zr-table-column
                prop=""
                label="操作"
                width="120"
              >
                <template #default="props">
                  <Mark
                    :row-data="props.row"
                    :analysis-id="id"
                    :input-mark="inputMark"
                    @openMarkDialog="openMarkDialog"
                    @listFun="handleChange"
                  />
                </template>
              </zr-table-column>
            </zr-table>
          </div>
        </div>

      </div>
    </div>

    <DetailPagination
      v-if="(activeName=='1' || status=='completed') && total>0"
      :search-form="searchForm"
      :total="total"
      @handleSizeChange="handleSizeChange"
      @handleCurrentChange="handleCurrentChange"
    />
    <MarkAddDialog
      :is-open-dialog="isOpenDialog"
      :analysis-id="id"
      @submitAddMark="submitAddMark"
      @openMarkDialog="openMarkDialog"
    />
  </div>
</template>
<script setup>
import { ref, reactive, defineProps } from 'vue';
import { ZrMessage } from 'qianji-ui';
import { regeditList, regeditFileList, dirTree, filterCondition } from '@/api/threat/mirrorAngle/windows';
import { fileDownload, fileDetail, statusApi, calculateApi } from '@/api/threat/mirrorAngle/index';
import FileMsg from '../FileMsg.vue';
import DetailPagination from '@/components/common/DetailPagination.vue';
import Tree from '@/components/common/Tree.vue';
import Mark from '../Mark.vue';
import MarkAddDialog from '../MarkAddDialog.vue';

const props = defineProps({
  id: String
});
const activeName = ref('1');
const tabOption = [
  {
    label: '注册表配置文件',
    name: '1'
  },
  {
    label: '注册表内容',
    name: '2'
  }
];
const tableData = ref([]);
const searchForm = reactive({
  page: 1,
  'page_size': 100,
  type: 'all',
  name: ''
});
const total = ref(0);
const machineTable = ref();
const loading = ref(false);
const dirList = ref([]);
const filterConditionOption = ref({});
const status = ref('');
const inter = ref();
onBeforeUnmount(() => { // 离开页面，销毁定时器
  clearInterval(inter.value);
});
onMounted(() => {
  regeditFileListFun();
});
// function expandChange(row, expandedRows) {
//   if (row.Lazy === undefined) {
//     fileDetail(row.id).then(res => {
//       row.Lazy = res.data.data;
//     });
//     machineTable.value.setCurrentRow(row);
//   }
// }
const inputMark = ref('');
const isOpenDialog = ref(false);
const treeLoading = ref(false);
function openMarkDialog(value) {
  isOpenDialog.value = value;
}
function submitAddMark(input) {
  inputMark.value = input;
}

function dirTreeFun() {
  treeLoading.value = true;
  dirTree(props.id).then(res => {
    dirList.value = getNewTree(res.data.data.dirs || []);
  }).finally(() => {
    treeLoading.value = false;
  });
}
function getNewTree(obj) {
  obj.map(item => {
    item.id = item.path;
    item.label = item.dir_name;
    if (item.dirs && item.dirs.length > 0) {
      item.children = item.dirs;
      getNewTree(item.dirs);
    }
  });
  return obj;
}
function tabChange() {
  searchForm.page = 1;
  searchForm['page_size'] = 100;
  if (activeName.value === '2') {
    statusApiFun();
  }
  handleChange();
}
function handleChange() {
  if (activeName.value === '1') {
    regeditFileListFun();
  } else if (activeName.value === '2' && status.value == 'completed') {
    tableData.value = [];
    total.value = 0;
    filterCondition(props.id).then(res => {
      filterConditionOption.value = res.data.data;
      var key = Object.keys(res.data.data);
      searchForm.type = key[0];
    }).finally(() => {
      dirTreeFun();
      searchFun();
    });
  }
}
function regeditFileListFun() {
  loading.value = true;
  regeditFileList(searchForm, props.id).then(res => {
    tableData.value = res.data.data.lists;
    total.value = res.data.data.count;
  }).finally(() => {
    loading.value = false;
  });
}
function currentChange(data) {
  searchForm.path = data.id;
  searchForm.type = 'all';
  searchForm.name = '';
  regeditListFun();
}
function regeditListFun() {
  loading.value = true;
  regeditList(searchForm, props.id).then(res => {
    tableData.value = res.data.data.lists;
    total.value = res.data.data.count;
  }).finally(() => {
    loading.value = false;
  });
}
function downloadClick(row) {
  fileDownload(row.id, props.id).then(res => {
    window.open(res.data.data.url);
  });
}
function searchFun() {
  regeditListFun();
}
function handleSizeChange(val) {
  searchForm.page = 1;
  searchForm['page_size'] = val;
  handleChange();
}
function handleCurrentChange(val) {
  searchForm.page = val;
  handleChange();
}
function getRowClass({ row }) {
  if (!row.download_flag) {
    return "row-expand-cover";
  } else {
    return "";
  }
}

// 计算
function statusApiFun() {
  var obj = {
    pid: 0,
    name: 'windows_registry_content'
  };
  statusApi(obj, props.id).then(res => {
    status.value = res.data.data.status;
    if (inter.value) {
      clearInterval(inter.value);
    }
    if (res.data.data.status === 'completed') {
      handleChange();
    } else if (res.data.data.status === 'processing') {
      inter.value = setInterval(() => {
        statusApiFun();
      }, 10000);
    }
  });
}

function calculateClick() {
  var obj = {
    pid: 0,
    name: 'windows_registry_content'
  };
  calculateApi(obj, props.id).then(res => {
    if (res.data.code === 0) {
      status.value = 'processing';
      setTimeout(() => {
        statusApiFun();
      }, 10000);
      ZrMessage.success('开始计算');
    }
  });
}
</script>
<style lang="scss" scoped>
    .file-search{
      margin-bottom: 20px;
      text-align: right;
      >span{
        width: 36%;
        display: inline-block;
      }
      .el-select{
        width: 120px;
        background: #fff;
      }
    }
  .regedit{
    display: flex;
    justify-content: space-between;
    .file-tree{
        min-width: 365px;
    }
    .file-msg{
      flex: 1;
      margin-left: 30px;
        // width: calc(100% - 380px);
    }
}
  :deep .el-table .row-expand-cover .cell .el-table__expand-icon {
      display: none;
    }
    .buttonStyle{
      display: flex;
      align-items:center ;
    }
  </style>
