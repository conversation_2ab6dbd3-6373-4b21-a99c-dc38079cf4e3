<template>
  <div class="mark">
    <zr-dropdown
      trigger="click"
      :hide-on-click="false"
      placement="bottom-end"
      @visible-change="handleVisibleChange"
    >
      <span class="zr-dropdown-link">
        标记
        <Icon
          name="zr-down"
          class="el-icon--right"
        />
      </span>
      <template #dropdown>
        <zr-dropdown-menu>
          <zr-dropdown-item
            v-for="(value,key) in markOption"
            :key="key"
            :command="key"
            @click="markClick(key)"
            @mouseover="mouseoverFun(key)"
            @mouseleave="mouseleaveFun(key)"
          >
            {{ key }}
            <div style="width: 40px;height: 20px;display: flex;align-items: center;">
              <p style="width: 50%;height: 100%;padding-top:2px">
                <zr-icon
                  v-if="value"
                  name="Check"
                  color="#1890ff"
                  size="16"
                />
              </p>
              <p style="width: 50%;height: 100%;">
                <zr-icon
                  v-show="delKey===key"
                  name="Close"
                  class="el-icon--right"
                  size="14"
                  @click.stop="deleteMarkFun(key)"
                />
              </p>
            </div>
          </zr-dropdown-item>
          <zr-dropdown-item
            command="add"
            divided
            @click="openMarkDialog"
          >添加</zr-dropdown-item>
        </zr-dropdown-menu>
      </template>
    </zr-dropdown>
  </div>
</template>
<script setup>
import { defineProps, ref, reactive, defineEmits, watch } from 'vue';
import { getMarkList, checkMark, cancleMark, deleteMark } from '@/api/threat/mirrorAngle/windows';
import { ZrMessage, ZrMessageBox } from 'qianji-ui';
const emits = defineEmits(['openMarkDialog', 'listFun']);

const props = defineProps({
  rowData: {
    type: Object
  },
  analysisId: {
    type: String
  },
  inputMark: {
    type: String
  }
});
const markOption = ref([]);

const initialStatus = ref(false);
const delKey = ref('');

function getMarkListFun() {
  getMarkList(props.analysisId, props.rowData.id).then(res => {
    markOption.value = res.data.data.list;
  }).catch(() => {
    markOption.value = [];
  });
}

function handleVisibleChange() { // 打开下拉菜单获取下拉内容
  getMarkListFun();
}

function openMarkDialog() { // 添加
  emits('openMarkDialog', true);
}


function markClick(command) { // 单个标记的点击方法
  for (const key in markOption.value) {
    if (key == command) {
      initialStatus.value = markOption.value[key];
    }
  }
 
  // 切换标记的选中状态
  markOption[command] = !initialStatus.value;

  if (markOption[command]) {
    checkMark(props.analysisId, props.rowData.id, { 'mark_name': command }).then(res => {
      ZrMessage.success('标记成功');
      getMarkListFun();
      emits('listFun');
    });
  } else {
    cancleMark(props.analysisId, props.rowData.id, { 'mark_name': command }).then(res => {
      ZrMessage.success('取消标记成功');
      getMarkListFun();
      emits('listFun');
    });
  }
}

function mouseoverFun(data) { // 鼠标移入
  delKey.value = data;
}

function mouseleaveFun(data) { // 鼠标移出
  delKey.value = '';
}

function deleteMarkFun(key) { // 删除标记
  ZrMessageBox.confirm(
    '<p style="margin-right:4px;display:inline-block;width: 21px;height: 21px;border-radius: 50%;background: #ff9900;color: #fff;text-align:center;line-height:21px;">!</p><span style="font-weight:bold">是否删除标签 ' + key + ' ?' + '</span> <br><span style="margin-left:25px"/>删除标签以后，报告内与标签关联的数据也会解除关联',
    '确认操作', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      dangerouslyUseHTMLString: true
    }).then(() => {
    deleteMark(props.analysisId, { 'mark_name': key }).then(res => {
      ZrMessage.success('删除标签成功');
      emits('listFun'); // 刷新相应的列表
    });
  });
}

</script>

<style lang="scss" scoped>
.zr-dropdown-link {
  cursor: pointer;
  color: var(--el-color-primary);
  display: flex;
  align-items: center;
}
.mark{
  display: flex;
}
.el-dropdown-menu{
  max-height: 300px;

}

:deep(li.el-dropdown-menu__item:last-child){
  position: sticky;
  bottom: 0;
  left: 0;
  background: #fff;
  color: #1890FF;
}

:deep(.el-dropdown-menu__item){
  display: flex;
  justify-content: space-between;
}
</style>
