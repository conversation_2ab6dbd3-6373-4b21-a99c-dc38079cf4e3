<template>
  <!-- 临时文件系统 -->
  <div class="scratch-file">
    <div class="file-tree">
      <zr-card
        class="box-card"
      >
        <zr-button
          v-if="dirList.length>0"
          link
          type="primary"
          style="float: right;margin: 0 20px 10px 0;"
          left-icon="zr-download"
          @click="exportClick"
        >导出文件列表</zr-button>
        <Tree
          :id="'61wmQn3y9lAWGd65AdYbr'"
          :data-source="dirList"
          :icon="true"
          :volatility-version="detailData.volatility_version"
          :placeholder-msg="'文件名称'"
          @currentChange="currentChange"
        />
      </zr-card>
    </div>
    <div
      v-if="searchForm.path!=='/'"
      class="file-msg"
    >
      <zr-table
        ref="machineTable"
        v-loading="loading"
        :data="tableData"
        :empty-text="detailData.volatility_version == 'V3' ? '分析引擎不支持该模块 或 内存镜像中不包含相关数据' :'暂无数据'"
        :row-class-name="getRowClass"
        class="scroll-bar"
        row-key="ID"
      >
        <zr-table-column
          type="expand"
          width="30"
        >
          <template #default="props">
            <FileMsg
              :file-msg="props.row"
            />
          </template>
        </zr-table-column>
        <zr-table-column
          prop="name"
          label="名称"
        />
        <zr-table-column
          prop="path"
          label="路径"
        />
        <zr-table-column
          prop="size"
          label="大小 (B)"
        />
        <zr-table-column
          prop="marks"
          label="标记"
          width="200"
        >
          <template #default="scope">
            <div class="tag-container">
              <span v-if="scope.row.marks && scope.row.marks.length>0">
                <zr-tag
                  v-for="item in scope.row.marks"
                  :key="item"
                  class="tag-item"
                >{{ item }}
                </zr-tag>
              </span>
              <span v-else> - </span>
            </div>
          </template>
        </zr-table-column>
        <zr-table-column
          label="操作"
          width="120"
        >
          <template #default="scope">
            <div class="buttonStyle">
              <zr-button
                link
                type="primary"
                style="margin-right: 10px;"
                :disabled="!scope.row.download_flag"
                @click="downloadClick(scope.row)"
              >下载</zr-button>
              <Mark
                :row-data="scope.row"
                :analysis-id="id"
                :input-mark="inputMark"
                @openMarkDialog="openMarkDialog"
                @listFun="scratchFileListFun"
              />
            </div>
        
          </template>
        </zr-table-column>
      </zr-table>
      <DetailPagination
        v-if="total>0"
        :search-form="searchForm"
        :total="total"
        @handleSizeChange="handleSizeChange"
        @handleCurrentChange="handleCurrentChange"
      />
      <MarkAddDialog
        :is-open-dialog="isOpenDialog"
        :analysis-id="id"
        @submitAddMark="submitAddMark"
        @openMarkDialog="openMarkDialog"
      />
    </div>

  </div>
</template>
<script setup>
import Tree from '@/components/common/Tree.vue';
import { defineProps } from 'vue';
import { scratchFileTreeList, scratchFileList } from '@/api/threat/mirrorAngle/linux';
import { fileDownload, fileDetail } from '@/api/threat/mirrorAngle/index';
import FileMsg from '../FileMsg.vue';
import DetailPagination from '@/components/common/DetailPagination.vue';
import Mark from '../Mark.vue';
import MarkAddDialog from '../MarkAddDialog.vue';
const props = defineProps({
  id: String,
  detailData: Object
});
const tableData = ref([]);
const searchForm = reactive({
  path: '/',
  page: 1,
  'page_size': 100
});
const total = ref(0);
const dirList = ref([]);
const machineTable = ref();
const loading = ref(false);

onMounted(() => {
  scratchFileTreeListFun();
});
// function expandChange(row, expandedRows) {
//   console.log(row);
//   if (row.Lazy === undefined) {
//     fileDetail(row.id).then(res => {
//       row.Lazy = res.data.data;
//     });
//     machineTable.value.setCurrentRow(row);
//   }
// }
// 标记
const inputMark = ref('');
const isOpenDialog = ref(false);
function openMarkDialog(value) {
  isOpenDialog.value = value;
}
function submitAddMark(input) {
  inputMark.value = input;
}


function currentChange(data) {
  searchForm.path = data.id;
  scratchFileListFun();
}
function scratchFileListFun() {
  loading.value = true;
  scratchFileList(searchForm, props.id).then(res => {
    tableData.value = res.data.data.files.list;
    total.value = res.data.data.files.count;
  }).finally(() => {
    loading.value = false;
  });
}
function scratchFileTreeListFun(obj) {
  scratchFileTreeList(props.id).then(res => {
    dirList.value = getNewTree(res.data.data.dirs || []);
  });
}
function getNewTree(obj) {
  obj.map(item => {
    item.id = item.path;
    item.label = item.dir_name;
    if (item.dirs && item.dirs.length > 0) {
      item.children = item.dirs;
      getNewTree(item.dirs);
    }
  });
  return obj;
}

function exportClick() { // 导出文件列表
  window.open(window.BASE_URL + '/mfp-service/api/v1/threat_detection/linux/' + props.id + '/tmpfs/data');
}

function downloadClick(row) {
  fileDownload(row.id, props.id).then(res => {
    window.open(res.data.data.url);
  });
}
function handleSizeChange(val) {
  searchForm.page = 1;
  searchForm['page_size'] = val;
  scratchFileListFun();
}
function handleCurrentChange(val) {
  searchForm.page = val;
  scratchFileListFun();
}
function getRowClass({ row }) {
  if (!row.download_flag) {
    return "row-expand-cover";
  } else {
    return "";
  }
}
</script>
<style lang="scss" scoped>
.scratch-file{
  display: flex;
    justify-content: space-between;
    .file-tree{
        width: 350px;
    }
    .file-msg{
        width: calc(100% - 380px);
    }
}
:deep .el-table .row-expand-cover .cell .el-table__expand-icon {
    display: none;
  }
  .group{
    border: none;
  }
  .buttonStyle{
  display:flex;
  align-items:center ;
}
</style>
