<template>
  <!-- 进程 -->
  <div class="course">
    <div class="course-tree">
      <zr-card
        class="box-card"
      >
        <zr-button
          v-if="treeList.length>0"
          link
          type="primary"
          style="float: right;margin: 0 20px 10px 0;"
          left-icon="zr-download"
          @click="exportClick"
        >导出进程列表</zr-button>
        <Tree
          :data-source="treeList"
          :operate="true"
          :placeholder-msg="'进程ID或者进程名称'"
          @currentChange="currentChange"
          @downloadClick="downloadClick"
        />
      </zr-card>
    </div>
    <div
      v-if="treeData"
      class="course-msg"
    >
      <zr-tabs
        v-model="activeName"
        type="card"
        class="zr-tabs"
        @tab-change="handleChange"
      >
        <zr-tab-pane
          v-for="item in tabOption"
          :key="item.name"
          :label="item.label"
          :name="item.name"
        />
        <div
          v-if="activeName==='1'"
          style="float: right;display: flex;"
        >
          <span
            v-if="courseBasicList.marks && courseBasicList.marks.length>0"
            class="basicMarkTag"
          >
            <zr-tag
              v-for="item in courseBasicList.marks"
              :key="item"
              class="tag-item"
            >
              {{ item }}
            </zr-tag>
          </span>
          <zr-dropdown
            trigger="click"
            :hide-on-click="false"
            placement="bottom-end"
            @visible-change="handleVisibleChange(courseBasicList)"
          >
            <span class="zr-dropdown-link">
              标记
              <Icon
                name="zr-down"
                class="el-icon--right"
              />
            </span>
            <template #dropdown>
              <zr-dropdown-menu>
                <zr-dropdown-item
                  v-for="(value,key) in markOption"
                  :key="key"
                  :command="key"
                  @click="markClick(key)"
                  @mouseover="mouseoverFun(key)"
                  @mouseleave="mouseleaveFun(key)"
                >
                  {{ key }}
                  <div style="width: 40px;height: 20px;display: flex;align-items: center;">
                    <p style="width: 50%;height: 100%;">
                      <zr-icon
                        v-if="value"
                        name="Check"
                        color="#1890ff"
                        size="16"
                      />
                    </p>
                    <p style="width: 50%;height: 100%;">
                      <zr-icon
                        v-show="delKey===key"
                        name="Close"
                        class="el-icon--right"
                        size="14"
                        @click.stop="deleteMarkFun(key)"
                      />
                    </p>
                  </div>
                </zr-dropdown-item>
                <zr-dropdown-item
                  command="add"
                  divided
                  @click="openMarkDialog"
                >添加</zr-dropdown-item>
              </zr-dropdown-menu>
            </template>
          </zr-dropdown>
        </div>
      </zr-tabs>
      <div
        v-if="activeName==='1'"
        class="course-basic"
      >
        <div
          v-for="item in courseBasicOption"
          :key="item.label"
        >
          <p>{{ item.label }}</p>
          <p>
            <span>{{ courseBasicList[item.key]||courseBasicList[item.key]===0?courseBasicList[item.key]:'-' }}</span>
            <zr-button
              v-if="courseBasicList[item.key]&&item.copy"
              type="primary"
              link
              left-icon="zr-copy-file"
              @click="copyClick(courseBasicList[item.key])"
            />
          </p>
        </div>
      </div>
      <div v-else>
        <zr-button
          v-if="activeName==='2'&&(status==='notStarted'||status==='processing')"
          :type="status==='error'? 'danger':'primary'"
          plain
          style="width: 100%;"
          :disabled="status==='error'"
          :loading="status==='processing'"
          @click="calculateClick"
        >{{ status==='notStarted'?'点击计算进程ID：'+treeData.pid+' 的内存映射': status==='error'? '解析异常' : '计算中' }}</zr-button>
        <div
          v-if="activeName==='2'&&status==='completed'"
          style="margin: 12px 0 0;"
        >
          <zr-form
            ref="downloadFormRef"
            :model="downloadForm"
            style="display: flex;float: right;"
          >
            <zr-form-item
              label=""
              prop="start_addr"
              :rules="[
                {
                  pattern: /^0x[0-9A-Fa-f]+$/,
                  message: '只能输入十六进制数值',
                  trigger: 'blur'
                }]"
            >
              <zr-input
                v-model="downloadForm.start_addr"
                style="width:200px"
                placeholder="起始地址"
              />
              <span style="margin:0 5px">-</span>
            </zr-form-item>
            <zr-form-item
              label=""
              prop="end_addr"
              :rules="[
                {
                  pattern: /^0x[0-9A-Fa-f]+$/,
                  message: '只能输入十六进制数值',
                  trigger: 'blur'
                }]"
            >
              <zr-input
                v-model="downloadForm.end_addr"
                style="width:200px"
                placeholder="结束地址"
              />
            </zr-form-item>
            <zr-button
              type="primary"
              style="margin-left: 12px;"
              :disabled="tableData.length===0"
              @click="downloadMemoryClick(downloadFormRef)"
            >下载进程内存块</zr-button>
          </zr-form>
          <!-- <zr-button
            type="primary"
            style="margin-left: 12px;"
            :disabled="tableData.length===0"
            @click="downloadMemoryClick()"
          >下载进程内存块</zr-button> -->
        </div>
        <div
          v-if=" activeName==='4'"
          class="single-inspection"
        >
          <zr-input
            v-model="searchForm.ip"
            placeholder="IP地址"
            @keyup.enter="tabelListFun('4',true)"
          >
            <template #append>
              <zr-button @click="tabelListFun('4',true)">
                <zr-icon
                  name="Search"
                />
              </zr-button>
            </template>
          </zr-input>
        </div>
        <div
          v-if=" activeName==='3'"
          class="single-inspection"
        >
          <zr-input
            v-model="searchForm.path"
            placeholder="文件路径"
            @keyup.enter="tabelListFun('3',true)"
          >
            <template #append>
              <zr-button @click="tabelListFun('3',true)">
                <zr-icon
                  name="Search"
                />
              </zr-button>
            </template>
          </zr-input>
        </div>
        <div
          v-if=" activeName==='5'"
          class="single-inspection"
        >
          <zr-input
            v-model="searchForm.path"
            placeholder="文件路径"
            @keyup.enter="tabelListFun('5',true)"
          >
            <template #append>
              <zr-button @click="tabelListFun('5',true)">
                <zr-icon
                  name="Search"
                />
              </zr-button>
            </template>
          </zr-input>
        </div>
        <div
          v-if=" activeName==='6'"
          class="single-inspection"
        >
          <zr-input
            v-model="searchForm.variable"
            placeholder="变量/值"
            @keyup.enter="tabelListFun('6',true)"
          >
            <template #append>
              <zr-button @click="tabelListFun('6',true)">
                <zr-icon
                  name="Search"
                />
              </zr-button>
            </template>
          </zr-input>
        </div>
        
        <zr-table
          v-if="activeName!=='2'||(activeName==='2'&&status==='completed')"
          ref="machineTable"
          v-loading="loading"
          :data="tableData"
          empty-text="暂无数据"
          :row-class-name="getRowClass"
          class="scroll-bar"
          row-key="id"
        >
          <zr-table-column
            v-if="activeName=='3' || activeName=='5'"
            type="expand"
            width="30"
          >
            <template #default="props">
              <FileMsg
                :file-msg="props.row"
              />
            </template>
          </zr-table-column>
          <zr-table-column
            v-for="item in tableOption"
            :key="item.label"
            :prop="item.prop"
            :label="item.label"
            :width="item.width"
          >
            <template #default="scope">
              <p v-if="item.prop==='size'">
                {{ numDelivery(scope.row.size) }}
              </p>
              <div
                v-else-if="item.prop==='marks'"
                class="tag-container"
              >
                <span v-if="scope.row.marks && scope.row.marks.length>0">
                  <zr-tag
                    v-for="item in scope.row.marks"
                    :key="item"
                    class="tag-item"
                  >{{ item }}
                  </zr-tag>
                </span>
                <span v-else> - </span>
              </div>
              <div
                v-else
                class="table-item-content"
              >
                <zr-tooltip
                  class="box-item"
                  effect="dark"
                  :disabled="!scope.row[item.prop]"
                  :content="scope.row[item.prop]+''"
                  placement="top"
                >
                  {{ scope.row[item.prop]||'-' }}
                </zr-tooltip>
              </div>
            </template>
          </zr-table-column>
          <zr-table-column
            v-if="activeName !=='1'"
            label="操作"
            width="120"
          >
            <template #default="scope">
              <!-- <zr-button
                v-if="activeName=='5'"
                link
                type="primary"
                @click="handleClick(scope.row.id)"
              >详情</zr-button> -->
              <div class="buttonStyle">
                <zr-button
                  v-if="activeName=='3'"
                  link
                  type="primary"
                  :disabled="!scope.row.download_flag"
                  @click="downloadClick(scope.row.id)"
                >下载</zr-button>
                <zr-dropdown
                  trigger="click"
                  :hide-on-click="false"
                  placement="bottom-end"
                  @visible-change="handleVisibleChange(scope.row)"
                >
                  <span class="zr-dropdown-link">
                    标记
                    <Icon
                      name="zr-down"
                      class="el-icon--right"
                    />
                  </span>
                  <template #dropdown>
                    <zr-dropdown-menu>
                      <zr-dropdown-item
                        v-for="(value,key) in markOption"
                        :key="key"
                        :command="key"
                        @click="markClick(key)"
                        @mouseover="mouseoverFun(key)"
                        @mouseleave="mouseleaveFun(key)"
                      >
                        {{ key }}
                        <div style="width: 40px;height: 20px;display: flex;align-items: center;">
                          <p style="width: 50%;height: 100%;">
                            <zr-icon
                              v-if="value"
                              name="Check"
                              color="#1890ff"
                              size="16"
                            />
                          </p>
                          <p style="width: 50%;height: 100%;">
                            <zr-icon
                              v-show="delKey===key"
                              name="Close"
                              class="el-icon--right"
                              size="14"
                              @click.stop="deleteMarkFun(key)"
                            />
                          </p>
                        </div>
                      </zr-dropdown-item>
                      <zr-dropdown-item
                        command="add"
                        divided
                        @click="openMarkDialog"
                      >添加</zr-dropdown-item>
                    </zr-dropdown-menu>
                  </template>
                </zr-dropdown>
              </div>
            </template>
          </zr-table-column>
        </zr-table>
        <DetailPagination
          v-if="(activeName!=='2'||(activeName==='2'&&status==='completed'))&&total>0"
          :search-form="searchForm"
          :total="total"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
        />
      </div>
    </div>
    <!-- 添加标记弹窗 -->
    <zr-dialog
      v-model="markDialogVisible"
      title="添加标记"
      :width="480"
      destroy-on-close
    >
      <span>
        <zr-input
          v-model="inputMark"
          maxlength="10"
          show-word-limit
          placeholder="请输入标记，一次仅添加一个标记，最大长度为10个字符"
        />
      </span>
      <template #footer>
        <span class="dialog-footer">
          <zr-button @click="markDialogVisible = false">取消</zr-button>
          <zr-button
            type="primary"
            @click="submitAddMark"
          >提交</zr-button>
        </span>
      </template>
    </zr-dialog>
  </div>
</template>
<script setup>
import { ref, getCurrentInstance, defineProps } from 'vue';
import Tree from '@/components/common/Tree.vue';
import { courseTree, basicList, memMapList, psMapList, sockStatList, elfsList, envvarsList, downloadMemory } from '@/api/threat/mirrorAngle/linux';
import { fileDownload, fileDetail, statusApi, calculateApi } from '@/api/threat/mirrorAngle/index';
import { getMarkList, addMark, checkMark, cancleMark, deleteMark } from '@/api/threat/mirrorAngle/windows';

import FileMsg from '../FileMsg.vue';
import { ZrMessage, ZrMessageBox } from 'qianji-ui';
import DetailPagination from '@/components/common/DetailPagination.vue';
import { downloadFun } from '@/utils/downloadUtils';
const { proxy } = getCurrentInstance();
const props = defineProps({
  id: String
});
const loading = ref(false);
const activeName = ref('1');
const tabOption = [
  {
    label: '基本信息',
    name: '1'
  },
  {
    label: '内存映射',
    name: '2'
  },
  {
    label: '进程映射',
    name: '3'
  },
  {
    label: '网络连接',
    name: '4'
  },
  {
    label: '动态链接库',
    name: '5'
  },
  {
    label: '环境变量',
    name: '6'
  }
];
const tableData = ref([]);
const courseBasicOption = [ // 基本信息表
  {
    label: '进程名',
    key: 'comm',
    copy: true
  },
  {
    label: '进程ID',
    key: 'pid',
    copy: true
  },
  {
    label: '父进程ID',
    key: 'ppid',
    copy: true
  },
  {
    label: '线程ID',
    key: 'tid',
    copy: true
  },
  {
    label: 'UID',
    key: 'uid'
  },
  {
    label: 'GID',
    key: 'gid'
  },
  {
    label: '启动时间',
    key: 'start_time'
  },
  {
    label: '偏移量',
    key: 'offset'
  },
  {
    label: '大小 (KB)',
    key: 'size'
  },
  {
    label: 'MD5',
    key: 'md5',
    copy: true
  },
  {
    label: 'SHA1',
    key: 'sha1',
    copy: true
  },
  {
    label: 'SHA256',
    key: 'sha256',
    copy: true
  }
];
const courseBasicList = ref({});
const treeList = ref([]);

const tableOption = ref([]);
const searchForm = ref({
  page: 1,
  'page_size': 100
});
const total = ref(0);
const treeData = ref('');
const machineTable = ref();
const status = ref('');
const inter = ref();
const markOption = ref([]);
const markDialogVisible = ref(false);
const inputMark = ref('');
const initialStatus = ref(false);
const delKey = ref('');
const downloadFormRef = ref();
const downloadForm = ref({
  'start_addr': '',
  'end_addr': ''
});
onBeforeUnmount(() => { // 离开页面，销毁定时器
  clearInterval(inter.value);
});
// watch(course, (val) => {
//   treeRef.value.filter(val);
// });
onMounted(() => {
  courseTreeFun();
});

function exportClick() {
  window.open(window.BASE_URL + '/mfp-service/api/v1/threat_detection/linux/' + props.id + '/ps_list/download');
}
function statusApiFun() {
  var obj = {
    pid: treeData.value.pid,
    name: 'linux_mem_map'
  };
  statusApi(obj, props.id).then(res => {
    status.value = res.data.data.status;
    if (inter.value) {
      clearInterval(inter.value);
    }
    if (res.data.data.status === 'completed') {
      tabelListFun(activeName.value);
    } else if (res.data.data.status === 'processing') {
      inter.value = setInterval(() => {
        statusApiFun();
      }, 10000);
    }
  });
}
function calculateClick() {
  var obj = {
    pid: treeData.value.pid,
    name: 'linux_mem_map'
  };
  calculateApi(obj, props.id).then(res => {
    if (res.data.code === 0) {
      status.value = 'processing';
      setTimeout(() => {
        statusApiFun();
      }, 10000);
      ZrMessage.success('开始计算');
    }
  });
}
const downloadMemoryClick = (formEl) => { // 下载进程内存块
  if (!formEl) return;
  formEl.validate((valid) => {
    if (valid) {
      const dec1 = parseInt(downloadForm.value.start_addr, 16); // 比较十六进制大小
      const dec2 = parseInt(downloadForm.value.end_addr, 16);
      if (dec1 >= dec2) {
        ZrMessage.error('起始地址必须小于结束地址');
        return;
      }
      ZrMessage.success('正在下载，请稍后...');
      downloadFun('/mfp-service/api/v1/threat_detection/linux/' + props.id + '/' + treeData.value.pid + '/ps_mem_map/download', downloadForm.value);
    } else {
      return false;
    }
  });
};
// function expandChange(row, expandedRows) {
//   if (row.Lazy === undefined) {
//     fileDetail(row.id).then(res => {
//       row.Lazy = res.data.data;
//     });
//     machineTable.value.setCurrentRow(row);
//   }
// }
function courseTreeFun() {
  courseTree(props.id).then(res => {
    treeList.value = getNewTree(res.data.data);
  });
}
function currentChange(data) {
  treeData.value = data;
  clearInterval(inter.value);
  searchForm.value = {
    page: 1,
    'page_size': 100
  };
  if (activeName.value === '1') {
    basicListFun(data);
  } else if (activeName.value === '2') {
    statusApiFun();
  } else {
    tabelListFun(activeName.value);
  }
}
function basicListFun(data) {
  basicList(data, props.id).then(res => {
    courseBasicList.value = res.data.data;
  });
}
function getNewTree(obj) {
  obj.map(item => {
    item.dowId = item.id;
    item.id = item.pid;
    item.label = item.pid + ' - ' + item.name;
    if (item.child && item.child.length > 0) {
      item.children = item.child;
      getNewTree(item.child);
    }
  });
  return obj;
}
function tabelListFun(val, bool) {
  total.value = 0;
  tableData.value = [];
  const fetchData = (promise) => {
    loading.value = true;
    promise.then(res => {
      tableData.value = res.data.data.list;
      total.value = res.data.data.count;
    }).finally(() => {
      loading.value = false;
    });
  };
  if (val === '2' && status.value === 'completed') {
    fetchData(memMapList(treeData.value, props.id, searchForm.value));
  } else if (val === '3') {
    fetchData(psMapList(treeData.value, props.id, searchForm.value));
  } else if (val === '4') {
    fetchData(sockStatList(treeData.value, props.id, searchForm.value));
  } else if (val === '5') {
    fetchData(elfsList(treeData.value, props.id, searchForm.value));
  } else if (val === '6') {
    fetchData(envvarsList(treeData.value, props.id, searchForm.value));
  }
}
function handleChange(val) { // 表格项
  tableData.value = [];
  total.value = 0;
  clearInterval(inter.value);
  switch (val) {
    case '1':
      basicListFun(treeData.value);
      break;
    case '2':
      tableOption.value = [
        {
          prop: 'virtual',
          label: '虚拟地址'
        },
        {
          prop: 'physical',
          label: '物理地址'
        },
        {
          prop: 'size',
          label: '大小 (KB)'
        },
        {
          prop: 'marks',
          label: '标记',
          width: 200
        }
      ];
      statusApiFun();
      break;
    case '3':
      tableOption.value = [
        {
          prop: 'start',
          label: '起始地址'
        },
        {
          prop: 'end',
          label: '终止地址'
        },
        {
          prop: 'flags',
          label: '标志'
        },
        {
          prop: 'pgOff',
          label: '页偏移'
        },
        {
          prop: 'inode',
          label: 'inode节点'
        },
        {
          prop: 'major',
          label: '主设备号'
        },
        {
          prop: 'minor',
          label: '次设备号'
        },
        {
          prop: 'file_path',
          label: '文件路径'
        },
        {
          prop: 'marks',
          label: '标记',
          width: 200
        }
      ];
      break;
    case '4':
      tableOption.value = [
        {
          prop: 'sock_offset',
          label: '偏移量'
        },
        {
          prop: 'proto',
          label: '协议'
        },
        {
          prop: 'source_addr',
          label: '本地地址'
        },
        {
          prop: 'source_port',
          label: '本地端口'
        },
        {
          prop: 'destination_addr',
          label: '远程地址'
        },
        {
          prop: 'destination_port',
          label: '远程端口'
        },
        {
          prop: 'state',
          label: '状态'
        },
        {
          prop: 'marks',
          label: '标记',
          width: 200
        }
      ];
      break;
    case '5': // 动态链接库
      tableOption.value = [
        {
          prop: 'start',
          label: '起始地址'
        },
        {
          prop: 'end',
          label: '终止地址'
        },
        {
          prop: 'path',
          label: '文件路径'
        },
        {
          prop: 'marks',
          label: '标记',
          width: 200
        }
      ];
      break;
    case '6': // 环境变量
      tableOption.value = [
        {
          prop: 'key',
          label: '变量'
        },
        {
          prop: 'value',
          label: '值'
        },
        {
          prop: 'marks',
          label: '标记',
          width: 200
        }
      ];
      break;
  }
  nextTick(() => {
    searchForm.value = {
      page: 1,
      'page_size': 100
    };
    if (val === '2') {
      return;
    }
    tabelListFun(val);
  });
}

// ----标记
const rowData = ref({});
function getMarkListFun(row) {
  getMarkList(props.id, row.id).then(res => {
    markOption.value = res.data.data.list;
  }).catch(() => {
    markOption.value = [];
  });
}

function handleVisibleChange(row) { // 打开下拉菜单获取下拉内容
  rowData.value = row;
  getMarkListFun(row);
}

function openMarkDialog() { // 添加
  inputMark.value = '';
  markDialogVisible.value = true;
}

function submitAddMark() { // 提交添加的标记
  if (inputMark.value == '') {
    ZrMessage.error('请输入标记名称');
    return;
  }
  addMark(props.id, { 'mark_name': inputMark.value }).then(res => {
    ZrMessage.success('添加标记成功');
    markDialogVisible.value = false;
    getMarkListFun(rowData.value);
  });
}

// 列表接口映射
const apiMap = {
  2: memMapList,
  3: psMapList,
  4: sockStatList,
  5: elfsList,
  6: envvarsList
};
function apiList(activeName) {
  const api = apiMap[activeName];
  if (api) {
    api(treeData.value, props.id, searchForm.value).then(res => {
      tableData.value = res.data.data.list;
      total.value = res.data.data.count;
    });
  }
}

function markClick(command) { // 单个标记的点击方法
  for (const key in markOption.value) {
    if (key == command) {
      initialStatus.value = markOption.value[key];
    }
  }
 
  // 切换标记的选中状态
  markOption[command] = !initialStatus.value;

  if (markOption[command]) {
    checkMark(props.id, rowData.value.id, { 'mark_name': command }).then(res => {
      ZrMessage.success('标记成功');
      getMarkListFun(rowData.value);
      if (activeName.value == '1') {
        basicListFun(treeData.value);
      } else {
        apiList(activeName.value);
      }
    });
  } else {
    cancleMark(props.id, rowData.value.id, { 'mark_name': command }).then(res => {
      ZrMessage.success('取消标记成功');
      getMarkListFun(rowData.value);
      if (activeName.value == '1') {
        basicListFun(treeData.value);
      } else {
        apiList(activeName.value);
      }
    });
  }
}

function mouseoverFun(data) { // 鼠标移入
  delKey.value = data;
}

function mouseleaveFun(data) { // 鼠标移出
  delKey.value = '';
}

function deleteMarkFun(key) { // 删除标记
  ZrMessageBox.confirm(
    '<p style="margin-right:4px;display:inline-block;width: 21px;height: 21px;border-radius: 50%;background: #ff9900;color: #fff;text-align:center;line-height:21px;">!</p><span style="font-weight:bold">是否删除标签 ' + key + ' ?' + '</span> <br><span style="margin-left:25px"/>删除标签以后，报告内与标签关联的数据也会解除关联',
    '确认操作', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      dangerouslyUseHTMLString: true
    }).then(() => {
    deleteMark(props.id, { 'mark_name': key }).then(res => {
      ZrMessage.success('删除标签成功');
      if (activeName.value == '1') {
        basicListFun(treeData.value);
      } else {
        apiList(activeName.value);
      }
    });
  });
}

function numDelivery(num) {
  var size = num / 1024;
  var result = parseFloat(size);
  if (isNaN(result)) {
    return false;
  }
  result = Math.round(size * 100) / 100;
  return result;
}
function downloadClick(id) {
  fileDownload(id, props.id).then(res => {
    location.href = res.data.data.url;
  });
}

function handleSizeChange(val) {
  searchForm.value.page = 1;
  searchForm.value['page_size'] = val;
  tabelListFun(activeName.value);
}
function handleCurrentChange(val) {
  searchForm.value.page = val;
  tabelListFun(activeName.value);
}
function copyClick(text) { // 复制
  proxy.copyFun(text);
}
function getRowClass({ row }) {
  if (!row.download_flag) {
    return "row-expand-cover";
  } else {
    return "";
  }
}
</script>

<style lang="scss" scoped>
.course{
    display: flex;
    justify-content: space-between;
    .course-tree{
        width: 350px;
    }
    .course-msg{
        width: calc(100% - 380px);
    }
}
.course-basic{
  padding-top: 8px;
  >div{
    display: flex;
    align-items: center;
    border-bottom: 1px solid #ddd;
    line-height: 42px;
    font-size: 14px;
    >p:nth-child(1){
      width: 200px;
    }
  }
}
:deep .el-table .row-expand-cover .cell .el-table__expand-icon {
    display: none;
  }
  .group{
    border: none;
  }
/* // 标记样式 */
  .zr-dropdown-link {
  cursor: pointer;
  color: var(--el-color-primary);
  display: flex;
  align-items: center;
  margin-left: 10px
}
.buttonStyle{
  display:flex;
  align-items:center ;
}
.el-dropdown-menu{
  max-height: 300px;
}

:deep(li.el-dropdown-menu__item:last-child){
  position: sticky;
  bottom: 0;
  left: 0;
  background: #fff;
  color: #1890FF;
}
:deep(.el-dropdown-menu__item){
  display: flex;
  justify-content: space-between;
}

.basicMarkTag{
  width:200px;
  height: 26px;
  overflow-y: auto;
  .tag-item {
    margin-left: 0px;
    margin-right: 5px;
    margin-top: 2px;
  }
}
</style>
