<template>
  <!-- 内核模块 -->
  <div>
    <div
      class="single-inspection"
    >
      <zr-input
        v-model="searchForm.name"
        placeholder="模块名称"
        @keyup.enter="steinkernListFun"
      >
        <template #append>
          <zr-button @click="steinkernListFun">
            <zr-icon
              name="Search"
            />
          </zr-button>
        </template>
      </zr-input>
    </div>
    <zr-table
      ref="machineTable"
      v-loading="loading"
      :data="tableData"
      :empty-text="detailData.volatility_version == 'V3' ? '分析引擎不支持该模块 或 内存镜像中不包含相关数据' :'暂无数据'"
      :row-class-name="getRowClass"
      row-key="id"
      class="scroll-bar"
    >
      <zr-table-column
        type="expand"
        width="30"
      >
        <template #default="props">
          <FileMsg
            :file-msg="props.row"
          />
        </template>
      </zr-table-column>
      <zr-table-column
        prop="name"
        label="名称"
      />
      <zr-table-column
        prop="offset_string"
        label="偏移量"
      />
      <zr-table-column
        prop="size"
        label="大小 (B)"
        width="250"
      />
      <zr-table-column
        prop="parameter"
        label="加载参数"
      >
        <template #default="scope">
          <span v-if="scope.row.parameter && scope.row.parameter.length > 0 ">
            <zr-tooltip
              placement="top-start"
              :content="scope.row.parameter.join(',')"
            >
              <p class="table-item-content">{{ scope.row.parameter.join(',') }}</p>
            </zr-tooltip>
          </span>
          <span v-else> - </span>
        </template>
      </zr-table-column>
      <zr-table-column
        prop="marks"
        label="标记"
        width="200"
      >
        <template #default="scope">
          <div class="tag-container">
            <span v-if="scope.row.marks && scope.row.marks.length>0">
              <zr-tag
                v-for="item in scope.row.marks"
                :key="item"
                class="tag-item"
              >{{ item }}
              </zr-tag>
            </span>
            <span v-else> - </span>
          </div>
        </template>
      </zr-table-column>
      <zr-table-column
        label="操作"
        width="210"
      >
        <template #default="scope">
          <div class="buttonStyle">
            <zr-button
              link
              type="primary"
              style="margin-right: 10px;min-width:60px"
              :disabled="scope.row.download_flag || scope.row.dumping == 'processing' || scope.row.dumping == 'completed'"
              @click="dumpClick(scope.row)"
            >{{ scope.row.dumping == 'processing' ? '转储中' : scope.row.dumping == 'completed' ? '转储完成' : '转储' }}</zr-button>
            <zr-button
              style="margin-right: 17px;margin-left:0;"
              link
              type="primary"
              :disabled="!scope.row.download_flag"
              @click="downloadClick(scope.row)"
            >下载</zr-button>
            <Mark
              :row-data="scope.row"
              :analysis-id="id"
              :input-mark="inputMark"
              @openMarkDialog="openMarkDialog"
              @listFun="steinkernListFun"
            />
          </div>
        
        </template>
      </zr-table-column>
    </zr-table>
    <DetailPagination
      v-if="total>0"
      :search-form="searchForm"
      :total="total"
      @handleSizeChange="handleSizeChange"
      @handleCurrentChange="handleCurrentChange"
    />
    <MarkAddDialog
      :is-open-dialog="isOpenDialog"
      :analysis-id="id"
      @submitAddMark="submitAddMark"
      @openMarkDialog="openMarkDialog"
    />
  </div>
</template>
<script setup>
import { ref, reactive, defineProps, onBeforeUnmount, onMounted } from 'vue';
import { steinkernList, startDumpApi } from '@/api/threat/mirrorAngle/linux';
import { fileDownload, fileDetail } from '@/api/threat/mirrorAngle/index';
import FileMsg from '../FileMsg.vue';
import DetailPagination from '@/components/common/DetailPagination.vue';
import Mark from '../Mark.vue';
import MarkAddDialog from '../MarkAddDialog.vue';
import { ZrMessageBox, ZrMessage } from 'qianji-ui';
const props = defineProps({
  id: String,
  detailData: Object
});
const tableData = ref([]);
const machineTable = ref();
const searchForm = reactive({
  page: 1,
  'page_size': 100
});
const total = ref(0);
const loading = ref(false);
onBeforeUnmount(() => { // 离开页面，销毁定时器
  clearInterval(inter.value);
});
onMounted(() => {
  steinkernListFun();
});

// function expandChange(row, expandedRows) {
//   if (row.Lazy === undefined) {
//     fileDetail(row.id).then(res => {
//       row.Lazy = res.data.data;
//     });
//     machineTable.value.setCurrentRow(row);
//   }
// }

// 标记
const inputMark = ref('');
const isOpenDialog = ref(false);
function openMarkDialog(value) {
  isOpenDialog.value = value;
}
function submitAddMark(input) {
  inputMark.value = input;
}

const inter = ref();
function steinkernListFun() {
  loading.value = true;
  steinkernList(searchForm, props.id).then(res => {
    if (inter.value) {
      clearInterval(inter.value);
    }
    if (res.data.data.list.map(item => item.dumping).indexOf('processing') !== -1) {
      inter.value = setInterval(() => {
        steinkernListFun();
      }, 10000);
    }
    tableData.value = res.data.data.list;
    total.value = res.data.data.count;
  }).finally(() => {
    loading.value = false;
  });
}

function dumpClick(row) { // 转储
  var obj = {
    pid: row.offset_dec_str,
    name: 'linux_moduledump'
  };
  startDumpApi(obj, props.id).then(res => {
    if (res.data.code === 0) {
      steinkernListFun();
      ZrMessageBox.alert('开始解析并转储内核模块，时间较长，请耐心等待', '提示', {
        confirmButtonText: '关闭',
        type: 'warning'
      });
    }
  });
}
function downloadClick(row) {
  ZrMessage.success('正在下载，请稍后...');
  fileDownload(row.id, props.id).then(res => {
    window.open(res.data.data.url);
  });
}
function handleSizeChange(val) {
  searchForm.page = 1;
  searchForm['page_size'] = val;
  steinkernListFun();
}
function handleCurrentChange(val) {
  searchForm.page = val;
  steinkernListFun();
}
function getRowClass({ row }) {
  if (!row.download_flag) {
    return "row-expand-cover";
  } else {
    return "";
  }
}
</script>
<style lang="scss" scoped>

:deep .el-table .row-expand-cover .cell .el-table__expand-icon {
	display: none;
}
.buttonStyle{
  display:flex;
  align-items:center ;
}
</style>
