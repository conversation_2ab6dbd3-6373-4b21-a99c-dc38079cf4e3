<template>
  <!-- 自定义插件 -->
  <div>
    <zr-table
      :data="tableData"
      :loading="loading"
      empty-text="暂无数据"
      class="scroll-bar"
    >
      <zr-table-column
        prop="name"
        label="名称"
      />
      <zr-table-column
        prop="path"
        label="路径"
      />
      <zr-table-column
        prop=""
        label="操作"
        width="150px"
      >
        <template #default="scope">
          <zr-button
            link
            type="primary"
            @click="downloadClick(scope.row)"
          >下载运行结果</zr-button>
        </template>
      </zr-table-column>
    </zr-table>
  </div>
</template>
<script setup>
import { ref } from 'vue';
import { pluginResultsList } from '@/api/threat/mirrorAngle/windows';
import { useCookies } from 'vue3-cookies';
const { cookies } = useCookies();
const loading = ref(false);
const tableData = ref([]);
const props = defineProps({
  id: String
});

pluginResultsListFun();
function pluginResultsListFun() {
  loading.value = true;
  pluginResultsList(props.id).then(res => {
    tableData.value = res.data.data.list;
  }).finally(() => {
    loading.value = false;
  });
}
function downloadClick(row) {
  axios({
    url: window.BASE_URL + '/mfp-service/api/v1/plugin_results/' + row.id + '/download',
    method: 'get',
    responseType: 'blob',
    headers: { Token: cookies.get('dsp_token') }
  }).then((res) => {
    if (res.data.type == 'application/octet-stream') {
      const blob = new Blob([res.data], { type: 'text/plain' }); // 返回的文件流数据下载成txt文件
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      var name = row.result_file_name;
      link.download = name || '';
      link.href = url;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  });
}
</script>
