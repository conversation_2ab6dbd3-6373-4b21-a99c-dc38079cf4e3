<template>
  <!-- 网络包 -->
  <div>
    <zr-table
      ref="machineTable"
      v-loading="loading"
      :data="tableData"
      :empty-text="detailData.volatility_version == 'V3' ? '分析引擎不支持该模块 或 内存镜像中不包含相关数据' :'暂无数据'"
      :row-class-name="getRowClass"
      row-key="ID"
      class="scroll-bar"
    >
      <zr-table-column
        type="expand"
        width="30"
      >
        <template #default="props">
          <FileMsg
            :file-msg="props.row"
          />
        </template>
      </zr-table-column>
      <zr-table-column
        prop="filename"
        label="名称"
      />
      <zr-table-column
        prop="size"
        label="大小 (B)"
      />
      <zr-table-column
        prop="marks"
        label="标记"
        width="200"
      >
        <template #default="scope">
          <div class="tag-container">
            <span v-if="scope.row.marks && scope.row.marks.length>0">
              <zr-tag
                v-for="item in scope.row.marks"
                :key="item"
                class="tag-item"
              >{{ item }}
              </zr-tag>
            </span>
            <span v-else> - </span>
          </div>
        </template>
      </zr-table-column>
      <zr-table-column
        label="操作"
        width="120"
      >
        <template #default="scope">
          <div class="buttonStyle">
            <zr-button
              link
              type="primary"
              :disabled="!scope.row.download_flag"
              @click="downloadClick(scope.row)"
            >下载</zr-button>
            <Mark
              :row-data="scope.row"
              :analysis-id="id"
              :input-mark="inputMark"
              @openMarkDialog="openMarkDialog"
              @listFun="networkPacketListFun"
            />
          </div>
        </template>
      </zr-table-column>
    </zr-table>
    <DetailPagination
      v-if="total>0"
      :search-form="searchForm"
      :total="total"
      @handleSizeChange="handleSizeChange"
      @handleCurrentChange="handleCurrentChange"
    />
    <MarkAddDialog
      :is-open-dialog="isOpenDialog"
      :analysis-id="id"
      @submitAddMark="submitAddMark"
      @openMarkDialog="openMarkDialog"
    />
  </div>
</template>
<script setup>
import { ref, reactive, defineProps } from 'vue';
import { networkPacketList } from '@/api/threat/mirrorAngle/linux';
import { fileDownload, fileDetail } from '@/api/threat/mirrorAngle/index';
import FileMsg from '../FileMsg.vue';
import DetailPagination from '@/components/common/DetailPagination.vue';
import Mark from '../Mark.vue';
import MarkAddDialog from '../MarkAddDialog.vue';
const props = defineProps({
  id: String,
  detailData: Object
});
const tableData = ref([]);
const searchForm = reactive({
  page: 1,
  'page_size': 100
});
const total = ref(0);
const machineTable = ref();
const loading = ref(false);

onMounted(() => {
  networkPacketListFun();
});

// function expandChange(row, expandedRows) {
//   if (row.Lazy === undefined) {
//     fileDetail(row.id).then(res => {
//       row.Lazy = res.data.data;
//     });
//     machineTable.value.setCurrentRow(row);
//   }
// }

// 标记
const inputMark = ref('');
const isOpenDialog = ref(false);
function openMarkDialog(value) {
  isOpenDialog.value = value;
}
function submitAddMark(input) {
  inputMark.value = input;
}

function networkPacketListFun() {
  loading.value = true;
  networkPacketList(searchForm, props.id).then(res => {
    tableData.value = res.data.data.list;
    total.value = res.data.data.count;
  }).finally(() => {
    loading.value = false;
  });
}
function downloadClick(row) {
  fileDownload(row.id, props.id).then(res => {
    window.open(res.data.data.url);
  });
}
function handleSizeChange(val) {
  searchForm.page = 1;
  searchForm['page_size'] = val;
  networkPacketListFun();
}
function handleCurrentChange(val) {
  searchForm.page = val;
  networkPacketListFun();
}
function getRowClass({ row }) {
  if (!row.download_flag) {
    return "row-expand-cover";
  } else {
    return "";
  }
}
</script>
<style lang="scss" scoped>

  :deep .el-table .row-expand-cover .cell .el-table__expand-icon {
    display: none;
  }
  .buttonStyle{
  display:flex;
  align-items:center ;
}
  </style>
