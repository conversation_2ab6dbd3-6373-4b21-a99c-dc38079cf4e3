<template>
  <!-- 内存镜像视角 -->
  <div>
    <div
      v-if="!detailBool"
      class=""
    >
      <zr-row
        class="content-top"
        :gutter="24"
      >
        <zr-col
          :span="12"
          style="padding-right: 0px;"
        >
          <zr-card
            class="box-card"
          >
            <zr-row :gutter="24">
              <zr-col
                :span="8"
                class="ui-counter"
              >
                <span
                  class="title"
                  style="margin: 14px 0 9px 0"
                >总镜像文件数</span>
                <span class="counter">
                  <h2>{{ imageCount.all }}</h2>
                </span>
              </zr-col>
              <zr-col
                :span="8"
                class="ui-counter"
              >
                <span
                  class="system-icon-windows"
                  style="margin:3px 0 4px 0"
                >
                  <SystemIcon
                    name="windows"
                    class="title"
                    padding-left="10"
                    size="16"
                  />
                </span>
                <div class="counter">
                  <h2>{{ imageCount.windows }}</h2>
                  <span
                    v-if="imageCount.windows_percentage !==0"
                    class="percentageStyle"
                  > / {{ imageCount.windows_percentage }}
                    <span> % </span>
                  </span>
                </div>
              </zr-col>
              <zr-col
                :span="8"
                class="ui-counter"
              >
                <span class="system-icon-linux">
                  <SystemIcon
                    name="linux"
                    class="title"
                    padding-left="10"
                    size="16"
                  />
                </span>
                <span class="counter">
                  <h2>{{ imageCount.linux }}</h2>
                  <span
                    v-if="imageCount.linux_percentage !==0"
                    class="percentageStyle"
                  > / {{ imageCount.linux_percentage }}
                    <span> % </span>
                  </span>
                </span>
              </zr-col>
            </zr-row>
          </zr-card>
        </zr-col>
        <zr-col :span="12">
          <zr-card class="box-card">
            <zr-row
              :gutter="24"
            >
              <zr-col
                v-for="item in riskLevelOption"
                :key="item"
                :span="6"
                class="ui-counter"
              >
                <div
                  class="levelTitle"
                >
                  <span
                    class="level-icon"
                    :style="{ background: item.color}"
                  >
                    <Icon
                      :name="item.label=='安全' ? 'zr-safe-a' : 'zr-threaten-fill'"
                      color="#fff"
                      class="icon"
                      size="16"
                    />
                  </span>
                  <span class="title">  {{ item.label }}</span>
                </div>

                <span class="counter">
                  <h2>{{ riskLevelCount[item.count] }}</h2>
                  <span
                    v-if="riskLevelCount[item.percentage] !==0"
                    class="percentageStyle"
                  >/ {{ riskLevelCount[item.percentage] }}
                    <span> % </span>
                  </span>
                </span>
              </zr-col>
            </zr-row>
          </zr-card>
        </zr-col>
      </zr-row>
      <!-- 列表 -->
      <div
        class="main-content"
        style="margin-top:10px"
      >
        <zr-row
          :gutter="24"
          justify="space-between"
          style="white-space: nowrap"
        >
          <zr-col :span="8">
            <zr-button
              type="danger"
              size="default"
              plain
              :disabled="deleteList.length<=0"
              @click="batchDeleteClick"
            >批量删除</zr-button>
           
          </zr-col>
          <zr-col
            :span="16"
            style="text-align:right"
          >
            <zr-form :inline="true">
              <zr-form-item>
                <zr-select
                  v-model="searchForm.os"
                  size="default"
                  @change="searchClick"
                >
                  <zr-option
                    v-for="item in osOptions"
                    :key="item"
                    :label="item.key"
                    :value="item.value"
                  />
                </zr-select>
              </zr-form-item>
              <zr-form-item>
                <zr-select
                  v-model="searchForm.risk_level"
                  size="default"
                  @change="searchClick"
                >
                  <zr-option
                    v-for="item in riskLevelOptions"
                    :key="item"
                    :label="item.key"
                    :value="item.value"
                  />
                </zr-select>
              </zr-form-item>
              <zr-form-item class="nameInput">
                <zr-input
                  v-model="searchForm.name"
                  placeholder="主机名/文件名/威胁标签"
                  size="default"
                  @keyup.enter="searchClick"
                >
                  <template #append>
                    <zr-button
                      @click="searchClick"
                    >
                      <icon name="zr-search" />
                    </zr-button>
                  </template>
                </zr-input>
              </zr-form-item>
            </zr-form>

          </zr-col>
        </zr-row>
        <zr-table
          v-loading="loading"
          :data="tableData"
          row-id="id"
          :default-sort="{ prop: 'updated_at', order: 'descending' }"
          empty-text="暂无数据"
          @sort-change="sortChange"
          @expand-change="handleExpandChange"
          @selection-change="handleSelectionChange"
        >
          <zr-table-column
            type="expand"
            width="30"
          >
            <template
              #default="props"
            >
              <div class="detail-msg">
                <zr-card>
                  <zr-row
                    :gutter="24"
                    class="detail-test"
                  >
                    <zr-col :span="9">
                      <h3 class="title-pie">数据包基本信息</h3>
                      <p
                        v-for="item in basicMessage"
                        :key="item.value"
                        :label="item.label"
                      >
                        <span>{{ item.label }}：</span>
                        <span v-if="expandData">
                          <span v-if="item.value=='size'">{{ numDelivery(expandData[item.value]) }}</span>
                          <span v-else-if="item.value=='upload_method'">
                            {{ expandData[item.value] == 'manual' ? '手动上传' : '-' }}
                          </span>
                          <span v-else-if="item.value=='image_type'">
                            {{ expandData[item.value]=='mem-image' ? '内存镜像' : '-' }}
                          </span>
                          <span v-else>{{ expandData[item.value] || '-' }}</span>
                          <zr-button
                            v-if="item.copy && expandData[item.value]"
                            type="primary"
                            link
                            left-icon="zr-copy-file"
                            @click="copyClick(expandData[item.value] )"
                          />
                        </span>
                      </p>
                    </zr-col>
                    <zr-col :span="9">
                      <h3 class="title-pie">关联信息</h3>
                      <p
                        v-for="item in relatesMessage"
                        :key="item.value"
                        :label="item.label"
                      >
                        <span>{{ item.label }}：</span>
                        <span v-if="expandData">
                          <span v-if="item.value == 'ip'">
                            {{ expandData[item.value] && expandData[item.value].length>0 ? expandData[item.value].join(','): '-' }}
                          </span>
                          <span
                            v-else-if="item.value == 'os'"
                            :class="expandData[item.value]=='1inux' ? '' : 'system-icon-style'"
                          >
                            <SystemIcon :name="expandData[item.value]" />
                          </span>
                          <span v-else>{{ expandData[item.value] || '-' }}</span>
                        </span>
                      </p>
                    </zr-col>
                    <zr-col :span="6">
                      <h3 class="title-pie">执行信息</h3>
                      <p
                        v-for="item in executionMessage"
                        :key="item.value"
                        :label="item.label"
                      >
                        <span>{{ item.label }}：</span>
                        <span v-if="expandData[item.value] !== ''">
                          {{ timeFormat(expandData[item.value]) }}
                          <zr-tag v-if="expandData[item.agoValue]">{{ expandData[item.agoValue] }}</zr-tag>
                        </span>
                        <span v-else> - </span>
                      </p>
                    </zr-col>
                  </zr-row>
                  <div class="detail-test">
                    <p
                      v-for="item in otherMessage"
                      :key="item.value"
                      :label="item.label"
                    >
                      <span>{{ item.label }}：</span>
                      <span v-if="item.value=='risk_level'">
                        <span
                          class="top-tag"
                          :type="expandData[item.value]"
                        >
                          <span class="left-icon"><Icon :name="expandData[item.value]==='safe' ? 'zr-safe-a' : 'zr-threaten-fill'" /></span>
                          <span class="text">{{ riskLevelFun(expandData[item.value]) }}</span>
                        </span>
                      </span>
                      <span v-else-if="item.value == 'risk_tags' || item.value=='judgement_threat_tags'">
                        <span
                          v-if="expandData[item.value] && expandData[item.value].length>0"
                        >
                          <span
                            v-for="item in expandData[item.value]"
                            :key="item"
                            :class="'tag-custom ' + item.threat_level"
                          >{{ item.threat_tag }}</span>
                        </span>
                        <span v-else> - </span>
                      </span>
                      <span
                        v-else-if="item.value=='judgement_result'"
                        style="display: contents;"
                      >
                        <Icon
                          name="zr-trusted-configuration-b"
                          size="20"
                          :color="judgementResultColor(expandData[item.value])"
                        />
                        <span style="padding-left: 6px">{{ expandData[item.value] }}</span>
                      </span>
                      <span v-else>{{ expandData[item.value] || '-' }}</span>
                    </p>
                  </div>
                </zr-card>
              </div>
            </template>
          </zr-table-column>
          <zr-table-column
            type="selection"
            width="55"
          />
          <zr-table-column
            label="内存镜像文件名"
            prop="image_name"
          >
            <template #default="scope">
              <zr-tooltip
                v-if="scope.row.image_name"
                placement="top-start"
                :content="scope.row.image_name"
              >
                <p class="table-item-content">{{ scope.row.image_name }}</p>
              </zr-tooltip>
              <p v-else>-</p>
            </template>
          </zr-table-column>
          <zr-table-column
            label="操作系统"
            prop="os"
            width="120px"
            show-overflow-tooltip
          >
            <template #default="scope">
              <span
                v-if="scope.row.os"
                :class="scope.row.os=='linux' ?'':'system-icon-style'"
              >
                <SystemIcon :name="scope.row.os" />
              </span>
              <span v-else>-</span>
            </template>
          </zr-table-column>
          <!-- <zr-table-column
            label="关联主机信息"
            prop="host_name"
          >
            <template #default="scope">
              <span v-if="scope.row.host_name || scope.row.ip">
                <p>{{ scope.row.host_name }}</p>
                <zr-tooltip
                  v-if="scope.row.ip && scope.row.ip.length>0"
                  effect="dark"
                  :content="scope.row.ip.join(',')"
                  placement="top"
                >
                  <p class="table-item-content-one">{{ scope.row.ip.join(',') || '-' }}</p>
                </zr-tooltip>
              </span>
              <span v-else> - </span>
            </template>
          </zr-table-column>
          <zr-table-column
            label="关联任务"
            prop="task_name"
          >
            <template #default="scope">
              {{ scope.row.task_name || '-' }}
            </template>
          </zr-table-column> -->
          <zr-table-column
            label="所属用户"
            prop="user_name"
          >
            <template #default="scope">
              {{ scope.row.user_name || '-' }}
            </template>
          </zr-table-column>
          <zr-table-column
            label="威胁等级"
            prop="risk_level"
            width="100"
          >
            <template #default="scope">
              <span
                class="top-tag"
                :type="scope.row.risk_level"
              >
                <span class="left-icon"><Icon :name="scope.row.risk_level==='safe' ? 'zr-safe-a' : 'zr-threaten-fill'" /></span>
                <span class="text">{{ riskLevelFun(scope.row.risk_level) }}</span>
              </span>
            </template>
          </zr-table-column>
          <zr-table-column
            label="威胁标签"
            prop="risk_tags"
          >
            <template #default="scope">
              <zr-scrollbar
                v-if="scope.row.risk_tags"
                :wrap-class="['scrollbar-wrapper', 'wrapper-custom']"
                :wrap-style="[{ 'max-height': '50px', height: '50px'}]"
              >
                <p
                  v-for="item in scope.row.risk_tags"
                  :key="item"
                  :class="'tag-custom ' + item.threat_level"
                >{{ item.threat_tag }}</p>
              </zr-scrollbar>
              <p v-else>-</p>
            </template>
          </zr-table-column>
          <zr-table-column
            label="更新时间"
            prop="updated_at"
            width="180px"
            sortable
          >
            <template #default="scope">
              <div v-if="scope.row.updated_at || scope.row.updated_time_ago">
                <zr-tag v-if="scope.row.updated_time_ago">{{ scope.row.updated_time_ago }}</zr-tag>
                <p>{{ timeFormat(scope.row.updated_at) }}</p>
              </div>
              <div v-else>-</div>
            </template>
          </zr-table-column>
          <!-- <zr-table-column
            label="研判状态"
            prop="judgement_result"
            width="100px"
          >
            <template #default="scope">
              <span style="display: flex;">
                <Icon
                  v-if="scope.row.judgement_result"
                  name="zr-trusted-configuration-b"
                  size="20"
                  :color="judgementResultColor(scope.row.judgement_result)"
                />
                <span style="padding-left: 6px;">{{ scope.row.judgement_result || '-' }}</span>
              </span>
            </template>
          </zr-table-column> -->
          <zr-table-column
            label="操作"
            width="150px"
          >
            <template #default="scope">
              <zr-button
                type="primary"
                link
                @click="detailClick(scope.row)"
              >查看报告</zr-button>
              <!-- <zr-button
                type="primary"
                link
                @click="judgeMentClick(scope.row)"
              >
                {{ scope.row.judgement_completed? '取消研判' : ' 完成研判' }}</zr-button> -->
              <zr-button
                type="primary"
                link
                @click="deleteClick(scope.row)"
              >删除</zr-button>
            </template>
          </zr-table-column>
        </zr-table>
        <zr-pagination
          v-if="total>0"
          :current-page="searchForm.page"
          :page-size="searchForm.limit"
          :page-sizes="[20, 40, 60, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { timeFormat } from '@/utils/formatting';
import { getCount, getimagesList, deleteImages, batchDeleteImages, getOptions, judgement, getRisklevelCount } from '@/api/threat/mirrorAngle';
import { SystemIcon, ZrMessageBox, ZrMessage } from 'qianji-ui';

const { proxy } = getCurrentInstance();
const imageCount = ref({});
const osOptions = ref({});
const riskLevelOptions = ref([]);
const judgementStatusOptions = ref([]);
const searchForm = ref({
  os: '',
  name: '',
  'risk_level': '',
  sort: '-updated_at',
  page: 1,
  limit: 20
});
const detailBool = ref(false);
const loading = ref(false);
const total = ref(0);
const deleteList = ref([]);
const tableData = ref([]);
const expandData = ref({});
const riskLevelCount = ref({});

const riskLevelOption = [ // 威胁等级统计结果
  {
    label: '高危',
    color: '#fd4747',
    count: 'high',
    percentage: 'high_percentage'
  },
  {
    label: '中危',
    color: '#ff9900',
    count: 'medium',
    percentage: 'medium_percentage'
  },
  {
    label: '低危',
    color: '#909399',
    count: 'low',
    percentage: 'low_percentage'
  },
  {
    label: '安全',
    color: '#07b630',
    count: 'safe',
    percentage: 'safe_percentage'
  }
];
const basicMessage = [ // 数据包基本信息
  {
    label: '文件名',
    value: 'image_name',
    copy: true
  },
  {
    label: '数据包类型',
    value: 'image_type'
  },
  {
    label: '大小（GB）',
    value: 'size'
  },
  {
    label: '上传方式',
    value: 'upload_method'
  },
  {
    label: '校验和（Etag）',
    value: 'etag',
    copy: true
  }
];
const relatesMessage = [ // 关联信息
  {
    label: '关联主机名',
    value: 'host_name'
  },
  {
    label: '关联主机IP',
    value: 'ip'
  },
  {
    label: '操作系统',
    value: 'os'
  },
  {
    label: '所属任务',
    value: 'task_name'
  },
  {
    label: '所属用户',
    value: 'user'
  }
];
const executionMessage = [ // 执行信息
  {
    label: '上传完成时间',
    value: 'upload_complete_time',
    agoValue: 'upload_complete_time_ago'
  },
  {
    label: '检测开始时间',
    value: 'detection_start_time',
    agoValue: 'detection_start_time_ago'
  },
  {
    label: '检测完成时间',
    value: 'detection_complete_time',
    agoValue: 'detection_complete_time_ago'
  }
  // {
  //   label: '研判完成时间',
  //   value: 'judgement_completed_time',
  //   agoValue: 'judgement_completed_time_ago'
  // }
];
const otherMessage = [
  {
    label: '威胁等级',
    value: 'risk_level'
  },
  {
    label: '威胁标签',
    value: 'risk_tags'
  }
  // {
  //   label: '研判状态',
  //   value: 'judgement_result'
  // },
  // {
  //   label: '研判人员',
  //   value: 'judgement_operator'
  // },
  // {
  //   label: '研判威胁标签',
  //   value: 'judgement_threat_tags'
  // }
];

function judgementResultColor(row) { // 研判状态颜色
  if (row == '威胁') return '#fd4747';
  if (row == '可疑') return '#ff9900';
  if (row == '安全') return '#07b630';
  if (row == '未研判') return '#909399';
}

getCountFun();
function getCountFun() { // 统计
  getCount().then(res => {
    imageCount.value = res.data.data.ImageCount;
  });
}

getRisklevelCountFun();
function getRisklevelCountFun() { // 威胁等级统计
  getRisklevelCount().then(res => {
    riskLevelCount.value = res.data.data.risk_level_count;
  });
}

getOptionsFun();
function getOptionsFun() {
  getOptions().then(res => { // 操作系统下拉内容
    if (res.data.code == 0) {
      osOptions.value = res.data.data.os;
      riskLevelOptions.value = res.data.data.risk_level_options;
      judgementStatusOptions.value = res.data.data.judgement_status_options;
    } else {
      osOptions.value = [];
      riskLevelOptions.value = [];
      judgementStatusOptions.value = [];
    }
  });
}

getImagesListFun();
function getImagesListFun() {
  loading.value = true;
  getimagesList(searchForm.value).then(res => { // 列表接口
    if (res.data.code == 0) {
      tableData.value = res.data.data.list;
      total.value = res.data.data.total;
      // if (total.value == 0) {
      //   searchForm.value['judgement_completed'] = '';
      //   getImagesListFun();
      // }
    }
  }).finally(() => {
    loading.value = false;
  });
}

function searchClick() { // 搜索
  getImagesListFun();
}
function detailClick(row) { // 详情
  window.open('/mfp/mirror-angle/' + row.detection_id);
}

function judgeMentClick(row) { // 研判
  judgement(row.record_id, { 'judgement_completed': !row.judgement_completed }).then(res => {
    if (res.data.code == 0) {
      ZrMessage.success(row.judgement_completed ? '取消研判成功' : '完成研判成功');
      getImagesListFun();
      getRisklevelCountFun();
      getJudgementCountFun();
      getOptionsFun();
    } else {
      ZrMessage.error(res.data.msg);
    }
  });
}

function deleteClick(row) { // 删除
  ZrMessageBox.confirm('确认删除' + row.image_name + '任务?', '确认操作', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      deleteImages(row.record_id).then(res => { // 删除接口
        if (res.data.code == 0) {
          ZrMessage.success('删除成功');
          getImagesListFun();
          getCountFun();
          getOptionsFun();
          getRisklevelCountFun();
          getJudgementCountFun();
        } else {
          ZrMessage.error(res.data.msg);
        }
      });
    }).catch(() => {
    
    });
}

function handleSelectionChange(val) {
  deleteList.value = val.map(item => item.record_id);
}

function handleExpandChange(row) {
  expandData.value = row;
}


function copyClick(text) { // 复制
  proxy.copyFun(text);
}

function batchDeleteClick() { // 批量删除
  ZrMessageBox.confirm('确认删除勾选的所有选项?', '确认操作', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    batchDeleteImages({ 'record_ids': deleteList.value }).then(res => { // 批量删除接口
      if (res.data.code == 0) {
        ZrMessage.success('删除成功');
        getImagesListFun();
        getCountFun();
        getOptionsFun();
      } else {
        ZrMessage.error(res.data.msg);
      }
    }).catch(() => {
    });
  });
}
function riskLevelFun(level) {
  switch (level) {
    case 'critical':
      return '致命';
    case 'high':
      return '高';
    case 'medium':
      return '中';
    case 'low':
      return '低';
    case 'information':
      return '提示';
    case 'safe':
      return '安全';
    default:
      return '';
  }
}
function handleSizeChange(val) {
  searchForm.value.limit = val;
  searchForm.value.page = 1;
  getImagesListFun();
}
function handleCurrentChange(val) {
  searchForm.value.page = val;
  getImagesListFun();
}

function sortChange(data) { // 更新时间 排序
  const { prop, order } = data;
  if (order === 'ascending') {
    searchForm.value.sort = '+' + prop;
  } else {
    searchForm.value.sort = '-' + prop;
  }
  getImagesListFun();
}

function numDelivery(num) { // 从B转化为GB
  var size = num / 1024 / 1024 / 1024;
  var result = parseFloat(size);
  if (isNaN(result)) {
    return false;
  }
  result = Math.round(size * 100) / 100;
  return result;
}
</script>

<style lang="scss" scoped>
:deep(.content-top .el-card__body){
  padding: 10px !important;
}
.ui-counter{
  text-align: center;
  display: flex;
  align-items: center;
  flex-direction: column;
  padding: 0;
  white-space:nowrap;
  .title{
    font-size: 16px;
    margin: 8px;
  }
  .counter{
    font-size: 16px;
    font-weight: bold;
    display: flex;
    align-items: baseline;
  }
  .percentageStyle{
    color: #919398;
    margin-left: 5px
  }
  .system-icon-linux{
    :deep(.svg-icon){
    width: 32px;
    height: 32px;
  }
  :deep(.icon-box.title){
      margin-bottom: 7px;
  }

  }
}
.levelTitle{
    display: flex;
    text-align: center;
    align-items:center;
    margin: 3px 0 4px;
    .level-icon{
      color: white;
      font-size: 12px;
      display: inline-block;
      width: 24px;
      height: 24px;
      line-height:29px;
      border-radius: 2px;
    }
  }
.detail-test{
  line-height: 24px;
  margin-bottom: 20px;
  h3{
    margin: 12px 0 8px;
    font-size: 14px;
  }
  p{
    display: flex;
    align-items: center;
    font-size: 14px;
    line-height: 30px;
  }
}
.nameInput{
  :deep(.el-input__wrapper){
    width:250px
  }
}
</style>
  
