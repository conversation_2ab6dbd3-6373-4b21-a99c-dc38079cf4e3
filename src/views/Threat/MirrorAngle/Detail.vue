<template>
  <div class="detail">
    <div
      class="main-content basic-content"
    >
      <div class="basic">
        <h2>文件名：{{ basicInfo.image_name }}</h2>
        <zr-row :gutter="24">
          <zr-col
            v-for="(item,index) in basicOption"
            :key="item.label"
            :span="8"
          >
            <!-- :span="(index+1)%3==2?10:7" -->
            <p v-if="!item.hide">{{ item.label }}：</p>
            <p v-if="!item.hide">
              <span v-if="item.key==='upload_finished_at'||item.key==='completed_time'">{{ timeFormat(basicInfo[item.key]) }} <zr-tag
                v-if="basicInfo[item.key+'_ago']"
                style="height: 20px;"
              >{{ basicInfo[item.key+'_ago'] }}</zr-tag></span>
              <span v-else-if="item.key === 'judgement_completed_time'">{{ basicInfo[item.key] || '-' }}
                <zr-tag
                  v-if="basicInfo[item.key+'_ago']"
                  style="height: 20px;"
                >{{ basicInfo[item.key+'_ago'] }}</zr-tag>
              </span>
              <span v-else-if="item.key==='host_name'">
                {{ basicInfo[item.key]||'-' }}
                <span
                  v-if="basicInfo.ip&&basicInfo.ip.length>0"
                  class="line-clamp"
                >
                  <zr-tooltip
                    class="box-item"
                    effect="dark"
                    :content="basicInfo.ip.join(',')"
                    placement="bottom-start"
                  >
                    ({{ basicInfo.ip.join(',') }})
                  </zr-tooltip>
                </span>
              </span>
              <span v-else-if="item.key==='size'">{{ numDelivery(basicInfo[item.key]) }}</span>
              <span
                v-else-if="item.key==='os'"
                :class="basicInfo[item.key]==='linux' ? '' : 'system-icon-style'"
              >
                <SystemIcon
                  :name="basicInfo[item.key]"
                />
              </span>
              <span v-else>{{ basicInfo[item.key]||'-' }}</span>
              <zr-button
                v-if="item.key==='etag'"
                type="primary"
                link
                left-icon="zr-copy-file"
                @click="copyClick(basicInfo[item.key])"
              />
            </p>
          </zr-col>
        </zr-row>
       
      </div>
    </div>
    <div
      class="main-content"
      style="margin: 12px 12px 0;min-height:723px"
    >
      <TabPane
        id="weight"
        :option="tabOption"
        class="detail-tab"
        @handleClick="handleClick"
      />
      <div v-if="os==='Windows'">
        <!-- 威胁检测 -->
        <ThreatDetection
          v-if="activeName==='1'"
          :id="id"
          :os="os"
          :analysis-id="analysisId"
          :detail-data="basicInfo"
          @initFun="initFun"
        />
        <!-- 进程 -->
        <CourseWindows
          v-if="activeName==='2'"
          :id="analysisId"
          :detail-data="basicInfo"
        />
        <!-- 注册表 -->
        <Regedit
          v-if="activeName==='3'"
          :id="analysisId"
        />
        <!-- 文件 -->
        <File
          v-if="activeName==='4'"
          :id="analysisId"
        />
        <!-- 历史命令 -->
        <HistoryCommand
          v-if="activeName==='5'"
          :id="analysisId"
          :detail-data="basicInfo"
        />
        <!-- 驱动模块 -->
        <DriverModule
          v-if="activeName==='6'"
          :id="analysisId"
        />
        <!-- 服务SID -->
        <ServiceSID
          v-if="activeName==='7'"
          :id="analysisId"
        />
        <!-- IE历史 -->
        <IE
          v-if="activeName==='8'"
          :id="analysisId"
          :detail-data="basicInfo"
        />
        <!-- 内核函数 -->
        <ComputeKernel
          v-if="activeName==='9'"
          :id="analysisId"
          :detail-data="basicInfo"
        />
        <!-- 未加载模块 -->
        <UnloadedModule
          v-if="activeName==='10'"
          :id="analysisId"
          :detail-data="basicInfo"
        />
        <!-- 符号链接 -->
        <SymbolicLink
          v-if="activeName==='11'"
          :id="analysisId"
          :detail-data="basicInfo"
        />
        <!-- 密钥 -->
        <SecretKey
          v-if="activeName==='12'"
          :id="analysisId"
          :detail-data="basicInfo"
        />
        <!-- 自定义插件 -->
        <CustomPluginWindows
          v-if="activeName==='15'"
          :id="analysisId"
        />
        <!-- 全局检索 -->
        <GlobalSearch
          v-if="activeName==='13'"
          :id="analysisId"
          :detail-data="basicInfo"
        />
        <!-- 分析报告 -->
        <Analysis
          v-if="activeName==='14'"
          :id="id"
          :analysis-id="analysisId"
        />
      </div>
      <div v-if="os==='Linux'">
        <!-- 威胁检测 -->
        <ThreatDetection
          v-if="activeName==='1'"
          :id="id"
          :os="os"
          :analysis-id="analysisId"
          :detail-data="basicInfo"
          @initFun="initFun"
        />
        <!-- 进程 -->
        <CourseLinux
          v-if="activeName==='2'"
          :id="analysisId"
        />
        <!-- 临时文件系统 -->
        <ScratchFile
          v-if="activeName==='3'"
          :id="analysisId"
          :detail-data="basicInfo"
        />
        <!-- 内核模块 -->
        <Steinkern
          v-if="activeName==='4'"
          :id="analysisId"
          :detail-data="basicInfo"
        />
        <!-- 网卡信息 -->
        <NetworkCard
          v-if="activeName==='5'"
          :id="analysisId"
          :detail-data="basicInfo"
        />
        <!-- 路由缓存 -->
        <Route
          v-if="activeName==='6'"
          :id="analysisId"
          :detail-data="basicInfo"
        />
        <!-- ARP表 -->
        <Arp
          v-if="activeName==='7'"
          :id="analysisId"
          :detail-data="basicInfo"
        />
        <!-- 数据包队列 -->
        <DataPacket
          v-if="activeName==='8'"
          :id="analysisId"
          :detail-data="basicInfo"
        />
        <!-- 高速缓存 -->
        <NetworkPacket
          v-if="activeName==='9'"
          :id="analysisId"
          :detail-data="basicInfo"
        />
        <!-- 自定义插件 -->
        <CustomPluginLinux
          v-if="activeName==='12'"
          :id="analysisId"
        />
        <!-- 全局检索 -->
        <GlobalSearch
          v-if="activeName==='10'"
          :id="analysisId"
          :detail-data="basicInfo"
        />
        <!-- 分析报告 -->
        <Analysis
          v-if="activeName==='11'"
          :id="id"
          :analysis-id="analysisId"
        />
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, getCurrentInstance, onMounted } from 'vue';
import { basicInfoList } from '@/api/threat/mirrorAngle/index';
import TabPane from '@/components/common/TabPane.vue';
import { timeFormat } from '@/utils/formatting';
import GlobalSearch from './GlobalSearch.vue';
import ThreatDetection from './ThreatDetection.vue';
import Analysis from './Analysis.vue';
// Windows
import CourseWindows from './Windows/CourseWindows.vue';
import Regedit from './Windows/Regedit.vue';
import File from './Windows/File.vue';
import HistoryCommand from './Windows/HistoryCommand.vue';
import DriverModule from './Windows/DriverModule.vue';
import ServiceSID from './Windows/ServiceSID.vue';
import IE from './Windows/IE.vue';
import ComputeKernel from './Windows/ComputeKernel.vue';
import UnloadedModule from './Windows/UnloadedModule.vue';
import SymbolicLink from './Windows/SymbolicLink.vue';
import SecretKey from './Windows/SecretKey.vue';
import CustomPluginWindows from './Windows/CustomPluginWindows.vue';

// Linux
import CourseLinux from './Linux/CourseLinux.vue';
import ScratchFile from './Linux/ScratchFile.vue';
import Steinkern from './Linux/Steinkern.vue';
import NetworkCard from './Linux/NetworkCard.vue';
import Route from './Linux/Route.vue';
import Arp from './Linux/Arp.vue';
import DataPacket from './Linux/DataPacket.vue';
import NetworkPacket from './Linux/NetworkPacket.vue';
import CustomPluginLinux from './Linux/CustomPluginLinux.vue';

const { proxy } = getCurrentInstance();
const os = ref('');
// const os = ref('Windows');
const activeName = ref('1');
const tabOption = ref([]);
const tabWindowsOption = [
  {
    label: '威胁检测',
    name: '1'
  },
  {
    label: '进程',
    name: '2'
  },
  {
    label: '注册表',
    name: '3'
  },
  {
    label: '文件',
    name: '4'
  },
  {
    label: '历史命令',
    name: '5'
  },
  {
    label: '驱动模块',
    name: '6'
  },
  {
    label: '服务SID',
    name: '7'
  },
  {
    label: 'IE历史',
    name: '8'
  },
  {
    label: '内核函数',
    name: '9'
  },
  {
    label: '未加载模块',
    name: '10'
  },
  {
    label: '符号链接',
    name: '11'
  },
  {
    label: '密钥',
    name: '12'
  },
  {
    label: '自定义插件',
    name: '15'
  },
  {
    label: '全局检索',
    name: '13'
  },
  {
    label: '分析报告',
    name: '14'
  }
  
];
const tabLinuxOption = [
  {
    label: '威胁检测',
    name: '1'
  },
  {
    label: '进程',
    name: '2'
  },
  {
    label: '临时文件系统',
    name: '3'
  },
  {
    label: '内核模块',
    name: '4'
  },
  {
    label: '网卡信息',
    name: '5'
  },
  {
    label: '路由缓存',
    name: '6'
  },
  {
    label: 'ARP表',
    name: '7'
  },
  {
    label: '数据包队列',
    name: '8'
  },
  {
    label: '高速缓存',
    name: '9'
  },
  {
    label: '自定义插件',
    name: '12'
  },
  {
    label: '全局检索',
    name: '10'
  },
  {
    label: '分析报告',
    name: '11'
  }

];
const basicOption = [
  {
    label: '数据包类型',
    key: 'type'
  },
  {
    label: '操作系统',
    key: 'os'
  },
  {
    label: '上传完成时间',
    key: 'upload_finished_at'
  },
  {
    label: '数据包大小 (GB)',
    key: 'size'
  },
  {
    label: '所属任务',
    key: 'task_name'
  },
  {
    label: '检测完成时间',
    key: 'completed_time'
  },
  {
    label: '校验和 (Etag)',
    key: 'etag'
  },
  {
    label: '所属用户',
    key: 'user_name'
  },
  // {
  //   label: '研判完成时间',
  //   key: 'judgement_completed_time'
  // },
  // {
  //   label: 'Hash值（MD5）',
  //   key: 'MD5'
  // },
  {
    label: '关联主机名',
    key: 'host_name'
  },
  {
    label: '内存分析引擎',
    key: 'volatility_version'
  }
];
const id = ref('');
const basicInfo = ref({});
const analysisId = ref('');
onMounted(() => {
  const path = window.location.pathname;
  id.value = path.split('/')[path.split('/').length - 1];
  basicInfoListFun();
});

function basicInfoListFun() {
  basicInfoList(id.value).then(res => {
    analysisId.value = res.data.data.analysis_id;
    basicInfo.value = res.data.data;
    console.log(basicInfo.value, 'basciv');
    os.value = res.data.data.os == 'windows' ? 'Windows' : 'Linux';
    tabOption.value = res.data.data.os === 'windows' ? tabWindowsOption : tabLinuxOption;
  }).finally(() => {
  });
}
function initFun() {
  basicInfoListFun();
}
function handleClick(val) {
  activeName.value = val;
}
function copyClick(text) {
  proxy.copyFun(text);
}

function numDelivery(num) {
  var size = num / 1024 / 1024 / 1024;
  var result = parseFloat(size);
  if (isNaN(result)) {
    return false;
  }
  result = Math.round(size * 100) / 100;
  return result;
}
</script>
<style lang="scss" scoped>
  .detail{
    background: #eee;
    padding-bottom: 12px;
  }
  .basic-content{
    background: #0A1936;
    background: linear-gradient(to bottom, #0C3361, #071E38);
    color: #fff;
  }
.basic{
    h2{
        font-size: 18px;
        margin-bottom: 12px;
    }
    .el-col{
        font-size: 14px;
        display: flex;
        align-items: center;
        line-height: 30px;
    }
    p{
        display: flex;
        align-items: center;
        span{
            margin-right: 8px;
        }
    }
}
.line-clamp {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
