<template>
  <div>
    <!-- 添加标记弹窗 -->
    <zr-dialog
      v-model="markDialogVisible"
      title="添加标记"
      :width="480"
      destroy-on-close
      :before-close="cancleDialog"
    >
      <span>
        <zr-input
          v-model="inputMark"
          maxlength="10"
          show-word-limit
          placeholder="请输入标记，一次仅添加一个标记，最大长度为10个字符"
        />
      </span>
      <template #footer>
        <span class="dialog-footer">
          <zr-button @click="cancleDialog">取消</zr-button>
          <zr-button
            type="primary"
            @click="submitAddMark"
          >提交</zr-button>
        </span>
      </template>
    </zr-dialog>
  </div>
</template>
<script setup>
import { ref, defineProps, watch, defineEmits } from 'vue';
import { addMark } from '@/api/threat/mirrorAngle/windows';
import { ZrMessage } from 'qianji-ui';

const emits = defineEmits(['submitAddMark', 'openMarkDialog']);

const props = defineProps({
  isOpenDialog: {
    type: Boolean
  },
  analysisId: {
    type: String
  }
});
const markDialogVisible = ref(false);
const inputMark = ref('');

watch(() => props.isOpenDialog, (newVal) => {
  markDialogVisible.value = newVal;
});

function submitAddMark() { // 提交添加的标记
  if (inputMark.value == '') {
    ZrMessage.error('请输入标记名称');
    return;
  }
  addMark(props.analysisId, { 'mark_name': inputMark.value }).then(res => {
    ZrMessage.success('添加标记成功');
    emits('openMarkDialog', false);
    inputMark.value = '';
  });
}

function cancleDialog() { // 取消弹窗
  emits('openMarkDialog', false);
  inputMark.value = '';
}
</script>
