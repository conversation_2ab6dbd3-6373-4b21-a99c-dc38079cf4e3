<template>
  <!-- 报告检索 -->
  <div>
    <div class="search-box">
      <zr-input
        v-model="form.keyword"
        :placeholder="detailData.os==='windows'?'请输入关键字、如文件名、注册表项、IP、HASH值(MD5、SHA1、SHA256)等':'请输入关键字、如文件名、注册表项、IP、HASH值(MD5、SHA1、SHA256)等'"
        size="large"
        clearable
        @keyup.enter="searchClick"
      />
      <zr-button
        type="primary"
        size="large"
        left-icon="zr-search"
        @click="searchClick"
      >检索</zr-button>
    </div>
    <div
      v-if="searchBool"
      v-loading="loading"
      class="search-content"
    >
      <TabPane
        :option="detailData.os==='windows' ? windowsTab : linuxTab"
        class="detail-tab weight"
        @handleClick="handleClick"
      />
      <div
        v-if="reportList&&reportList.length>0"
        class="search-msg"
      >
        <div
          v-for="item in reportList"
          :key="Object.keys(item)"
          class="msg-content"
        >
          <h2><Icon
            name="zr-none-file"
            size="14"
          />{{ Object.keys(item)[0] }}</h2>
          <div class="particular-msg">
            <p
              v-for="(value,key) in item[Object.keys(item)[0]]"
              :key="key"
            >
              <span>{{ key }}：</span>
              <span>{{ value||value===0?value: '-' }}</span>
            </p>
            <div>
              <p>
                <span>所属镜像文件：</span>
                <span>{{ detailData.image_name||'-' }}</span>
              </p>
              <p>
                <span>关联主机名：</span>
                <span>{{ detailData.host_name||'-' }}</span>
              </p>
            </div>
          </div>
          <div class="operate-btn">
            <zr-button
              type="primary"
              link
              @click="detailClick(item[Object.keys(item)[0]])"
            >详情</zr-button>
            <zr-button
              type="primary"
              link
              :disabled="!item[Object.keys(item)[0]].id"
              @click="downloadClick(item[Object.keys(item)[0]].id)"
            >下载</zr-button>
          </div>
        </div>
      </div>
      <template v-else>
        <zr-empty description="暂无数据" />
      </template>
      <zr-pagination
        v-if="total>0"
        :current-page="form.page"
        :page-size="form.page_size"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <zr-drawer
      v-model="drawer"
      title="详情信息"
    >
      <template #default>
        <div class="drawer-msg">
          <table>
            <tr
              v-for="(value,key) in detailObj"
              :key="key"
            >
              <td>{{ key }}</td>
              <td>{{ value||'-' }}</td>
            </tr>
          </table>
        </div>
      </template>
      <template #footer>
        <div style="flex: auto">
          <zr-button @click="drawer=false">关闭</zr-button>
        </div>
      </template>
    </zr-drawer>
  </div>
</template>
<script setup>
import { ref, defineProps } from 'vue';
import { searchReportWindows } from '@/api/threat/mirrorAngle/windows';
import { searchReportLinux } from '@/api/threat/mirrorAngle/linux';
import { fileDownload } from '@/api/threat/mirrorAngle/index';
import TabPane from '@/components/common/TabPane.vue';
  
const props = defineProps({
  id: String,
  detailData: Object
});
console.log(props.detailData);
const drawer = ref(false);
const total = ref(0);
const form = ref({
  keyword: '',
  type: 'ps-sockstat',
  page: 1,
  'page_size': 10
});
const activeName = ref('');
const detailObj = ref({});
const searchBool = ref(false);
const loading = ref(false);
const windowsTab = [
  {
    label: '全部',
    name: '1'
  },
  {
    label: '进程',
    name: 'ps-base'
  },
  {
    label: '动态链接库',
    name: 'ps-dll'
  },
  {
    label: '安全标识符',
    name: 'ps-sid'
  },
  {
    label: '句柄',
    name: 'ps-handle'
  },
  {
    label: '环境变量',
    name: 'ps-envars'
  },
  {
    label: '网络连接',
    name: 'ps-netscan'
  },
  {
    label: '注册表文件',
    name: 'registry-file'
  },
  {
    label: '注册表内容',
    name: 'registry-content'
  },
  {
    label: '历史命令',
    name: 'command'
  },
  {
    label: '驱动模块',
    name: 'driver-module'
  },
  {
    label: '服务SID',
    name: 'sid'
  },
  {
    label: 'IE历史',
    name: 'ie'
  }
];
const linuxTab = [
  {
    label: '全部',
    name: '1'
  },
  {
    label: '进程',
    name: 'ps-basic'
  },
  {
    label: '动态链接库',
    name: 'ps-elfs'
  },
  {
    label: '环境变量',
    name: 'ps-envvars'
  },
  {
    label: '网络连接',
    name: 'ps-sockstat'
  }
];
const reportList = ref([]);
function searchReportFun() {
  searchBool.value = true;
  loading.value = true;
  form.value.type = activeName.value;

  const searchReport = (searchFunction, os) => {
    searchFunction(form.value, props.id).then(res => {
      reportList.value = res.data.data.list;
      total.value = res.data.data.total;
    }).finally(() => {
      loading.value = false;
    });
  };

  if (props.detailData.os === 'windows') {
    searchReport(searchReportWindows, 'windows');
  } else {
    searchReport(searchReportLinux, 'linux');
  }
}
function searchClick() {
  form.value['page_size'] = 10;
  form.value.page = 1;
  searchReportFun();
}
function detailClick(data) {
  detailObj.value = data;
  drawer.value = true;
}
function downloadClick(id) {
  fileDownload(id, props.id).then(res => {
    location.href = res.data.data.url;
  });
}
function handleClick(val) {
  form.value['page_size'] = 10;
  form.value.page = 1;
  activeName.value = val === '1' ? '' : val;
  searchReportFun();
}

function handleSizeChange(val) {
  form.value['page_size'] = val;
  form.value.page = 1;
  searchReportFun();
}
function handleCurrentChange(val) {
  form.value.page = val;
  searchReportFun();
}
</script>
  <style lang="scss" scoped>
  .search-box{
    display: flex;
    width: 60%;
    margin: 20px auto 20px;
  }
  :deep(.detail-tab .el-tabs__item){
  font-weight: 500;
}
.search-content{
  padding: 0 40px;
}
.search-msg{
  .msg-content{
    border-bottom: 1px solid #ddd;
    line-height: 32px;
    padding: 12px 0;
    position: relative;
    display: flex;
    h2{
      font-size: 14px;
      color: rgb(0,191,191);
      width: 88px;
    }
    .particular-msg{
      flex: 1;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      padding-right: 140px;
      font-size: 14px;
      p{
        display: inline-block;
        margin-left: 16px;
        >span:nth-child(1){
          font-weight: 600;
        }
      }
    }
    .operate-btn{
      position: absolute;
      top: 10px;
      right: 20px;
    }
  }
}
.drawer-msg{
  width: 100%;
  table{
    border-right: 1px solid #ebeef5;
    border-bottom: 1px solid #ebeef5;
    /* 设置边缘间距0 */
    border-spacing: 0;
    /* 用于表格属性, 表示表格的两边框合并为一条 */
    border-collapse: collapse;
    width: 100%;
    tr{
      display: flex;
      td{
        border-left: 1px solid #ebeef5;
        border-top: 1px solid #ebeef5;
        line-break: anywhere;
        line-height: 23px;
        padding: 8px;
        font-size: 14px;
      }
      td:nth-child(1){
        width: 100px;
        text-align: center;
        background: #f5f7fa;
        color: #606266;
        font-weight: 700;
      }
      td:nth-child(2){
        flex: 1;
        color: #303133;

      }
    }
  }
}
  </style>
