<template>
  <div class="main-content">
    <tab-pane
      :option="tabOption"
      @handleClick="handleClick"
    />
    <SearchEngines v-if="tabName=='1'" />
  </div>
</template>
<script setup>
import TabPane from '@/components/common/TabPane.vue';
import SearchEngines from './SearchEngines/index.vue';
    
const tabName = ref('1');
const tabOption = [
  {
    label: '搜索引擎',
    name: '1'
  }
];
    
function handleClick(name) { // tab切换
  tabName.value = name;
}
</script>
    
  
