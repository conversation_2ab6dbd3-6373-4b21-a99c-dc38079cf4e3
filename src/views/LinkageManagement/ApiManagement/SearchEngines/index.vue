<template>
  <div class="">
    <zr-row
      :gutter="24"
      justify="space-between"
      style="white-space: nowrap"
    >
      <zr-col :span="8">
        <zr-button
          type="primary"
          size="default"
          @click="openDrawerClick('add')"
        >添加搜索引擎</zr-button>
      </zr-col>
      <zr-col
        :span="16"
        style="text-align:right"
      >
        <zr-form :inline="true">
          <zr-form-item>
            <zr-input
              v-model="searchForm.name"
              placeholder="搜索引擎名称"
              size="default"
              @keydown.enter.prevent="searchClick"
            >
              <template #append>
                <zr-button
                  @click="searchClick"
                >
                  <icon name="zr-search" />
                </zr-button>
              </template>
            </zr-input>
          </zr-form-item>
        </zr-form>
      </zr-col>
    </zr-row>
    <zr-table
      v-loading="loading"
      :data="tableData"
      empty-text="暂无数据"
    >
      <zr-table-column
        label="搜索引擎"
        prop="search_engine"
        width="250px"
      >
        <template #default="scope">
          <p class="image-container">
            <img
              class="custom-image"
              :src="`data:image/png;base64,${scope.row.image}`"
            >
            {{ scope.row.search_engine }}
          </p>
        </template>
      </zr-table-column>
      <zr-table-column
        label="地址"
        prop="ip"
        width="350px"
      />
      <zr-table-column
        label="备注"
        prop="description"
      >
        <template #default="scope">
          <zr-tooltip
            v-if="scope.row.description"
            placement="top-start"
            :content="scope.row.description"
          >
            <p class="table-item-content">{{ scope.row.description }}</p>
          </zr-tooltip>
          <p v-else>-</p>
        </template>
      </zr-table-column>
      <zr-table-column
        label="启用状态"
        prop="switch"
        width="120px"
      >
        <template #header>
          <div style="display: flex; align-items: center;">启用状态
            <zr-tooltip
              class="box-item"
              effect="dark"
              content="最多可同时启用两个搜索引擎"
              placement="top-start"
            >
              <zr-icon
                style="margin-left: 4px;"
                name="InfoFilled"
                size="15"
              />
            </zr-tooltip>
          </div>
        </template>
        <template #default="scope">
          <zr-switch
            v-model="scope.row.switch"
            :disabled="openTotal >= 2 && !scope.row.switch"
            @change="switchChange(scope.row)"
          />
        </template>
      </zr-table-column>
      <zr-table-column
        label="操作"
        prop=""
        width="120px"
      >
        <template #default="scope">
          <zr-button
            type="primary"
            link
            @click="openDrawerClick('edit', scope.row)"
          >编辑</zr-button>
          <zr-button
            type="primary"
            link
            @click="deleteClick(scope.row)"
          >删除</zr-button>
        </template>
      </zr-table-column>
    </zr-table>
    <zr-pagination
      v-if="total>0"
      :current-page="searchForm.page"
      :page-size="searchForm.page_size"
      :page-sizes="[20, 40, 60, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
    <!-- 弹窗 -->
    <drawer
      v-model="openDrawer"
      :title="drawerTitle"
      :drawer-data="drawerData"
      @saveClick="saveClick"
      @cancelClick="cancelClick"
    />
  </div>
</template>
<script setup>
import { ref } from 'vue';
import drawer from './drawer.vue';
import { ZrMessage, ZrMessageBox } from 'qianji-ui';
import { systemApiList, detailSystemApi, deleteSystemApi, switchSystemApi } from '@/api/system/apiManagement';
  
  
const loading = ref(false);
const tableData = ref([]);
const total = ref(0);
const openDrawer = ref(false);
const drawerTitle = ref('');
const drawerData = ref({});
const searchForm = reactive({
  page: 1,
  'page_size': 20,
  name: ''
});
const openTotal = ref(0);
  
systemApiListFun();
function systemApiListFun() {
  loading.value = true;
  systemApiList(searchForm).then(res => {
    tableData.value = res.data.data.list;
    total.value = res.data.data.count;
    openTotal.value = res.data.data.open_total;
  }).finally(() => {
    loading.value = false;
  });
}
  
function openDrawerClick(type, row) { // 添加、编辑
  openDrawer.value = true;
  drawerTitle.value = type == 'add' ? '添加搜索引擎' : '编辑搜索引擎';
  if (type == 'edit') {
    detailSystemApi(row.id).then(res => {
      if (res.data.code == 0) {
        drawerData.value = res.data.data;
      } else {
        drawerData.value = {};
      }
    });
  } else {
    drawerData.value = {};
  }
}
  
function switchChange(row) {
  switchSystemApi(row.id, { 'is_open': row.switch }).then(res => {
    ZrMessage.success(res.data.data);
    systemApiListFun();
  });
}
  
function deleteClick(row) { // 删除
  ZrMessageBox.confirm('确认删除?', '确认操作', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      deleteSystemApi(row.id).then(res => {
        ZrMessage.success('删除成功');
        systemApiListFun();
      });
    })
    .catch(() => {
    });
}
  
function searchClick() { // 搜索
  searchForm.page = 1;
  systemApiListFun();
}
  
function handleSizeChange(val) {
  searchForm.page = 1;
  searchForm['page_size'] = val;
  systemApiListFun();
}
function handleCurrentChange(val) {
  searchForm.page = val;
  systemApiListFun();
}
  
function saveClick(form) {
  openDrawer.value = false;
  systemApiListFun();
}
  
function cancelClick() {
  openDrawer.value = false;
}
  
</script>
  <style lang="scss" scoped>
  .image-container {
    display: inline-flex;
    justify-content: center;
    align-items:center
  }
  .custom-image {
    margin-right: 5px;
    width:16px;
    height: auto;
    object-fit: contain; /* 保持长宽比 */
  }
  </style>
  
