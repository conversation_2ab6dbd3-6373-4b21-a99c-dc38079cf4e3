<template>
  <div>
    <zr-drawer
      v-bind="$attrs"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      @open="draOpen"
    >
      <template #header>
        <h4 style="color: #303133;">
          {{ title }}
        </h4>
      </template>
      <p
        class="titleStyle"
      >基本信息</p>
      <zr-form
        ref="formRef"
        label-position="top"
        :model="form"
        :rules="rules"
      >
        <zr-form-item
          label="搜索引擎"
          prop="search_engine"
        >
          <zr-input
            v-model="form.search_engine"
            maxlength="20"
            show-word-limit
            placeholder="请输入字母、数字、汉字,1-20位"
          />
        </zr-form-item>
        <zr-form-item
          label="地址"
          prop="ip"
        >
          <zr-input
            v-model="form.ip"
            maxlength="100"
            show-word-limit
            placeholder="请输入地址,1-100位"
          />
        </zr-form-item>
        <zr-form-item
          prop="description"
          label="备注"
        >
          <zr-input
            v-model="form.description"
            type="textarea"
            maxlength="500"
            show-word-limit
            placeholder="请输入描述,0-500位"
          />
        </zr-form-item>
      </zr-form>
      <p
        class="titleStyle"
      >上传图标（大小不超过100K）</p>
      <zr-upload
        ref="upload"
        action="#"
        :multiple="true"
        :limit="1"
        :auto-upload="false"
        :on-exceed="handleExceed"
        :on-change="handleChange"
        :on-remove="handleRemove"
        :headers="headersObj"
      >
        <template #trigger>
          <zr-button type="primary">点击上传</zr-button>
        </template>
        <template #tip>
          <div class="el-upload__tip">支持jpg/png格式图片，仅限100KB以内.</div>
        </template>
      </zr-upload>
      <template #footer>
        <zr-button
          type="primary"
          size="default"
          @click="submit(formRef)"
        >
          确定
        </zr-button>
        <zr-button
          size="default"
          @click="cancelClick"
        >
          取消
        </zr-button>
      </template>
    </zr-drawer>
  </div>
</template>
<script setup>
import { ref, reactive, defineEmits, defineProps } from 'vue';
import { ZrMessage } from 'qianji-ui';
import { addSystemApi, editSystemApi } from '@/api/system/apiManagement';
import { useCookies } from 'vue3-cookies';
import { genFileId } from 'element-plus';
  
const { cookies } = useCookies();
const emit = defineEmits(['cancelClick', 'saveClick']);
const getFile = ref({});
const headersObj = { // 上传文件时传token
  'token': cookies.get('dsp_token')
};
const props = defineProps({
  title: {
    type: String
  },
  drawerData: {
    type: Object
  }
});
  
const form = ref({
  'search_engine': '',
  ip: '',
  description: ''
});
watch(() => props.drawerData, (newVal) => {
  form.value = newVal;
}, {
  deep: true,
  immediate: true
}
);
const formRef = ref();
const rules = reactive({
  'search_engine': [
    { required: true, message: '请输入搜索引擎', trigger: 'blur' },
    { min: 1, max: 20, message: '长度不超过20', trigger: 'blur' },
    { pattern: /^[\u4e00-\u9fa5a-zA-Z0-9]+$/, message: '只能输入字母、数字、汉字', trigger: 'blur' }
  ],
  ip: [
    { required: true, message: '请输入地址', trigger: 'blur' },
    { min: 1, max: 100, message: '长度不超过100', trigger: 'blur' },
    { pattern: /^(http(s)?:\/\/)?(www\.)?([a-zA-Z0-9-]+\.){1,2}[a-zA-Z]{2,}(\.[a-zA-Z]{2,})?(\/[a-zA-Z0-9-._~:/?#[\]@!$&'()*+,;=%]*)?$/, message: '请输入包括http/https协议、www子域名、域名、路径等常见网页地址格式', trigger: 'blur' }
  ]
});
  
const handleChange = (response) => { // 上传文件状态改变时
  getFile.value = response.raw; // 取文件信息
};
  
function handleRemove() { // 移除文件
  getFile.value = {};
}
  
function validationFun() { // 校验上传的类型与大小
  const fileType = ["image/jpg", "image/png", "image/jpeg"]; // 判断类型
  const isFileType = fileType.includes(getFile.value.type);
  if (!isFileType) {
    ZrMessage.error('只支持JPG/PNG格式图片');
    getFile.value = {};
    return false;
  }
  
  const isSize = getFile.value.size / 1024 < 100; // 判断大小
  if (!isSize) {
    ZrMessage.error("大小不能超过100KB!");
    return false;
  }
  return true;
}
  
  
const submit = (formEl) => {
  if (!formEl) return;
  formEl.validate(async(valid) => {
    if (valid) {
      const data = new FormData();
      data.append('file', getFile.value);
      data.append('search_engine', form.value.search_engine);
      data.append('ip', form.value.ip);
      data.append('description', form.value.description || '');
      if (props.title == '添加搜索引擎') {
        if (Object.keys(getFile.value).length === 0) {
          ZrMessage.error('请上传图片');
          return;
        }
        if (!validationFun()) {
          return;
        }
        addSystemApi(data).then(res => { // 添加接口
          ZrMessage.success(res.data.data);
          emit('saveClick');
        }).catch(() => {
        });
      } else {
        if (Object.keys(getFile.value).length !== 0) {
          if (!validationFun()) {
            return;
          }
        }
        editSystemApi(props.drawerData.id, data).then(res => { // 编辑接口
          ZrMessage.success(res.data.data);
          emit('saveClick');
        }).catch(() => {
        });
      }
    } else {
      return false;
    }
  });
};
  
const upload = ref();
const handleExceed = (files) => { // 上传的文件超出限制的个数
  upload.value.clearFiles();
  const file = files[0];
  file.uid = genFileId();
  upload.value.handleStart(file);
};
  
function draOpen() { // 打开弹窗
  getFile.value = {};
}
  
function cancelClick() {
  emit('cancelClick');
}
</script>
  <style lang="scss" scoped>
    .titleStyle{
      font-size: 14px;
      font-weight: 800;
      margin-bottom: 10px;
    }
  
  </style>
  
