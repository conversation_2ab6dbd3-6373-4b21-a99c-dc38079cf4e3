<template>
  <!-- 数据包管理 -->
  <div class="main-content">
    <zr-form
      :inline="true"
      :model="searchForm"
      @submit.prevent
    >
      <zr-row :gutter="24">
        <zr-col :span="4">
          <zr-button
            type="danger"
            plain
            :disabled="ids.length===0"
            @click="batchDelClick"
          >批量删除</zr-button>
        </zr-col>
        <zr-col
          :span="20"
          style="text-align: right;"
        >
          <!-- <zr-form-item>
            <zr-select v-model="searchForm.status">
              <zr-option
                label="全部上传状态"
                value=""
              />
              <zr-option
                label="二区"
                value="beijing"
              />
            </zr-select>
          </zr-form-item> -->
          <zr-form-item style="width:300px">
            <zr-input
              v-model="searchForm.search_content"
              placeholder="文件名/关联主机"
              @keyup.enter="searchClick"
            >
              <template #append>
                <zr-button @click="searchClick">
                  <zr-icon
                    name="Search"
                  />
                </zr-button>
              </template>
            </zr-input>
          </zr-form-item>
        </zr-col>
      </zr-row>
    </zr-form>
    <zr-table
      ref="tabelList"
      v-loading="loading"
      :data="tableData"
      empty-text="暂无数据"
      :row-key="getRowKey"
      @expand-change="expandChange"
      @selection-change="handleSelectionChange"
    >
      <zr-table-column
        type="expand"
        width="30"
      >
        <template #default="props">
          <div class="detail-msg">
            <zr-card>
              <zr-row
                :gutter="24"
                class="data-packet"
              >
                <zr-col
                  v-for="(item,index) in dataPacketOption"
                  :key="item.title"
                  :span="index<2?9:6"
                >
                  <div v-if="props.row[item.key]">
                    <h3 class="title-pie">{{ item.title }}</h3>
                    <p
                      v-for="ite in item.data"
                      :key="ite.label"
                    >{{ ite.label }}：
                      <span v-if="ite.key==='upload_status'">{{ props.row[item.key][ite.key]==='exist'?'已上传':'未上传' }}</span>
                      <span v-else-if="ite.key==='package_created_at'||ite.key==='upload_finished_at'">{{ timeFormat(props.row[item.key][ite.key]) }} <zr-tag v-if="props.row[item.key][ite.key+'_ago']">{{ props.row[item.key][ite.key+'_ago'] }}</zr-tag></span>
                      <span v-else-if="ite.key==='size'">{{ numDelivery(props.row[item.key][ite.key])||'-' }}</span>
                      <span
                        v-else-if="ite.key==='sys_type'"
                        :class="props.row[item.key][ite.key]==='Linux'?'':'system-icon-style'"
                      >
                        <SystemIcon :name="props.row[item.key][ite.key].toLowerCase()||''" />
                      </span>
                      <span v-else-if="ite.key==='upload_method'">{{ props.row[item.key][ite.key]==='manual'?'手动上传':props.row[item.key][ite.key] }}</span>
                      <span v-else>{{ props.row[item.key][ite.key]||'-' }}</span>
                      <zr-button
                        v-if="ite.copy"
                        type="primary"
                        link
                        left-icon="zr-copy-file"
                        @click="copyClick(props.row[item.key][ite.key])"
                      />
                    </p>
                  </div>
                </zr-col>
                <zr-col :span="24">
                  <p
                    v-if="props.row.pkg_info"
                  >备注信息：
                    <span style="display: flex;align-items: center;">
                      <span v-if="!props.row.descriptionBool">{{ props.row.pkg_info.description }}</span>
                      <zr-input
                        v-else
                        v-model="description"
                        placeholder="请输入处置相关的描述信息，最长为30个字符"
                        maxlength="30"
                        style="width:500px"
                      />
                      <zr-button
                        type="primary"
                        link
                        style="margin-left: 8px"
                        @click="descriptionClick(props.row)"
                      >{{ props.row.descriptionBool?'保存':'编辑' }}</zr-button>
                    </span>
                  </p>
                </zr-col>
              </zr-row>

            </zr-card>
          </div>
        </template>
      </zr-table-column>
      <zr-table-column
        type="selection"
        width="30"
      />
      <zr-table-column
        prop="filename"
        label="数据包类型/文件名"
        show-overflow-tooltip
      >
        <template #default="scope">
          <div class="data-type">
            <div>
              <span>{{ scope.row.type }}</span>
            </div>
            <p>{{ scope.row.filename }}</p>
          </div>
        </template>
      </zr-table-column>
      <!-- <zr-table-column
        prop="etag"
        label="校验和（Etag）"
        show-overflow-tooltip
      /> -->
      <zr-table-column
        prop="sys_type"
        label="操作系统"
        width="120"
        show-overflow-tooltip
      >
        <template #default="scope">
          <span :class="scope.row.sys_type==='Linux'?'':'system-icon-style'">
            <SystemIcon :name="scope.row.sys_type.toLowerCase()||''" />
          </span>
        </template>
      </zr-table-column>
      <zr-table-column
        prop="size"
        label="大小（GB）"
        width="110"
        show-overflow-tooltip
      >
        <template #default="scope">
          {{ numDelivery(scope.row.size) }}
        </template>
      </zr-table-column>
      <!-- <zr-table-column
        prop="upload_method"
        label="上传方式"
        width="100"
        show-overflow-tooltip
      ><template #default="scope">
        {{ scope.row.upload_method==='manual'?'手动上传':scope.row.upload_method }}
      </template>
      </zr-table-column> -->
      <zr-table-column
        prop="connect_name"
        label="关联主机"
        show-overflow-tooltip
      >
        <template #default="scope">
          {{ scope.row.connect_name||'-' }}
          <br>
          {{ scope.row.connect_ip }}
        </template>
      </zr-table-column>
      <zr-table-column
        prop="assignment_name"
        label="所属任务"
        show-overflow-tooltip
      >
        <template #default="scope">
          {{ scope.row.assignment_name||'-' }}
        </template>
      </zr-table-column>
      <zr-table-column
        prop="username"
        label="所属用户"
        show-overflow-tooltip
        width="100"
      />
      <zr-table-column
        prop="package_created_at"
        label="上传时间"
        width="160"
        show-overflow-tooltip
      >
        <template #default="scope">
          <zr-tag v-if="scope.row.package_created_at_ago">{{ scope.row.package_created_at_ago }}</zr-tag>
          <p>{{ timeFormat(scope.row.package_created_at) }}</p>
        </template>
      </zr-table-column>
      <zr-table-column
        prop="upload_status"
        label="上传状态"
        width="80"
        show-overflow-tooltip
      >
        <template #default="scope">
          {{ scope.row.upload_status=='exist'?'已上传':'未上传' }}
        </template>
      </zr-table-column>
      <zr-table-column
        label="操作"
        width="200"
      >
        <template #default="scope">
          <!-- <zr-button
            link
            type="primary"
            size="small"
          >
            查看日志
          </zr-button> -->
          <zr-button
            link
            type="primary"
            @click="analysisClick(scope.row)"
          >
            再次分析
          </zr-button>
          <zr-button
            link
            type="primary"
            @click="delClick(scope.row)"
          >
            删除
          </zr-button>
          <zr-button
            link
            type="primary"
            @click="downloadClick(scope.row)"
          >
            下载
          </zr-button>
        </template>
      </zr-table-column>
    </zr-table>
    <zr-pagination
      v-if="total>0"
      :current-page="searchForm.page_index"
      :page-size="searchForm.page_size"
      :page-sizes="[20, 40, 60, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
    <zr-drawer
      v-model="drawerBool"
      title="数据包分析"
    >
      <template #default>
        <div
          v-if="drawerBool"
          class="analysis-msg data-packet"
        >
          <div>
            <h3 class="title-pie">基本信息</h3>
            <p
              v-for="item in analysisOption"
              :key="item.label"
            >{{ item.label }}：{{ analysisMsg[item.key] }}</p>
          </div>
          <div>
            <h3 class="title-pie">重新分析配置信息</h3>
            <zr-form
              ref="ruleFormRef"
              :model="form"
              :rules="rules"
              label-position="top"
            >
              <zr-form-item label="操作系统类型">
                <zr-radio-group
                  v-model="form.sys_type"
                  @change="typeChange"
                >
                  <zr-radio-button label="windows">Windows</zr-radio-button>
                  <zr-radio-button label="linux">Linux</zr-radio-button>
                </zr-radio-group>
              </zr-form-item>
              <zr-form-item
                label="所属任务"
                prop="task_id"
              >
                <zr-select
                  v-model="form.task_id"
                  filterable
                >
                  <zr-option
                    v-for="item in tasksOption"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  />
                </zr-select>
              </zr-form-item>
              <zr-form-item
                label="系统内核符号表"
                prop="symbol_id"
              >
                <template #label>
                  系统内核符号表
                  <span class="label-msg">请确保您所选择的系统内核符号表与内存镜像匹配</span>
                </template>
                <zr-select
                  v-model="form.symbol_id"
                  filterable
                  clearable
                >
                  <zr-option
                    label="内置符号表"
                    value=""
                  />
                  <zr-option
                    v-for="item in symbolsOption"
                    :key="item.Id"
                    :label="item.Name"
                    :value="item.Id"
                  />
                </zr-select>
              </zr-form-item>
              <zr-form-item
                label=""
                prop="volatility_version"
              >
                <template #label>
                  <div style="display: flex;align-items: center;">
                    内存分析引擎
                    <zr-tooltip
                      class="box-item"
                      effect="dark"
                      placement="top-start"
                    > <template #content> V3：在V2引擎的基础上，可兼容更高版本的操作系统<br>V2：适用于 Windows 10.0.19041 和 Linux Kernel 4.4 以及更早版本的操作系统</template>
                      <zr-icon
                        style="margin-left: 4px;"
                        name="InfoFilled"
                        size="15"
                      />
                    </zr-tooltip>
                  </div>
                </template>
                <zr-select
                  v-model="form.volatility_version"
                >
                  <zr-option
                    label="V2"
                    value="V2"
                  />
                  <zr-option
                    label="V3"
                    value="V3"
                  />
                </zr-select>
              </zr-form-item>
              <zr-form-item
                label="分析插件"
                prop="plugin_ids"
              >
                <zr-select
                  v-model="form.plugin_ids"
                  :options="pluginsOption"
                  multiple
                  clearable
                  collapse-tags
                  collapse-tags-tooltip
                />
              </zr-form-item>
            </zr-form>
          </div>
        </div>
      </template>
      <template #footer>
        <div style="flex: auto">
          <zr-button
            type="primary"
            @click="confirmClick(ruleFormRef)"
          >分析</zr-button>
          <zr-button @click="cancelClick">取消</zr-button>
        </div>
      </template>
    </zr-drawer>
  </div>
</template>
<script setup>
import { ref, reactive, getCurrentInstance, onMounted } from 'vue';
import { timeFormat } from '@/utils/formatting';
import { listApi, listDetailApi, listDelApi, listBatchDelApi, listDownloadApi, getPkgConfig, tasksOptionApi, symbolsOptionApi, analysePkg, editDescription } from '@/api/dataPacket/list';
import { ZrMessage, ZrMessageBox } from 'qianji-ui';
import { useRouter } from 'vue-router';
import { pluginsOptionApi } from '@/api/task/dataPacket';

const router = useRouter();
const { proxy } = getCurrentInstance();
const searchForm = ref({
  'page_index': 1,
  'page_size': 20,
  // status: '',
  'search_content': ''
});
const description = ref('');
const total = ref(0);
const tableData = ref([]);
const tabelList = ref();
const tasksOption = ref([]);
const symbolsOption = ref([]);
const dataPacketOption = [
  {
    title: '数据包基本信息',
    key: 'pkg_info',
    data: [
      {
        label: '文件名',
        key: 'filename',
        copy: true
      },
      {
        label: '数据包类型',
        key: 'type'
      },
      {
        label: '大小（GB）',
        key: 'size'
      },
      {
        label: '上传方式',
        key: 'upload_method'
      },
      {
        label: '校验和（Etag）',
        key: 'etag',
        copy: true
      }
    ]
  },
  {
    title: '关联信息',
    key: 'conn_info',
    data: [
      {
        label: '关联主机名',
        key: 'connect_name'
      },
      {
        label: '关联主机IP',
        key: 'connect_ip'
      },
      {
        label: '操作系统',
        key: 'sys_type'
      },
      {
        label: '所属任务',
        key: 'assignment'
      },
      {
        label: '所属用户',
        key: 'username'
      }
    ]
  },
  {
    title: '执行信息',
    key: 'exe_info',
    data: [
      {
        label: '上传状态',
        key: 'upload_status'
      },
      {
        label: '上传时间',
        key: 'package_created_at'
      },
      {
        label: '完成时间',
        key: 'upload_finished_at'
      },
      {
        label: '用时（分钟）',
        key: 'expend'
      }
    ]
  }
];
const analysisOption = [
  {
    label: '类型',
    key: 'sys_type'
  },
  {
    label: '文件名',
    key: 'filename'
  },
  {
    label: '校验和（Etag）',
    key: 'etag'
  },
  {
    label: '大小（M）',
    key: 'size'
  }
];
const drawerBool = ref(false);
const ruleFormRef = ref();
const rules = reactive({
  'task_id': [{
    required: true,
    message: '请选择所属任务',
    trigger: 'blur'
  }]
  // 'symbol_id': [{
  //   required: true,
  //   message: '请选择Symbol文件',
  //   trigger: 'blur'
  // }]
});
const form = ref({});
const ids = ref([]);
const analysisMsg = ref({});
const loading = ref(false);
const pluginsOption = ref([]);
onMounted(() => {
  listFun();
});

function typeChange(type) {
  form.value['plugin_ids'] = []; // 切换操作系统后，清空分析插件内容
  pluginsOptionFun(type);
}

function pluginsOptionFun(type) { // 分析插件
  pluginsOptionApi({ os: type }).then(res => {
    pluginsOption.value = res.data.data.list;
  });
}

function descriptionClick(row) {
  if (row.descriptionBool) {
    editDescription({ description: description.value }, row.id).then(res => {
      if (res.data.code === 0) {
        row.pkg_info.description = description.value;
        row.descriptionBool = false;
        ZrMessage.success('修改备注成功');
      } else {
        ZrMessage.error(res.data.msg);
      }
    });
  } else {
    description.value = row.pkg_info.description;
    row.descriptionBool = true;
  }
}
function downloadClick(row) {
  listDownloadApi(row.id).then(res => {
    if (res.data.code === 0) {
      window.open(res.data.data.url);
    } else {
      ZrMessage.error(res.data.message);
    }
  });
}
function numDelivery(num) {
  var size = num / 1024 / 1024 / 1024;
  var result = parseFloat(size);
  if (isNaN(result)) {
    return false;
  }
  result = Math.round(size * 100) / 100;
  return result;
}
function listFun() {
  loading.value = true;
  listApi(searchForm.value).then(res => {
    tableData.value = res.data.data.list;
    total.value = res.data.data.count;
  }).finally(() => {
    loading.value = false;
  });
}
function getRowKey(row) { // 在列表刷新之前，保存已勾选的数据
  return row.id;
}
function expandChange(row, expandedRows) {
  row.descriptionBool = false;
  if (row['conn_info'] === undefined) {
    listDetailApi({ id: row.id }).then(res => {
      row['conn_info'] = res.data.data.conn_info;
      row['exe_info'] = res.data.data.exe_info;
      row['pkg_info'] = res.data.data.pkg_info;
    });
    tabelList.value.setCurrentRow(row);
  }
}
function analysisClick(row) {
  analysisMsg.value = row;
  tasksOptionApi({ page: 1, 'page_size': 999 }).then(res => {
    tasksOption.value = res.data.data.list;
  });
  symbolsOptionApi({ 'page_index': 1, 'page_size': 999, 'is_bulit_in': false }).then(res => {
    symbolsOption.value = res.data.data.list;
  });
  getPkgConfig({ 'image_id': row.id }).then(res => {
    form.value = res.data.data;
    form.value['sys_type'] = res.data.data.os;
    pluginsOptionFun(form.value['sys_type']);
  });
  nextTick(() => {
    drawerBool.value = true;
  });
}
function cancelClick() {
  drawerBool.value = false;
}
const confirmClick = async(formEl) => {
  if (!formEl) return;
  await formEl.validate((valid, fields) => {
    if (valid) {
      form.value['image_id'] = analysisMsg.value.id;
      form.value['plugin_ids'] = form.value['plugin_ids'].join(',');
      form.value['sys_type'] = form.value['sys_type'] == 'linux' ? 'Linux' : 'Windows';
      delete form.value.os;
      analysePkg(form.value).then(res => {
        if (res.data.code === 0) {
          ZrMessage.success('开始分析');
          setTimeout(() => {
            router.push({ name: 'TaskDetection' });
          }, 500);
        } else {
          ZrMessage.error(res.data.message);
        }
      });
    }
  });
};
function delClick(row) { // 删除
  ZrMessageBox.confirm('确认删除该项?', '提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      listDelApi(row).then(res => {
        if (res.data.code === 0) {
          ZrMessage.success('删除成功');
          listFun();
        } else {
          ZrMessage.error(res.data.message);
        }
      });
    });
}
function batchDelClick() { // 批量删除
  ZrMessageBox.confirm('确认删除所选项?', '提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      listBatchDelApi({ ids: ids.value }).then(res => {
        if (res.data.code === 0) {
          ZrMessage.success('删除成功');
          listFun();
        } else {
          ZrMessage.error(res.data.message);
        }
      });
    });
}
function searchClick() {
  searchForm.value['page_index'] = 1;
  listFun();
}
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
}
function handleSizeChange(val) {
  searchForm.value['page_index'] = 1;
  searchForm.value['page_size'] = val;
  listFun();
}
function handleCurrentChange(val) {
  searchForm.value['page_index'] = val;
  listFun();
}
function copyClick(text) { // 复制
  proxy.copyFun(text);
}
</script>
<style lang="scss" scoped>
.data-packet{
  line-height: 24px;
  margin-bottom: 20px;
  h3{
    margin: 12px 0 8px;
    font-size: 14px;
  }
  p{
    display: flex;
    align-items: center;
    font-size: 14px;
    line-height: 30px;
    color: #4E5155;
  }
}
.analysis-msg{
  h3{
    margin: 0 0 8px;
  }
  >div:nth-child(2){
    margin-top: 40px;
  }
}
.label-msg{
  font-size: 12px;
  color: #bbb;
  margin-left: 20px;
}
// :deep(.icon-box){
//   >span{
//     display: none;
//   }
// }
.data-type{
  >div{
    display: flex;
    align-items: center;
    >span{
      margin-right: 4px;
    }
  }
  >p{
    font-weight: 600;
  }
}
</style>
  
