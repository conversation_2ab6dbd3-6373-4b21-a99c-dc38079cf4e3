<template>
  <div
    id="upload-progress"
    style="visibility:hidden;"
  >
    <div class="title">
      <h2>数据包上传进度</h2>
      <p
        class="close"
        @click="closeClick"
      >
        <zr-icon
          name="Close"
          size="18"
        />
      </p>
    </div>
    <div class="content">
      <div
        v-for="(item,index) in uploadProgressList"
        :key="index"
        class="content-msg"
      >
        <div class="content-title">
          <Icon
            :color="progressBag(item)"
            :name="progressIcon(item)"
          />
          <p class="file-name">{{ item.filename }}</p>
          <p
            v-if="item.percentCompleted<100&&!item.stop"
            class="stop"
            @click="closeFileClick(index,'stop')"
          >
            <zr-tooltip
              class="box-item"
              effect="dark"
              content="停止上传"
              placement="top"
            >
              <Icon
                name="zr-circle-pause"
                :size="16"
              />
            </zr-tooltip>
          </p>
          <p
            v-else
            class="close"
            @click="closeFileClick(index,'close')"
          >
            <zr-tooltip
              class="box-item"
              effect="dark"
              content="删除记录"
              placement="top"
            >
              <Icon
                name="zr-circle-close"
                :size="16"
              />
            </zr-tooltip>
          </p>
        </div>
        <div class="content-progress">
          <div>
            <p :style="{width: item.percentCompleted + '%',background: progressBag(item)}" />
          </div>{{ item.percentCompleted }}%
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { onMounted, onUnmounted } from 'vue';
import { useStore } from '@/store/store';
import { storeToRefs } from 'pinia';
const store = useStore();
const { uploadProgressList, routeBool } = storeToRefs(store);
const handleBeforeUnload = (e) => { // 判断如果刷新浏览器清空上传进度记录
  var upload = 0;
  uploadProgressList.value.map(item => {
    if (item.percentCompleted < 100) {
      upload++;
    }
  });
  if (upload > 0) {
    e.preventDefault();
    e.returnValue = '确定刷新页面吗？';
    return '确定刷新页面吗？';
  } else {
    uploadProgressList.value = [];
    routeBool.value = false;
  }
};
onMounted(() => {
  window.addEventListener('beforeunload', handleBeforeUnload);
});
onUnmounted(() => {
  window.removeEventListener('beforeunload', handleBeforeUnload);
});

function progressBag(item) {
  if (item.percentCompleted < 100 && item.stop) {
    return '#ff9900';
  } else if (item.percentCompleted === 100) {
    return '#07b630';
  } else {
    return '#1890ff';
  }
}
function progressIcon(item) {
  if (item.percentCompleted < 100 && item.stop) {
    return 'zr-polygon-Warning-fill';
  } else if (item.percentCompleted === 100) {
    return 'zr-circle-seleted-fill';
  } else {
    return 'zr-cloud-upload-fill-b';
  }
}
function closeClick() {
  document.getElementById('upload-progress').style.visibility = 'hidden';
}
function closeFileClick(index, type) {
  if (type === 'stop') { // 停止上传
    uploadProgressList.value[index].stop = true;
  } else { // 删除记录
    uploadProgressList.value.splice(index, 1);
    store.uploadProgressFun(uploadProgressList.value);
    if (uploadProgressList.value.length === 0) {
      document.getElementById('upload-progress').style.visibility = 'hidden';
    }
  }
}
</script>
<style lang="scss" scoped>
#upload-progress{
    position: fixed;
    height: 200px;
    background-color: rgb(251, 250, 250);
    border: 1px solid rgb(226, 226, 226);
    border-radius: 3px;
    padding: 0px;
    box-shadow: rgba(0, 0, 0, 0.08) 0px 2px 10px;
    position: absolute;
    right: 20px;
    top: 62px;
    width: 400px;
    overflow: hidden;
    z-index: 1000;
    min-height: 400px;
    transition-duration: 0.3s;
    .title{
        border-bottom: 1px solid rgb(226, 226, 226);
        display: flex;
        justify-content: space-between;
        padding-bottom: 12px;
        h2{
            font-size: 16px;
        }
        .close{
            width: 18px;
            height: 18px;
            cursor: pointer;
            color: #72767b
        }
        .close:hover{
          color: #1890ff
        }
    }
    .content{
        background-color: rgb(251, 250, 250);
        height: 310px;
        overflow: auto;
        .content-msg{
            border-bottom: 1px solid rgb(226, 226, 226);
            padding: 15px 5px;
            margin: 0px 16px;
            position: relative;
            .content-title{
                margin-bottom: 8px;
                display: flex;
                align-items: center;
                justify-content: space-between;
                .file-name{
                    width: 100%;
                    margin-left: 8px;
                    font-weight: 600;
                    font-size: 14px;
                }
                .close{
                    width: 16px;
                    height: 16px;
                    cursor: pointer;
                }
                .stop{
                    width: 16px;
                    height: 16px;
                    cursor: pointer;
                }
                .close:hover{
                    background: #ddd;
                }
            }
            .content-progress{
                width: 100%;
                display: flex;
                align-items: center;
                color: #606266;
                >div{
                    width: 100%;
                    height: 8px;
                    background: #eee;
                    border-radius: 4px;
                    margin-right: 8px;
                    >p{
                        position: relative;
                        top: 0;
                        left: 0;
                        width: 0%;
                        height: 8px;
                        background: red;
                        border-radius: 4px;
                    }
                }
            }
        }
    }
}
</style>
