<template>
  <div :class="`search-input-wrap search-input-wrap-${styleType}`">
    <zr-input
      v-model.trim="searchData.value"
      placeholder="port:'8080' OR service:'http'"
      @keyup.enter="search"
    >
      <template #prefix>
        <!-- <i :class="'search-logo-' + size"></i> -->
        <Icon name="zr-search" />
      </template>
      <template #suffix>
        <div class="search-input-wrap-handle">
          <zr-popover
            v-for="(item, key) in btnConfig"
            :key="key"
            placement="bottom-end"
            :width="200"
            trigger="click"
          >
            123
            <template #reference>
              <span>
                <zr-tooltip
                  class="box-item"
                  :effect="styleType === 'home' ? 'light' : 'dark'"
                  :content="item.label"
                  placement="top"
                >
                  <Icon
                    :name="item.icon"
                    :size="iconSize"
                  />
                </zr-tooltip>
              </span>
            </template>
          </zr-popover>
        </div>
      </template>
    </zr-input>
    <zr-button class="search-input-wrap-button" type="primary" @click="search()">搜索</zr-button>
  </div>
</template>

<script setup>
// 组件
import { ZrPopover } from 'qianji-ui';
// import Batch from './'
defineProps({
  styleType: {
    type: String,
    default: 'default'
  }
});
const emits = defineEmits(['search']);
// btn配置
const btnConfig = readonly([
  {
    label: 'IP/域名批量搜索',
    icon: 'zr-project-a-fill',
    key: 'batch'
  },
  {
    label: '使用关键词搜索',
    icon: 'zr-form',
    key: 'keyword'
  },
  {
    label: '使用网页图标搜索',
    icon: 'zr-circle-picture',
    key: 'icon'
  }
]);
const iconSize = ref('20');
// 搜索配置
const searchData = reactive({
  value: ''
});
// 搜索
function search() {
  emits('search', searchData);
}

</script>

<style lang="scss" scoped>
$searchHomeBgColor: rgba(40, 64, 114, 0.6);
$searchHomeFontColor:  #6082BE;
.search-input-wrap {
  height: 46px;
  display: flex;
  .el-input {
    :deep(.el-input__wrapper) {
      .search-input-wrap-handle {
        .el-tooltip__trigger {
          display: inline-block;
          margin-right: 5px;
        }
      }
    }
  }
  .search-input-wrap-handle {

  }
  .search-input-wrap-button {
    display: inline-block;
    width: 100px;
    height: 100%;
    border-radius: 0 4px 4px 0;
    margin-left: -3px;
  }
  &.search-input-wrap-default {
  }
  &.search-input-wrap-home {
    .el-input {
      background-color: $searchHomeBgColor;
      :deep(.el-input__wrapper) {
        border: 1px solid #426dc7;
        background-color: $searchHomeBgColor;
        box-shadow: none !important;
        .el-input__inner {
          color: #fff;
          &::placeholder {
            color: $searchHomeFontColor !important;
          }
        }
        .search-input-wrap-handle {
          .el-icon {
            cursor: pointer;
          }
        }
        .el-icon {
          font-size: 20px;
          color: $searchHomeFontColor;
          vertical-align: middle;
        }
      }
    }
  }
}
</style>
