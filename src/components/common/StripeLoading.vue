<!--LSUI>条纹进度条-->
<template>
  <div>
    <div
      :style="{
        width: '100%',
        height: `${bdH}px`,
        borderRadius: `${bdBorderRadius}px`,
        background: bdbgColor,
      }"
      class="border"
    >
      <div
        :style="{
          width: `${contentW}%`,
          height: '100%',
          borderRadius: `${contentBorderRadius}px`,
          transition: `${processMoveTime}s`,
          paddingLeft: `-${bdL + 10}px`,
        }"
        class="content"
      >
        <div
          :style="{
            width: `${contentInW}px`,
            height: '100%',
            background: contentColorGroup,
            backgroundSize: `${contentSpacing}px ${contentSpacing}px`,
            transform: `translate(-${contentInW / 3}px, 0)`,
            animationDuration: `${processBgMoveTime}s`,
          }"
          class="content_in"
        />
      </div>
    </div>
  </div>
</template>
   
<script>
export default {
  name: "Stripeloading",
  components: {},
  props: {
    // 进度条宽度
    width: {
      type: Number,
      default: 300
    },
    // 进度条高度
    height: {
      type: Number,
      default: 40
    },
    // 进度(1-100)
    process: {
      type: Number,
      default: 0
    },
    // 进度条颜色1
    processColor1: {
      type: String,
      default: "pink"
    },
    // 进度条颜色2
    processColor2: {
      type: String,
      default: "white"
    },
    // 条纹间距
    stripeSpacing: {
      type: Number,
      default: 50
    },
    // 进度条移动速度
    processSpeed: {
      type: Number,
      default: 5
    },
    // 条纹移动速度
    stripeSpeed: {
      type: Number,
      default: 5
    }
  },
  data() {
    return {
      bdW: 300, // 边框宽度
      bdH: 40, // 边框高度
      bdL: 10, // 边框厚度
      bdColor: "#ccc", // 边框颜色
      bdbgColor: "#eee", // 边框背景颜色
      contentW: 4, // 内容宽度
      contentColor1: "pink", // 内容颜色1
      contentColor2: "white", // 内容颜色2
      contentSpacing: 50, // 条纹间距
      processMoveTime: 3, // 进度条移动时长
      processBgMoveTime: 5 // 进度条背景移动时长
    };
  },
  computed: {
    // 边框弧度
    bdBorderRadius() {
      return this.bdH / 2;
    },
    // 内容弧度
    contentBorderRadius() {
      return (this.bdH - 2 * this.bdL) / 2;
    },
    // 内容斜条纹
    contentColorGroup() {
      const bgStyle =
          "linear-gradient(45deg," +
          this.contentColor1 +
          " 25%," +
          this.contentColor2 +
          " 0%," +
          this.contentColor2 +
          " 50%," +
          this.contentColor1 +
          " 0%," +
          this.contentColor1 +
          " 75%," +
          this.contentColor2 +
          " 0%" +
          ")";
      return bgStyle;
    },
    contentInW() {
      const contentInMinW =
          Math.ceil(this.bdW / this.contentSpacing) * this.contentSpacing;
      return Math.max(this.contentSpacing, contentInMinW) * 3;
    }
  },
  watch: {
    process: {
      handler(val, oldVal) {
        this.contentW = Math.min(val, 100);
      },
      deep: true,
      immediate: true
    }
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      this.bdW = this.width;
      this.bdH = this.height;
      this.contentW = Math.min(this.process, 100);
      this.contentColor1 = this.processColor1;
      this.contentColor2 = this.processColor2;
      this.contentSpacing = this.stripeSpacing;
      this.processMoveTime = 15 / this.processSpeed;
      this.processBgMoveTime = 25 / this.stripeSpeed;
    }
  }
};
</script>
   
  <style rel="stylesheet/scss" lang="scss" scoped>
  .border {
    overflow: hidden;
  }
  .content {
    overflow: hidden;
    margin: 0;
  }
  .content_in {
    animation-name: move;
    animation-timing-function: linear;
    animation-iteration-count: infinite;
  }
  @keyframes move {
    100% {
      transform: translate(0px, 0);
    }
  }
  </style>
