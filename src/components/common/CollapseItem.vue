<template>
  <div class="itemBox">
    <span class="name">{{ collapseItem.key }}</span>
    <span class="number">{{ collapseItem.doc_count }}</span>
  </div>
</template>
<script setup>
defineProps(['collapseItem']);
</script>


<style lang="scss" scoped>
  .itemBox{
    display: flex;
    justify-content: space-between;
    padding: 6px 0;
    .name{
      font-size: 13px;
      color: #409eff;
      cursor: pointer;
      padding-right:10px;
      box-sizing: border-box;
      flex:1;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .number{
      font-size: 13px;
      color: #a3a9b3;
    }
  }
</style>


