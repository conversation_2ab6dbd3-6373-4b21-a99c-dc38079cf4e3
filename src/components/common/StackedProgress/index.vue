<template>
  <!-- 进度条组件 -->
  <div class="ui-progress">
    <div class="ui-progress-track">
      <div
        class="ui-progress-bar is-complete"
        :style="completeStyle"
      />
      <div
        class="ui-progress-bar is-incomplete"
        :style="incompleteStyle"
      />
      <div
        class="ui-progress-bar is-error"
        :style="errorStyle"
      />
    </div>
    <div class="ui-progress-total">{{ completeCount }} / {{ incompleteCount }} / {{ errorCount }}</div>
  </div>
</template>
<script setup>
import { ref, defineEmits, defineProps } from 'vue';
const props = defineProps({
  total: {
    type: Number,
    default: 100
  },
  progress: {
    type: Array,
    default: () => []
  }
});

const errorCount = ref(props.progress[2] || 0);
const incompleteCount = ref(props.progress[1] || 0);
const completeCount = ref(props.progress[0] || 0);
const errorStyle = ref({
  width: calcWidth(props.progress[2], props.total)
});
const incompleteStyle = ref({
  width: calcWidth(props.progress[1], props.total)
});
const completeStyle = ref({
  width: calcWidth(props.progress[0], props.total)
});

function calcWidth(count, total) {
  let width = count / total * 100;
  
  if (width > 100) {
    width = 100;
  }
  return width + '%';
}

</script>
  
  <style lang="scss" scoped>
  .ui-progress {
    position: relative;
    margin: 0;
    padding: (14px + 2px) 0 0;
    width: 75%;
    height: 14px + 2px + 4px;
    line-height: 1;
  }
  
  .ui-progress-track {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    overflow: hidden;
    position: relative;
    height: 4px;
    border-radius: 100px;
    background-color: #ebeef5;
  }
  
  .ui-progress-bar {
    flex: 0 1 auto;
    height: 100%;
    text-align: center;
    line-height: 1;
    white-space: nowrap;
    transition: width 0.6s ease;
  
    &:last-of-type {
      border-top-right-radius: 100px;
      border-bottom-right-radius: 100px;
    }
  
    &.is-error {
      background-color: rgb(255, 153, 0);
    }
    &.is-incomplete {
      background-color: rgb(45, 183, 245);
    }
    &.is-complete {
      background-color: rgb(103, 194, 58);
    }
  }
  
  .ui-progress-total {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    text-align: center;
  }
  </style>
  
