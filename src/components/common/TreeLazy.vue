<!-- 分组树结构组件 -->
<template>
  <div class="group">
    <h3 v-if="label">{{ label }}</h3>
    <div>
      <zr-input
        v-model="searchInput"
        class="w-50 m-2"
        :suffix-icon="Search"
        :placeholder="'请输入'+ placeholderMsg"
      />
      <div class="group-tree">
        <zr-tree
          ref="treeRef"
          class="filter-tree"
          :data="dataSource"
          :load="loadNode"
          lazy
          :props="defaultProps"
          default-expand-all
          :filter-node-method="filterNode"
          @node-click="nodeClick"
          @node-expand="nodeOpen"
          @node-collapse="nodeClose"
        >
          <template #default="{ node, data }">
            <p
              class="tree-unit"
              @mouseenter="treeMouseenter(data)"
              @mouseleave="treeMouseleave(data)"
            >
              
              <span>
                <span v-if="icon">
                  <zr-icon
                    v-show="!node.expanded||node.isLeaf"
                    name="Folder"
                    color="#ffcd69"
                    size="20"
                  />
                  <zr-icon
                    v-show="node.expanded&&!node.isLeaf"
                    name="FolderOpened"
                    color="#ffcd69"
                    size="20"
                  />
                </span>
                {{ node.label }}
              </span>
              <span v-if="operate">
                <zr-button
                  v-show="data.id===treeId"
                  type="primary"
                  link
                >下载</zr-button>
              </span>
            </p>
          </template>
        </zr-tree>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, defineProps, watch } from 'vue';
import { Search } from '@element-plus/icons-vue';
import { scratchFileList } from '@/api/threat/mirrorAngle/linux';
  
const emits = defineEmits(['treeAddFun', 'treeDetailFun', 'updateLevelFun', 'checkChange', 'treeDelFun', 'change-path']);
const props = defineProps({
  label: { // 树结构标题
    type: String
  },
  dataSource: { // 来自父组件的树结构数据
    type: Array
  },
  showCheckbox: { // 是否支持多选框
    type: Boolean,
    default: false
  },
  draggable: { // 是否支持拖拽
    type: Boolean,
    default: false
  },
  operate: { // 是否支持操作按钮
    type: Boolean,
    default: false
  },
  placeholderMsg: { // 查询框内容
    type: String
  },
  icon: { // 是否显示文件图标
    type: Boolean,
    default: false
  },
  id: {
    type: String
  }
});
const searchInput = ref('');
const defaultProps = {
  children: 'children',
  label: 'label',
  disabled: 'disabled'
};
const treeRef = ref();
const treeId = ref('');
const searchForm = reactive({
  path: '/',
  page: 1,
  'page_size': 10
});

// 树节点点击事件
function nodeClick(data, node, obj) {
  console.log(data, node, obj, '111111111');
  // this.$emit('change-path', { path: data.path, id: data.id, pid: data.pid });
}

/** 节点打开 */
function nodeOpen(data, node, obj) {
  console.log(data, node, obj, '0000');
  emits('change-path', data);
  // data.icon = 'fa fa-folder-open';
}

/** 节点关闭 */
function nodeClose(data, node, obj) {
  // data.icon = 'fa fa-folder';
}
/** 树节点懒加载 */
function loadNode(node, resolve) {
  console.log(node.data, resolve, '11111');
  // if (node.level === 0) {
  //   scratchFileList(searchForm, props.id).then(response => {
  //     setTimeout(() => {
  //       // this.dataStatus = 'lazy';
  //     }, 500);
  //     return resolve(response.data);
  //   });
  // } else {
  // setTimeout(() => {
  // scratchFileList(searchForm, props.id).then(res => {
  //   var data = res.data.data.dir.list.map(item => {
  //     return { label: item, id: item, children: [] };
  //   });
  //   return resolve(data);
  // });
  // }, 500);
  // }
}
// onUpdated(() => {
//   nextTick(() => {
//     let selectId = '';
//     if ((!props.delChange) && id.value) {
//       selectId = id.value;
//     } else {
//       selectId = props.dataSource[0].id;
//     }
//     treeRef.value.setCurrentKey(selectId);
//     const data = [];
//     const dealTreeData = (treeData) => {
//       treeData.map((item) => {
//         if (item.check && item.AssetGroupItem.length == 0) {
//           data.push(item.group_id);
//         }
//         if (item.AssetGroupItem && item.AssetGroupItem.length > 0) {
//           dealTreeData(item.AssetGroupItem);
//         }
//       });
//     };
//     dealTreeData(props.dataSource);
//     treeRef.value.setCheckedKeys(data, true);
//   });
// });

watch(searchInput, (val) => { // 分组模糊查询
  treeRef.value.filter(val);
});

const filterNode = (value, data) => {
  if (!value) return true;
  return data.label.includes(value);
};
function treeMouseenter(data) {
  treeId.value = data.id;
}
function treeMouseleave() {
  treeId.value = '';
}
</script>
  <style lang="scss" scoped>
  .el-tree{
    min-height: 500px;
    margin-top: 8px;
  }
  .tree-unit{
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      span:nth-child(1){
        display: flex;
        align-items: center;
        .el-icon{
          margin-right:4px
        }
      }
  }
    .group{
        width: 300px;
        height: 100%;
        flex-shrink:0;
        display: block;
        border: 1px solid #E4E7ED;
        margin: 0 15px 10px 0;
        // box-shadow: 2px 2px 5px #ddd;
        h3{
            font-size: 15px;
            font-weight: 500;
            line-height: 47px;
            padding-left: 15px;
            border-bottom: 1px solid #E4E7ED;
        }
        >div{
            padding: 16px;
            // min-height: 500px;
            >.group-tree{
                // max-height: 400px;
                overflow: auto;
                margin-top: 12px;
            }
        }
        .custom-tree-node {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 14px;
            padding-right: 8px;
            .tree-name{
              max-width: 110px;
              display: inline-block;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
              margin-right: 5px;
            }
            span{
              display: flex;
              align-items: center;
                a{
                    color:#9b9b9b;
                    margin-left: 8px;
                }
            }
        }
    }
    :deep(.el-tree-node__content){
      margin-bottom: 4px;
    }
    :deep(.el-tree-node__label){
        width: 100%;
    }
  </style>
  
