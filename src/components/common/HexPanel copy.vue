<template>
  <div>
    <div class="hex-panel-box">
      <div class="control-box">
        <span class="prepare-box">
          <span class="prepare-label offset">偏移</span>
          <input
            v-model="inputOffset"
            class="prepare-input offset"
          >
        </span>
        <button @click="jumpOffset()">跳转</button>
        <!-- <button @click="jumpAlert()">下一处告警</button> -->
      </div>
      <div class="content-box">
        <div class="header-box">
          <div class="header-offset">OFFSET</div>
          <div class="header-hex">HEX</div>
          <div class="header-bin">BIN</div>
          <div class="header-scroll" />
        </div>
        <div class="body-box">
          <!--数据内容部分 v-infinite-scroll 滚动加载 style="overflow: auto;" 必填，否则一直加载-->
          <div
            v-infinite-scroll="scrollLoad"
            class="body-inner-box"
            style="overflow: auto;"
            @scroll="scroll(true)"
          >
            <div class="body-offset">
              <div
                v-for="(value,key) in list"
                :key="key"
              >{{ value.line }}</div>
            </div>
            <div class="body-hex">
              <div
                v-for="(value,key) in list"
                :key="key"
                class="row hex-row"
              >
                <span
                  v-for="(data,index) in value.hex"
                  :key="index"
                  class="hex-word"
                  :class="[{active:isHighLightWord(value.line,index)}]"
                >{{ data }}</span>
              </div>
            </div>
            <div class="body-bin">
              <div
                v-for="(value,key) in list"
                :key="key"
                class="row bin-row"
              >
                <span
                  v-for="(data,index) in value.hex"
                  :key="index"
                  class="bin-word"
                  :class="[{active:isHighLightWord(value.line,index)}]"
                >{{ hex2bin(data) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="alert-box">{{ loadingMsg }}</div>
    </div>
    <!-- <div class="table-box">
        <el-table :data="alertInfo.list" style="width: 95%;margin-left: 2.5%" max-height="463" border
                  :row-class-name="tableRowClassName" size="mini" >
          <el-table-column label="序号" width="60">
            <template v-slot="scope">
              {{ scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column prop="str" label="关键字" min-width="200">
            <template v-slot="scope">
              {{ base64Decode(scope.row.str) }}
            </template>
          </el-table-column>
          <el-table-column prop="tag" label="规则">
            <template v-slot="scope">
              <el-tag size="mini" v-if="scope.row.level === '高危'" type="danger"> {{ scope.row.tag }}</el-tag>
              <el-tag size="mini" v-else-if="scope.row.level === '中危'" type="warning"> {{ scope.row.tag }}</el-tag>
              <el-tag size="mini" v-else> {{ scope.row.tag }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="start" label="偏移" width="120"></el-table-column>
        </el-table>
      </div> -->
  </div>
</template>
  
<script>
// import { fileDetail, processDetail } from '@/api/windows_report';
  
export default {
  props: {
    id: {
      type: Number,
      default: 0
    },
    recordId: {
      type: Number,
      default: 0
    },
    module: {
      type: String,
      default: ''
    },
    memHash: {
      type: String,
      default: ''
    },
    classFile: {
      type: String,
      default: ''
    },
    processId: {
      type: Number,
      default: -1
    }
  
  },
  data() {
    return {
      ruleIndex: 0,
      processIndex: 0,
      blockIndex: 0,
      dataLoading: false,
      loadingMsg: '',
      list: [],
      offset: 0,
      topOffset: 0,
      inputOffset: 0,
      length: 512,
      maxRowTotal: 100,
      alertInfo: {
        current: -1, list: []
      },
      highLightWord: {}
    };
  },
  watch: {
    memHash(value) {
      if (value !== '') {
        this.getData();
        this.getMemoryCheckInfo();
      }
    },
    classFile(value) {
      if (value !== '') {
        this.getData();
        this.getMemoryCheckInfo();
      }
    }
  },
  mounted() {
    // 进程的单独通过watch来监听
    if (this.module !== '') {
      this.getData();
      this.getMemoryCheckInfo();
    }
  },
  methods: {
    /*
      * 向上滚动方法
      * */
    scroll(flag) {
      if (flag) {
        if (document.querySelector('.body-inner-box').scrollTop === 0 && this.topOffset !== 0) {
          const prevOffset = this.topOffset - this.length < 0 ? 0 : this.topOffset - this.length;
          this.getData(prevOffset, this.length, false, 'up');
        }
      }
    },
    /*
      * 判断是否为高亮字节
      * */
    isHighLightWord(line, offset) {
      return this.highLightWord[line] && this.highLightWord[line].indexOf(offset) !== -1;
    },
    /*
      * 向下滚动加载
      * */
    scrollLoad() {
      this.getData(this.offset, this.length);
    },
    base64Decode(data) {
      return window.atob(data);
    },
    /*
      * 十六进制转换二进制
      * */
    hex2bin(hex) {
      return String.fromCharCode(parseInt(hex, 16));
    },
    /*
      * 跳转到指定的偏移地址,并且清空之前加载的数据
      * */
    jumpOffset() {
      this.getData(this.inputOffset, this.length, true);
    },
    /*
      * 跳转至告警数据
      * */
    jumpAlert() {
      this.alertInfo.current = this.alertInfo.current + 1 >= this.alertInfo.list.length ? 0 : this.alertInfo.current + 1;
      this.inputOffset = this.alertInfo.list[this.alertInfo.current].start;
      this.getData(this.inputOffset, this.length, true);
    },
    /*
      * 获取数据
      * @param offset 偏移地址
      * @param length 请求的数据大小
      * @param reset 是否重置面板内的数据
      * @param dircetion 获取数据的方向（滚动的方向）
      * */
    getData(offset = 0, length = this.length, reset = false, direction = 'down') {
      if (!this.dataLoading) {
        const scrollBody = document.querySelector('.body-inner-box');
        this.dataLoading = true;
        this.loadingMsg = '正在加载...';
        let request;
        if (this.module === 'files') {
        //   request = fileDetail({ type: 'content', mid: this.recordId, id: this.id, offset: offset, length: length, hash: '$hash' });
        } else {
          if (this.processId === -1) {
            // request = processDetail({ type: 'content', mid: this.recordId, id: this.id, offset: offset, length: length, hash: this.memHash });
          } else {
            // request = processDetail({ type: 'content', mid: this.recordId, id: this.id, 'process_id': this.processId, offset: offset, length: length, name: encodeURI(this.classFile) });
          }
        }
        request.then((response) => {
          if (response.data && response.data.length) {
            if (reset) {
              this.list = [];
              this.topOffset = offset;
            }
            // 判断当前累加后的行数是不是超出定义的数组的最大长度,超出则丢弃最前面（down）/后面（up）的数据
            const result = this.list.length + response.data.length - this.maxRowTotal;
            if (direction === 'down') {
              if (result <= 0) {
                this.list = this.list.concat(response.data);
              } else {
                this.list = this.list.concat(response.data).slice(result);
                // 因为偏移地址小的数据被截去了,最顶部的偏移值要比之前的大
                this.topOffset += result * 16;
                const scrollTop = scrollBody.scrollTop;
                // 请求了新数据,但是面板内总的数据量没有变化,所以滚动条不会自动上移,需要人为控制
                scrollBody.scrollTop -= scrollTop / (this.maxRowTotal / (length / 16));
              }
              // 最后的偏移地址变成 初始传入的偏移地址加上 todo 请求到的数据长度,这里先用请求的数据长度代替
              this.offset = offset + this.length;
            } else {
              if (result <= 0) {
                // 注意,这里请求到的数据追加之后的数据
                this.list = response.data.concat(this.list);
              } else {
                // 超出最大数组长度的部分,保留从最开始到最大长度部分的数据
                this.list = response.data.concat(this.list).slice(0, this.maxRowTotal);
                scrollBody.scrollTop += this.maxRowTotal / (length / 16);
              }
              // 顶部偏移改为请求时的偏移地址
              this.topOffset = offset;
              this.offset = (offset + this.list.length * 16);
            }
            if (reset) {
              scrollBody.scrollTop = 1;
            }
            this.loadingMsg = '滚动加载更多';
          } else {
            this.list = [
              {
                line: '00000000',
                hex: [
                  '4d',
                  '5a',
                  '90',
                  '00',
                  '03',
                  '00',
                  '00',
                  '00',
                  '04',
                  '00',
                  '00',
                  '00',
                  'ff',
                  'ff',
                  '00',
                  '00'
                ]
              },
              {
                line: '00000010',
                hex: [
                  'b8',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '40',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00'
                ]
              },
              {
                line: '00000020',
                hex: [
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00'
                ]
              },
              {
                line: '00000030',
                hex: [
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '10',
                  '01',
                  '00',
                  '00'
                ]
              },
              {
                line: '00000040',
                hex: [
                  '0e',
                  '1f',
                  'ba',
                  '0e',
                  '00',
                  'b4',
                  '09',
                  'cd',
                  '21',
                  'b8',
                  '01',
                  '4c',
                  'cd',
                  '21',
                  '54',
                  '68'
                ]
              },
              {
                line: '00000050',
                hex: [
                  '69',
                  '73',
                  '20',
                  '70',
                  '72',
                  '6f',
                  '67',
                  '72',
                  '61',
                  '6d',
                  '20',
                  '63',
                  '61',
                  '6e',
                  '6e',
                  '6f'
                ]
              },
              {
                line: '00000060',
                hex: [
                  '74',
                  '20',
                  '62',
                  '65',
                  '20',
                  '72',
                  '75',
                  '6e',
                  '20',
                  '69',
                  '6e',
                  '20',
                  '44',
                  '4f',
                  '53',
                  '20'
                ]
              },
              {
                line: '00000070',
                hex: [
                  '6d',
                  '6f',
                  '64',
                  '65',
                  '2e',
                  '0d',
                  '0d',
                  '0a',
                  '24',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00'
                ]
              },
              {
                line: '00000080',
                hex: [
                  'c6',
                  '4f',
                  'd9',
                  '0a',
                  '82',
                  '2e',
                  'b7',
                  '59',
                  '82',
                  '2e',
                  'b7',
                  '59',
                  '82',
                  '2e',
                  'b7',
                  '59'
                ]
              },
              {
                line: '00000090',
                hex: [
                  '51',
                  '5c',
                  'b4',
                  '58',
                  '89',
                  '2e',
                  'b7',
                  '59',
                  '51',
                  '5c',
                  'b2',
                  '58',
                  '0a',
                  '2e',
                  'b7',
                  '59'
                ]
              },
              {
                line: '000000A0',
                hex: [
                  '51',
                  '5c',
                  'b3',
                  '58',
                  '96',
                  '2e',
                  'b7',
                  '59',
                  '26',
                  '50',
                  'b2',
                  '58',
                  'a2',
                  '2e',
                  'b7',
                  '59'
                ]
              },
              {
                line: '000000B0',
                hex: [
                  '26',
                  '50',
                  'b3',
                  '58',
                  '8d',
                  '2e',
                  'b7',
                  '59',
                  '26',
                  '50',
                  'b4',
                  '58',
                  '91',
                  '2e',
                  'b7',
                  '59'
                ]
              },
              {
                line: '000000C0',
                hex: [
                  '35',
                  '5f',
                  'b2',
                  '58',
                  '81',
                  '2e',
                  'b7',
                  '59',
                  '51',
                  '5c',
                  'b1',
                  '58',
                  '83',
                  '2e',
                  'b7',
                  '59'
                ]
              },
              {
                line: '000000D0',
                hex: [
                  '51',
                  '5c',
                  'b6',
                  '58',
                  '8f',
                  '2e',
                  'b7',
                  '59',
                  '82',
                  '2e',
                  'b6',
                  '59',
                  '27',
                  '2e',
                  'b7',
                  '59'
                ]
              },
              {
                line: '000000E0',
                hex: [
                  '96',
                  '51',
                  'bf',
                  '58',
                  '8b',
                  '2e',
                  'b7',
                  '59',
                  '96',
                  '51',
                  'b7',
                  '58',
                  '83',
                  '2e',
                  'b7',
                  '59'
                ]
              },
              {
                line: '000000F0',
                hex: [
                  '96',
                  '51',
                  'b5',
                  '58',
                  '83',
                  '2e',
                  'b7',
                  '59',
                  '52',
                  '69',
                  '63',
                  '68',
                  '82',
                  '2e',
                  'b7',
                  '59'
                ]
              },
              {
                line: '00000100',
                hex: [
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00'
                ]
              },
              {
                line: '00000110',
                hex: [
                  '50',
                  '45',
                  '00',
                  '00',
                  '4c',
                  '01',
                  '06',
                  '00',
                  '9a',
                  '96',
                  '7f',
                  '64',
                  '00',
                  '00',
                  '00',
                  '00'
                ]
              },
              {
                line: '00000120',
                hex: [
                  '00',
                  '00',
                  '00',
                  '00',
                  'e0',
                  '00',
                  '02',
                  '21',
                  '0b',
                  '01',
                  '0e',
                  '24',
                  '00',
                  '0e',
                  '01',
                  '00'
                ]
              },
              {
                line: '00000130',
                hex: [
                  '00',
                  'a8',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '54',
                  '61',
                  '00',
                  '00',
                  '00',
                  '10',
                  '00',
                  '00'
                ]
              },
              {
                line: '00000140',
                hex: [
                  '00',
                  '20',
                  '01',
                  '00',
                  '00',
                  '00',
                  '00',
                  '10',
                  '00',
                  '10',
                  '00',
                  '00',
                  '00',
                  '02',
                  '00',
                  '00'
                ]
              },
              {
                line: '00000150',
                hex: [
                  '06',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '06',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00'
                ]
              },
              {
                line: '00000160',
                hex: [
                  '00',
                  '00',
                  '02',
                  '00',
                  '00',
                  '04',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '02',
                  '00',
                  '40',
                  '01'
                ]
              },
              {
                line: '00000170',
                hex: [
                  '00',
                  '00',
                  '10',
                  '00',
                  '00',
                  '10',
                  '00',
                  '00',
                  '00',
                  '00',
                  '10',
                  '00',
                  '00',
                  '10',
                  '00',
                  '00'
                ]
              },
              {
                line: '00000180',
                hex: [
                  '00',
                  '00',
                  '00',
                  '00',
                  '10',
                  '00',
                  '00',
                  '00',
                  'e0',
                  '7c',
                  '01',
                  '00',
                  '54',
                  '00',
                  '00',
                  '00'
                ]
              },
              {
                line: '00000190',
                hex: [
                  '34',
                  '7d',
                  '01',
                  '00',
                  '64',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00'
                ]
              },
              {
                line: '000001A0',
                hex: [
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00'
                ]
              },
              {
                line: '000001B0',
                hex: [
                  '00',
                  'e0',
                  '01',
                  '00',
                  '48',
                  '16',
                  '00',
                  '00',
                  '30',
                  '71',
                  '01',
                  '00',
                  '38',
                  '00',
                  '00',
                  '00'
                ]
              },
              {
                line: '000001C0',
                hex: [
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00'
                ]
              },
              {
                line: '000001D0',
                hex: [
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '70',
                  '70',
                  '01',
                  '00',
                  '40',
                  '00',
                  '00',
                  '00'
                ]
              },
              {
                line: '000001E0',
                hex: [
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '20',
                  '01',
                  '00',
                  '9c',
                  '01',
                  '00',
                  '00'
                ]
              },
              {
                line: '000001F0',
                hex: [
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00',
                  '00'
                ]
              }
            ];
            this.loadingMsg = '已无更多数据';
          }
          this.dataLoading = false;
        });
      }
    },
    /*
      * 获取内存检测信息,包括各告警区段的偏移以及告警的字节位置
      * */
    getMemoryCheckInfo() {
      let request;
      if (this.module === 'files') {
        // request = fileDetail({ type: 'ruleList', mid: this.recordId, id: this.id });
      } else {
        if (this.processId === -1) {
        //   request = processDetail({ type: 'ruleList', mid: this.recordId, id: this.id, hash: this.memHash });
        } else {
        //   request = processDetail({ type: 'ruleList', mid: this.recordId, id: this.id, hash: this.memHash, 'process_id': this.processId, name: encodeURI(this.classFile) });
        }
      }
      request.then((response) => {
        if (response.data && response.data.alertInfo && response.data.highLightWords) {
          this.alertInfo.list = response.data.alertInfo;
          this.highLightWord = response.data.highLightWords;
        }
      });
    },
    getQueryVariable(variable) {
      const query = window.location.search.substring(1);
      const vars = query.split('&');
      for (let i = 0; i < vars.length; i++) {
        const pair = vars[i].split('=');
        if (pair[0] === variable) { return pair[1]; }
      }
      return false;
    },
    tableRowClassName({ row, rowIndex }) {
      if (rowIndex === this.alertInfo.current) {
        return 'danger-row';
      }
      return '';
    }
  }
};
</script>
  
  <style scoped>
  * {
    margin: 0;
    padding: 0;
    font-size: 14px;
    /*等宽字符*/
    font-family: Consolas, Monaco, monospace;
  }
  html, body {
    width: 100%;
    height: 100%;
    padding: 20px;
    box-sizing: border-box;
    min-width: 1366px;
    overflow: auto;
  }
  
  .hex-panel-box {
    width: 669px;
    display: flex;
    flex-direction: column;
    margin: 0 auto;
    border-radius: 2px;
    --border-color: #e3e3e3;
  }
  
  .hex-panel-box .control-box {
    padding: 5px 0;
  }
  
  .hex-panel-box .control-box>*{
    display: inline-block;
    vertical-align: middle;
  }
  
  .hex-panel-box .content-box {
    border: 1px solid var(--border-color);
  }
  
  .hex-panel-box .header-box, .hex-panel-box .body-box {
    display: flex;
    flex-direction: row;
  }
  
  .hex-panel-box .header-box {
    height: 40px;
    line-height: 40px;
    background: #f1f1f1;
  }
  
  .hex-panel-box .alert-box {
    text-align: center;
    flex-direction: row;
    font-family: 微软雅黑, "Raleway", sans-serif;
    padding: 5px 0;
    color: #409eff;
    font-size: 13px;
  }
  
  .hex-panel-box .header-box > div {
    text-align: center;
    box-sizing: border-box;
  }
  
  .hex-panel-box .body-box {
    height: 420px;
    overflow: auto;
  }
  
  .hex-panel-box .body-box .body-inner-box {
    width: 100%;
    height: auto;
  }
  
  .hex-panel-box .body-box .body-inner-box > div {
    float: left;
    height: auto;
    min-height: 420px;
    line-height: 25px;
    box-sizing: border-box;
  }
  
  .hex-panel-box .header-box .header-offset {
    width: 100px;
    border-right: 1px solid var(--border-color);
    border-bottom: 1px solid var(--border-color);
  }
  
  .hex-panel-box .body-box .body-inner-box .body-offset {
    width: 100px;
    padding: 10px;
    border-right: 1px solid var(--border-color);
    text-align: center;
  }
  
  .hex-panel-box .header-box .header-hex {
    width: 390px;
    border-right: 1px solid var(--border-color);
    border-bottom: 1px solid var(--border-color);
  }
  
  .hex-panel-box .body-box .body-inner-box .body-hex {
    width: 390px;
    border-right: 1px solid var(--border-color);
    padding: 10px;
    box-sizing: border-box;
  }
  
  .hex-panel-box .header-box .header-bin {
    width: 160px;
    border-right: 1px solid var(--border-color);
    border-bottom: 1px solid var(--border-color);
  }
  
  .hex-panel-box .body-box .body-inner-box .body-bin {
    width: 160px;
    padding: 10px;
    border-right: 1px solid var(--border-color);
  }
  
  .hex-panel-box .header-box .header-scroll {
    width: 16px;
    border-bottom: 1px solid var(--border-color);
  }
  
  .hex-panel-box .body-box .hex-word.active,.hex-panel-box .body-box .bin-word.active {
    color: #fff;
    background: #f00;
  }
  
  .hex-word,.bin-word{
    font-size: 14px;
    display: inline-block;
    width: calc(100% / 16);
    text-align: center;
  }
  
  /*preprae*/
  .hex-panel-box .prepare-box{
    display: inline-block;
    font-size: 0;
  }
  .hex-panel-box .prepare-box .prepare-label{
    text-align: center;
    height: 100%;
    box-sizing: border-box;
    border-radius: 0;
    font-size: 12px;
    width: 90px;
    padding: 0;
    line-height: 24px;
    text-align: center;
    background-color: #F5F7FA;
    color: #909399;
    position: relative;
    border: 1px solid #DCDFE6;
    border-right: 0;
    white-space: nowrap;
    display: inline-block;
    vertical-align: middle;
  }
  
  .hex-panel-box .prepare-box .prepare-input{
    border: 1px solid #DCDFE6;
    line-height: 26px;
    height: 26px;
    vertical-align: middle;
    border-radius: 0;
    padding-left: 5px;
    outline: none;
    box-sizing: border-box;
  }
  .hex-panel-box .prepare-box .prepare-label.offset{
    width: 60px;
  }
  
  .hex-panel-box .prepare-box .prepare-input.offset{
    width: 100px;
  }
  
  /*button*/
  .hex-panel-box .control-box button{
    font-family: 微软雅黑, 'Raleway', sans-serif;
    line-height: 24px;
    box-sizing: border-box;
    outline: none;
    border: 1px solid #DCDFE6;
    cursor: pointer;
    font-size: 12px;
    padding: 0 10px;
    min-width: 65px;
    border-radius: 0;
    color: #343434;
  }
  
  .hex-panel-box .control-box button:hover{
    background: #e1e1e1;
  }
  
  /*表格*/
  .table-box{
    display: block;
    --border-color: #e3e3e3;
  }
  .table-box .el-table{
    display: inline-block;
    width: 600px;
    margin-top: 15px;
    border: 1px solid var(--border-color);
  }
  .el-table .danger-row {
    background: rgba(255, 0, 0, 0.2);
  }
  /*表格*/
  
  </style>
  
