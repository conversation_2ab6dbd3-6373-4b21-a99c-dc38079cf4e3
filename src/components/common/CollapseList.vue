<template>
  <div class="collapseBox">
    <div class="action">
      <zr-button @click="open">{{ isUp ? '全部展开':'全部收起' }}</zr-button>
    </div>
    <zr-collapse v-model="activeNames" @change="handleChange">
      <template v-for="item in collapseList">
        <zr-collapse-item :title="item.title" :name="item.name">
          <template v-if="item.list.length > 0">
            <CollapseItem v-for="(item2,index2) in item.list" :key="index2" :collapse-item="item2" />
          </template>
          <p v-else class="noData">暂无数据</p>
        </zr-collapse-item>
      </template>
    </zr-collapse>
  </div>

</template>
<script setup>
import CollapseItem from '@/components/common/CollapseItem.vue';
import { queryCollapse } from '@/api/searchDetail';

const props = defineProps({
  collapseList: {
    type: Array,
    default: () => []
  },
  firstNames: {
    type: Array,
    default: () => []
  },
  allNames: {
    type: Array,
    default: () => []
  }
});

const isUp = ref(true);
const open = () => { // 全部展开 收起
  isUp.value = !isUp.value;
  if (isUp.value) {
    activeNames.value = [];
  } else {
    activeNames.value = props.allNames;
    getlist();
  }
};

const activeNames = ref(props.firstNames);
const handleChange = (val) => {
  activeNames.value = Object.values(val);
  getlist();
};
const getlist = async() => { // 请求折叠数据
  const params = {
    aggs_list: activeNames.value
  };
  const res = await queryCollapse(params);
  const { data, executeTime, status } = res;
  if (status === 200) {
    params.aggs_list.forEach(item => {
      props.collapseList.forEach(item2 => {
        if (item === item2.name) {
          item2.list = data[item];
        }
      });
    });
  }
};
getlist();
</script>

<style lang="scss" scoped>
:deep(.el-collapse-item__content){
  padding-bottom: 0px !important;
}
:deep(.el-collapse-item){
  // border-bottom: 1px solid #eaedf2;
}
:deep(.el-collapse-item__header:hover){
  background: #fff;
}
:deep(.el-collapse){
  border-top:none;
}
.action{
  text-align: center;
  :deep(.el-button){
    width: 200px;
  }
}
.noData{
  font-size: 13px;
  color: #242933;
  margin: 7px 0;
  text-align: center;
}
.collapseBox{
  // width:20%;
  // margin-right:20px;
  padding: 16px;
  box-sizing: border-box;
  background: #fff;
  position:sticky;
  top:0;
}
</style>


