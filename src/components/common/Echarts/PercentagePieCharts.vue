<template>
  <!-- 饼图 -->
  <div :id="chartsId" />
</template>
<script setup>
import { defineProps, inject, onMounted } from 'vue';
const echarts = inject("ec");
const props = defineProps({
  pieData: { // 来自父组件的titil和data
    type: Object
  },
  chartsId: { // 来自父组件的id名
    type: String
  }
});
onMounted(() => {
  chartsTpFunc(props.pieData);
});
var myChart;
function chartsTpFunc(row) {
  // 初始化之前先判断该实例是否存在，若存在，先销毁
  if (myChart != null && myChart != "" && myChart != undefined) {
    myChart.dispose();// 销毁
  }
  // 基于准备好的dom，初始化echarts实例
  myChart = echarts.init(document.getElementById(props.chartsId));
  const option = {
    title: {
      text: '{a|' + props.pieData.name + '}' + '\n\n{b|' + props.pieData.value + '}{c|%}',
      x: 'center',
      y: 'center',
      textStyle: {
        rich: {
          a: {
            fontSize: 14,
            color: '#929393',
            fontWeight: 'normal'
          },
          b: {
            fontSize: 16,
            color: '#0e1011',
            fontWeight: 'normal'
          },
          c: {
            fontSize: 16,
            color: '#0e1011',
            fontWeight: 'normal'
          }
        }
      }
    },
    series: [
      {
        name: '',
        type: 'pie',
        radius: ['75%', '65%'],
        silent: true,
        clockwise: true,
        startAngle: 90,
        label: {
          show: false
        },
        data: [
          {
            value: props.pieData.value,
            itemStyle: {
              normal: {
                // 外环发光
                borderWidth: 0.6,
                color: colorFun(props.pieData.value)
              }
            }
          },
          {
            value: 100 - props.pieData.value,
            label: {
              normal: {
                show: false
              }
            },
            itemStyle: {
              // 圆环底色
              normal: {
                color: "#f0f2f5"
              }
            },
            data: row.data
          }
        ]
      }
    ]
  };
  option && myChart.setOption(option);
  // 根据页面大小自动响应图表大小
  window.addEventListener("resize", function() {
    myChart.resize();
  });
}
function colorFun(val) {
  if (val >= 90) return '#fd4747';
  if (val < 60) return '#07b630';
  else return '#ff9900';
}
</script>
  
