<template>
  <!-- 云图 -->
  <div :id="chartsId" />
</template>
<script setup>
import { defineProps, inject } from 'vue';
import '@/utils/echarts-wordcloud';
const echarts = inject("ec");
const props = defineProps({
  chartData: { // 来自父组件的titil和data
    type: Object
  },
  chartsId: { // 来自父组件的id名
    type: String
  }
});
watch(() => props.chartData, (newVal) => {
  chartsTpFunc(newVal);
});
var myChart;
function chartsTpFunc(row) {
  // 初始化之前先判断该实例是否存在，若存在，先销毁
  if (myChart != null && myChart != "" && myChart != undefined) {
    myChart.dispose();// 销毁
  }
  // 基于准备好的dom，初始化echarts实例
  myChart = echarts.init(document.getElementById(props.chartsId));
  const option = {
    grid: {
      left: '0%',
      right: '4%',
      bottom: '3%',
      top: '10%',
      containLabel: true
    },
    tooltip: {
      show: true
    },
    series: [{
      type: 'wordCloud',
      gridSize: 2,
      sizeRange: [12, 25],
      rotationRange: [0, -30, 40],
      shape: 'pentagon',
      width: 650,
      height: 200,
      drawOutOfBound: true,
      textStyle: {
        color: function(row) {
          const color = ['#46A8F6', '#21BAD7', '#53AF4F', '#C1C834', '#F4BB1F', '#9E9E9E', '#9F887E', '#33B9F6', '#31A599', '#8EC149', '#FCD435', '#FC6E47', '#79919C', '#E55755'];
          return color[row.dataIndex];
        }
      },
      data: row.data && row.data.length > 0 ? row.data : [{ name: '暂无数据', value: 0 }]
    }]
  };
  option && myChart.setOption(option);
  // 根据页面大小自动响应图表大小
  window.addEventListener("resize", function() {
    myChart.resize();
  });
}
</script>
    
  
