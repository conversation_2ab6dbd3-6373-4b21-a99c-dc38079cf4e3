<template>
  <!-- 柱状图 -->
  <div :id="chartsId" />
</template>
<script setup>
import { defineProps, inject } from 'vue';
const echarts = inject("ec");
const props = defineProps({
  chartData: { // 来自父组件的titil和data
    type: Object
  },
  chartsId: { // 来自父组件的id名
    type: String
  }
});
watch(() => props.chartData, (newVal) => {
  chartsTpFunc(newVal);
});
var myChart;
function chartsTpFunc(row) {
  // 初始化之前先判断该实例是否存在，若存在，先销毁
  if (myChart != null && myChart != "" && myChart != undefined) {
    myChart.dispose();// 销毁
  }
  // 基于准备好的dom，初始化echarts实例
  myChart = echarts.init(document.getElementById(props.chartsId));
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      top: '12%',
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: row.xAxisData ? 'category' : 'value',
      minInterval: 1,
      data: row.xAxisData,
      axisLabel: {
        interval: 0,
        rotate: row.xAxisData && row.xAxisData.length > 5 ? 15 : 0, // 旋转角度，为0表示水平
        margin: 8
      }
    },
    yAxis: {
      type: row.yAxisData ? 'category' : 'value',
      minInterval: 1,
      data: row.yAxisData,
      axisLabel: {
        interval: 0
      }
    },
    series: row.data
  };
  option && myChart.setOption(option);
  // 根据页面大小自动响应图表大小
  window.addEventListener("resize", function() {
    myChart.resize();
  });
}
</script>
    
  
