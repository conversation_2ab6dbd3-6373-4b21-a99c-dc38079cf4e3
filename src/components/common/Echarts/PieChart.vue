<template>
  <!-- 饼图 -->
  <div :id="chartsId" />
</template>
<script setup>
import { defineProps, inject } from 'vue';
const echarts = inject("ec");
const props = defineProps({
  pieData: { // 来自父组件的titil和data
    type: Object
  },
  chartsId: { // 来自父组件的id名
    type: String
  }
});
watch(() => props.pieData, (newVal) => {
  chartsTpFunc(newVal);
});
var myChart;
function chartsTpFunc(row) {
  // 直径显示处理
  // const dataList = [];
  // let num = 10;
  // let itemOld = 0;
  // row.data.forEach(item => {
  //   if (itemOld > item.value) {
  //     num = (num - 5);
  //   } else if (itemOld < item.value) {
  //     num = (num + 5);
  //   }
  //   dataList.push({
  //     name: item.name,
  //     value: num
  //   });
  //   itemOld = item.value;
  // });

  // 初始化之前先判断该实例是否存在，若存在，先销毁
  if (myChart != null && myChart != "" && myChart != undefined) {
    myChart.dispose();// 销毁
  }
  // 基于准备好的dom，初始化echarts实例
  myChart = echarts.init(document.getElementById(props.chartsId));
  const option = {
    title: {
      text: row.title,
      left: row.titleLeft || 'left',
      top: row.titlePlace,
      textStyle: {
        color: '#303133',
        fontSize: 16,
        fontWeight: 'normal'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: (b) => {
        let nameMag = '';
        row.data.forEach(item => {
          if (item.name == b.name) {
            nameMag = b.marker + item.name + " <span style='font-weight:600;margin-left:10px;'>" + item.value + "</span> (" + (item.percent || 0) + "%)";
          }
        });
        return nameMag;
      }
    },
    legend: {
      type: 'scroll',
      top: 39,
      orient: 'vertical',
      left: row.legendLeft,
      formatter: (name) => {
        let title = '';
        row.data.map(item => {
          if (item.name == name) {
            title = name + '（' + parseFloat(item.value) + '）';
          }
        });
        return title;
      }
    },
    color: ['#46A8F6', '#21BAD7', '#53AF4F', '#C1C834', '#F4BB1F', '#9E9E9E', '#9F887E', '#33B9F6', '#31A599', '#8EC149', '#FCD435', '#FC6E47', '#79919C', '#E55755'],
    //  '#ED6193', '#B66BC7', '#9578CE', '#7987CD',
    series: [
      {
        // roseType: 'area',
        itemStyle: {
          borderRadius: [0, 5]
        },
        name: '',
        type: 'pie',
        minAngle: 20,
        radius: ['45%', '70%'],
        center: row.imgPlace,
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 14,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: row.data
      }
    ]
  };
  option && myChart.setOption(option);
  // 根据页面大小自动响应图表大小
  window.addEventListener("resize", function() {
    myChart.resize();
  });
}
</script>
