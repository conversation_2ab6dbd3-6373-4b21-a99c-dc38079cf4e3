<template>
  <div class="searchBox">
    <div class="searchCenter">
      <div class="messBox">
        <p v-if="showLeft">共
          <img v-if="isLoading" class="loadingGif" src="@/assets/imgs/loading.gif" />
          <span v-if="!isLoading" class="number">{{ checkObj.num1 }}</span>
          条, 含
          <img v-if="isLoading2" class="loadingGif" src="@/assets/imgs/loading.gif" />
          <span v-if="!isLoading2">{{ ipNum }}</span>
          个独立IP,
          <span class="time">用时
            <img v-if="isLoading" class="loadingGif" src="@/assets/imgs/loading.gif" />
            <span v-if="!isLoading">{{ checkObj.time }}</span>
            秒</span>
        </p>
        <slot name="left"></slot>
        <slot name="center"></slot>
        <div v-if="showRight" class="rigMenu">
          <p class="border">
            <zr-tooltip
              effect="dark"
              content="可查询'IP/域名+端口+传输协议'的多条扫描数据。"
              placement="bottom"
            >
              <span>全量数据</span>
            </zr-tooltip>
            <zr-tooltip
              effect="dark"
              content="可查询以'IP/域名+端口+传输协议'为主键的最新一条数据。"
              placement="bottom"
            >
              <span class="newData"><Icon name="zr-change" size="14" class="marginRig"></icon>最新数据</span>
            </zr-tooltip>
          </p>

          <p>
            <zr-tooltip
              effect="dark"
              content="开启后，将过滤掉400、401、502等状态码和无法解析的协议/端口数据。"
              placement="top"
            >
              <span>过滤无效请求</span>
            </zr-tooltip>
            <zr-switch v-model="value" class="switch" @change="handleChange" />

          </p>
          <slot></slot>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { watch } from 'vue';
const props = defineProps({
  tabActive: {
    type: String,
    default: 'service'
  },
  showLeft: {
    type: Boolean,
    default: false
  },
  showRight: {
    type: Boolean,
    default: false
  },
  ipNum: {
    type: Number,
    default: 0
  }
});
const value = ref(true);
const isLoading = ref(true);
const isLoading2 = ref(true);
const list = [
  { num1: '343,243', num2: '1,240,294', time: 2.256 },
  { num1: '943,843', num2: '736,257', time: 3.258 },
  { num1: '834,343', num2: '357,825', time: 3.172 },
  { num1: '8,430,085', num2: '136,287', time: 3.673 }
  
];
const checkObj = ref(list[0]);
const setTime = (time) => {
  setTimeout(() => {
    isLoading.value = false;
  }, time * 1000);
  setTimeout(() => {
    isLoading2.value = false;
  }, time * 1000 + 1000);
};
const loadLoading = (type) => { // 加载loading
  switch (type) {
    case 'service':
      checkObj.value = list[0];
      setTime(list[0].time);
      break;
    case 'host':
      checkObj.value = list[1];
      setTime(list[1].time);
      break;
    case 'product':
      checkObj.value = list[2];
      setTime(list[2].time);
      break;
    case 'hole':
      checkObj.value = list[3];
      setTime(list[3].time);
      break;
  }
};
watch(() => props.tabActive, (newVal) => {
  isLoading.value = true;
  isLoading2.value = true;
  // console.log(newVal);
  loadLoading(newVal);
}, {
  immediate: true
});

const handleChange = () => {
  
};
</script>

<style lang="scss" scoped>
$color1:#409eff;
.searchBox{
  width:'100%';
  background:#fff;
  // padding: 30px 0 20px;
}
:deep(.el-input-group__append) {
  cursor: pointer;
}
.messBox{
  font-size: 14px;
  color: #182131;
  // margin-top: 20px;
  // padding: 10px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .number{
    color: $color1;
  }
  .time{
    color: #e46b64;
  }
}
.newData{
  color:$color1;
}
.rigMenu{
  display: flex;
  align-items: center;
  p{
    cursor: pointer;
    color: #676f77;
  }
  & .el-tooltip__trigger{
    margin-left: 10px;
  }
  .border{
    padding-right:10px;
    border-right: 1px solid #eaedf2;
    margin-right:10px;
  }
  .switch{
    margin-left:10px;
  }
}
p{
  margin:0;
}
.marginRig{
  margin-right: 5px;
}
</style>
