<template>
  <div style="display: inline-block;">
    <zr-dropdown trigger="click">
      <template v-if="isSlot">
        <slot></slot>
      </template>
      <zr-link v-else :underline="false" type="primary">
        {{ textValue }}
      </zr-link>
      <template #dropdown>
        <zr-dropdown-menu>
          <zr-dropdown-item
            v-for="(item, key) in menuConfig"
            :key="key"
            @click.native="menuItemHandle(item)"
          >
            <Icon :name="item.icon" :size="item.iconSize || 14" /> {{ item.label }}
          </zr-dropdown-item>
        </zr-dropdown-menu>
      </template>
    </zr-dropdown>
    <Relation
      v-if="relationDialogVisible"
      v-model:relationDialogVisible="relationDialogVisible"
      :search-data="{
        value: textValue
      }"
    ></Relation>

    <!-- 详情的弹框 -->
    <zr-dialog
      v-model="detailsDialogVisible"
      title="详情"
      :fullscreen="true"
      class="relation-dialog-card"
      :destroy-on-close="true"
    >
      <zr-empty
        :image="require('@/assets/ikon/developing.png')"
        :image-size="500"
        description="全速开发中"
      />
    </zr-dialog>
  </div>
</template>
<script setup>
// 组件
import { ZrMessage } from 'qianji-ui';
import Relation from '@/components/common/Relation';
// 插件
import useClipboard from 'vue-clipboard3';
const { toClipboard } = useClipboard();
// 配置
const emits = defineEmits(['menuItemHandle']);
const props = defineProps({
  // 菜单项的配置
  menuConfig: {
    type: Object,
    default: () => {
      return {};
    }
  },
  // 操作的值
  textValue: {
    type: String,
    default: ''
  },
  // 是否是插槽模式
  isSlot: {
    type: Boolean,
    default: false
  }
});
// 操作
const relationDialogVisible = ref(false);
const detailsDialogVisible = ref(false);
function menuItemHandle(item) {
  // 如果是拷贝的就在里面进行处理
  // 通用的复制功能
  if (item.key === 'copy') {
    toClipboard(props.textValue);
    ZrMessage({
      message: '已复制到剪切板',
      type: 'success'
    });
  // 通用的关联分析功能
  } else if (item.key === 'relation') {
    setTimeout(() => {
      relationDialogVisible.value = true;
    }, 0);
  } else if (item.key === 'details') {
    setTimeout(() => {
      detailsDialogVisible.value = true;
    }, 0);
  } else {
    emits('menuItemHandle', {
      config: item,
      value: props.textValue
    });
  }
}
</script>
<style lang="scss" scoped>
:deep(.el-dropdown) {
  vertical-align: unset;
}
</style>
