<template>
  <div class="home-state-card">
    <img :src="require('@/assets/imgs/statBg.png')" alt="">
    <div class="home-state-card-content">
      <div class="home-state-card-num">
        <!-- <count-to
          :start-val="800"
          :end-val="value"
          :duration="2000"
        ></count-to> -->
        {{ simplifyNum(value) }}
      </div>
      <h5>{{ config.label }}</h5>
    </div>
  </div>
</template>
<script setup>
// 组件
import { CountTo } from 'vue3-count-to';
// 方法
import funs from '@/utils/funs';
const simplifyNum = funs.simplifyNum;
const props = defineProps({
  config: {
    type: Object,
    default: () => {
      return {};
    }
  },
  value: {
    type: Number,
    default: 0
  },
  loading: {
    type: Boolean,
    default: false
  }
});
</script>

<style lang="scss" scoped>
.home-state-card {
  position: relative;
  text-align: center;
  color: #368BEC;
  > img {
    width: 100%;
  }
  .home-state-card-content-loading {
    width: 30px;
  }
  .home-state-card-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
  }
  .home-state-card-num {
    font-family: pmzdb-TiTi;
    font-style: normal;
    font-weight: 400;
    font-size: 36px;
    line-height: 30px;
  }
  h5 {
    margin: 0;
    margin-top: 22px;
    font-style: normal;
    font-weight: 400;
    font-size: 24px;
    line-height: 25px;
    letter-spacing: 0.02em;
  }
}
</style>
