<template>
  <div class="main-header-wrap">
    <div class="main-header-content">
      <div class="logo" @click="handleToSearch()">
        <img src="@/assets/logo/logo_title_black.png" />
      </div>
      <div v-if="showInput" class="main-header-input">
        <SearchInput style="height: 32px; width: 480px" :show-search-button="false"></SearchInput>
      </div>
      <div class="main-header-action">
        <zr-button-group v-if="!hasLogin" style="margin-right: 15px">
          <zr-button type="primary" @click="handleLogin">登录</zr-button>
          <zr-button type="primary" @click="handleRegister">注册</zr-button>
        </zr-button-group>
        <zr-button v-if="hasLogin" type="primary" @click="handleLogout">退出</zr-button>
        <zr-button type="primary" @click="handleToEnterprise('dashboard')"> 企业入口 </zr-button>
      </div>
      <!-- 用户注册弹窗 -->
      <zr-dialog v-model="registerDialogVisible" width="450px" @close="closeDialog">
        <div class="title">用户注册</div>
        <zr-form ref="formRef" :rules="rules" :model="form" class="register-form">
          <zr-form-item prop="username">
            <zr-input v-model="form.username" :prefix-icon="UserFilled" autocomplete="off" placeholder="请输入用户名" />
          </zr-form-item>
          <zr-form-item prop="password">
            <zr-input v-model="form.password" :prefix-icon="Lock" type="password" placeholder="请输入密码" show-password />
          </zr-form-item>
          <zr-form-item prop="rePassword">
            <zr-input v-model="form.rePassword" :prefix-icon="Lock" type="password" placeholder="请再次输入密码" show-password />
          </zr-form-item>
          <zr-form-item prop="first_name">
            <zr-input v-model="form.first_name" :prefix-icon="User" placeholder="请输入显示名称" />
          </zr-form-item>
          <zr-form-item>
            <zr-button type="primary" style="width: 100%" :loading="submitLoading" @click="handleRegisterSubmit">注册账号</zr-button>
          </zr-form-item>
          <div class="tip">
            <span>已有账号,</span>
            <zr-button type="primary" link @click="handleLogin">直接登录</zr-button>
          </div>
        </zr-form>
      </zr-dialog>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router';
import SearchInput from '@/components/common/SearchInput.vue';
import { Iphone, UserFilled, Lock, User } from '@element-plus/icons-vue';
import { createUser, getRoleList } from '@/api/system';
import { ElMessage } from 'element-plus';
import { MD5Password, getLoginUrl } from '@/utils';

const router = useRouter();

const props = defineProps({
  showInput: {
    type: Boolean,
    default: true
  }
});

const generalUserRoleName = 'weixie_putong';
const enterpriseUserRoleName = 'weixie_qiye';
const registerDialogVisible = ref(false);
const formRef = ref(null);
const submitLoading = ref(false);
const form = reactive({
  username: '',
  password: '',
  rePassword: '',
  first_name: ''
});
const roleId = ref('');

const rules = reactive({
  username: [
    { required: true, message: '请输入用户名', trigger: ['change', 'blur'] },
    { min: 2, max: 25, message: '长度2-25', trigger: ['change', 'blur'] }
  ],
  password: [
    {
      validator: (rule, value, callback) => {
        console.log('validate');
        if (value === '') {
          callback(new Error('请输入密码！'));
        } else {
          if (form.rePassword !== '') {
            if (!formRef.value) return;
            formRef.value.validateField('rePassword', () => null);
          }
          callback();
        }
      },
      trigger: ['change', 'blur']
    }
  ],
  rePassword: [
    {
      validator: (rule, value, callback) => {
        console.log('validate repassword');
        if (value === '') {
          callback(new Error('请再次输入密码'));
        } else if (value !== form.password) {
          callback(new Error('两次输入的密码不一致！'));
        } else {
          callback();
        }
      },
      trigger: ['change', 'blur']
    }
  ],
  first_name: [
    {
      required: true,
      message: '请输入显示名称',
      trigger: ['change', 'blur']
    }
  ]
});

// 判断是否已经登录
const hasLogin = computed(() => {
  if (window.__POWERED_BY_QIANKUN__) {
    return window.$parentStore.state.user;
  }
  return false;
});

/** 是否是企业用户 */
function isEnterpriseUser() {
  const roles = window.$parentStore.state.user.roles || [];
  return roles.find(item => item.role_name === enterpriseUserRoleName);
}

// 注册
function handleRegister() {
  registerDialogVisible.value = true;
  queryRoleList();
}
// 登录
function handleLogin() {
  const loginUrl = getLoginUrl();
  console.log(loginUrl);
  if (loginUrl) {
    window.location.replace(loginUrl);
  }
}
// 跳转到搜索主界面
function handleToSearch() {
  router.push({ name: 'search' });
}
// 跳转到企业入口
function handleToEnterprise() {
  if (!hasLogin.value) {
    ElMessage({ type: 'warning', message: '当前未登录，请先登录！' });
  } else {
    if (isEnterpriseUser()) {
      router.push({ name: 'dashboard' });
    } else {
      ElMessageBox.alert('当前用户是普通用户，请联系管理员升级用户级别，解锁该功能!', '提醒', {
        confirmButtonText: '我知道了',
        callback: action => {}
      });
    }
  }
}
// 退出登录
function handleLogout() {
  ElMessageBox.confirm(`确认退出当前登录吗?`, '警告', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(res => {
    setTimeout(() => {
      ce.emit('logout');
    }, 200);
  });
}

/** 查询角色列表 */
function queryRoleList() {
  getRoleList(generalUserRoleName)
    .then(res => {
      if (res.data && res.data.length > 0) {
        const role = res.data.find(item => item.name === generalUserRoleName);
        roleId.value = role ? role.id : '';
      } else {
        ElMessage.error('角色列表获取失败，请稍后再试!');
      }
    })
    .catch(() => {
      ElMessage.error('角色列表获取失败，请稍后再试!');
    });
}

async function handleRegisterSubmit() {
  if (!formRef.value) return;
  await formRef.value.validate((valid, fields) => {
    if (valid) {
      if (!roleId.value) {
        return ElMessage({ type: 'error', message: '角色信息初始化失败，请稍后再试！' });
      }
      const data = {
        username: form.username,
        first_name: form.first_name,
        password: MD5Password(form.username, form.password),
        roles: [
          {
            id: roleId.value
          }
        ]
      };
      submitLoading.value = true;
      createUser(data)
        .then(res => {
          submitLoading.value = false;
          registerDialogVisible.value = true;
          ElMessage({ type: 'success', message: '注册成功' });
          reset();
          handleLogin();
        })
        .catch(() => {
          submitLoading.value = false;
        });
    }
  });
}

function closeDialog() {
  reset();
}

function reset() {
  formRef.value.resetFields();
}
</script>

<style lang="scss" scoped>
.main-header-wrap {
  width: 100%;
  height: 64px;
  background-color: #fff;
  border-bottom: 1px solid #e4e7ed;

  .main-header-content {
    width: 1200px;
    height: 100%;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .logo {
      display: inline-block;
      vertical-align: middle;
      margin-right: 84px;
      cursor: pointer;

      img {
        width: 96px;
        height: 48px;
      }
    }

    .main-header-input {
      flex: 1;
      text-align: left;
    }
  }
}

.title {
  text-align: center;
  font-weight: 400;
  color: #303133;
  font-size: 20px;
  line-height: 23px;
  margin-top: -25px;
  margin-bottom: 30px;
}

.tip {
  text-align: center;

  span:first-child {
    vertical-align: top;
  }
}

.register-form {
  padding: 0 20px;

  // :deep(.el-input--prefix .el-input__inner) {
  //   padding-left: 15px;
  // }
}
</style>
