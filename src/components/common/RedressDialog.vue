<template>
  <zr-dialog
    v-model="dialogTable"
    :close-on-click-modal="false"
    destroy-on-close
    :title="'纠正'+dialogRow.name+'字段的分类分级结果'"
    width="22%"
    @open="open"
    @close="cancelClick"
  >
    <zr-form
      :model="redressForm"
    >
      <zr-radio-group v-model="redressForm.redress">
        <zr-form-item>
          <zr-radio :label="1">使用自动识别的分类分级结果</zr-radio>
          <div :class="redressForm.redress==1?'radio-content':'radio-content auto'">
            <p>数据分类：用户个人信息</p>
            <p>数据分级：高</p>
          </div>
        </zr-form-item>
        <zr-form-item>
          <zr-radio :label="2">使用人工纠正的分类分级结果</zr-radio>
          <div class="radio-content">
            <zr-form-item label="数据分类">
              <zr-select
                v-model="redressForm.classify"
                :disabled="redressForm.redress!==2"
              >
                <zr-option
                  label="Option1"
                  value="1"
                />
              </zr-select>
            </zr-form-item>
            <zr-form-item label="数据分级">
              <zr-select
                v-model="redressForm.grading"
                :disabled="redressForm.redress!==6"
              >
                <zr-option
                  label="Option1"
                  value="1"
                />
              </zr-select>
            </zr-form-item>
          </div>
        </zr-form-item>
        <zr-form-item>
          <zr-radio :label="3">标记为非敏感数据</zr-radio>
        </zr-form-item>
      </zr-radio-group>
    </zr-form>
    <template #footer>
      <div style="flex: auto">
        <zr-button
          type="primary"
          size="default"
          @click="saveClick"
        >
          确定
        </zr-button>
        <zr-button
          size="default"
          @click="cancelClick"
        >
          取消
        </zr-button>
      </div>
    </template>
  </zr-dialog>
</template>
<script setup>
import { ref, defineProps, defineEmits } from 'vue';
  
const emits = defineEmits(['cancelClick', 'saveClick']);
const props = defineProps({
  dialogTableVisible: { // 弹窗打开状态值
    type: Boolean
  },
  dialogRow: { // 来自父组件的数据
    type: Object
  }
});
watch(() => props.dialogTableVisible, (newVal) => {
  dialogTable.value = newVal;
});
  
const dialogTable = ref(false);
const redressForm = ref({
  redress: 1
});
  
function open() { // 监听弹窗打开时
}
function saveClick() { // 保存

}
function cancelClick() { // 关闭
  emits('cancelClick');
}
  
</script>
  <style lang="scss" scoped>
    .radio-content{
      padding-left: 20px;
      &.auto{
        color: #919398;
      }
    }
  </style>
  
