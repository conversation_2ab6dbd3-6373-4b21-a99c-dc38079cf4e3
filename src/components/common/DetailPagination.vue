<template>
  <!-- 查看数据量限制分页组件 -->
  <div>
    <zr-pagination
      :current-page="searchForm.page"
      :page-size="searchForm.page_size"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, sizes, prev, pager, next"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>
<script setup>
import { defineEmits, defineProps } from 'vue';
import { ZrMessage } from "qianji-ui";

const emits = defineEmits(['handleSizeChange', 'handleCurrentChange']);
const props = defineProps({
  total: Number,
  searchForm: Object
});

function handleCurrentChange(val) {
  if (val * props.searchForm.page_size > 10000) {
    ZrMessage.warning('当前仅支持查看一万条数据');
    return;
  }
  emits('handleCurrentChange', val);
}
function handleSizeChange(val) {
  emits('handleSizeChange', val);
}
</script>
