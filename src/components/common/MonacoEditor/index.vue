<template>
  <!-- monacoEditor 编译器 -->
  <div class="monaco">
    <div
      ref="monacovvv"
      style="width: 100%; height: 100%;"
    />
  </div>
</template>
  
<script setup>
import { onMounted, ref, watch } from 'vue';
import * as monaco from 'monaco-editor';
const emit = defineEmits(['monacoValClick']);
var monacoEditor;
const monacovvv = ref();
onMounted(() => {
  init();
});

const props = defineProps({
  monacoVal: {
    type: String
  },
  readOnly: {
    type: Boolean
  }
});

const init = () => {
  monacoEditor = monaco.editor.create(monacovvv.value, {
    theme: 'vs',
    value: props.monacoVal || '',
    language: 'yaml',
    folding: true,
    foldingHighlight: true,
    foldingStrategy: 'indentation',
    showFoldingControls: 'always',
    disableLayerHinting: true,
    emptySelectionClipboard: false,
    selectionClipboard: false,
    automaticLayout: true,
    codeLens: false,
    scrollBeyondLastLine: false,
    colorDecorators: true,
    accessibilitySupport: 'off',
    lineNumbers: 'on',
    lineNumbersMinChars: 5,
    enableSplitViewResizing: false,
    readOnly: props.readOnly || false
  });
  monacoEditor.onDidChangeModelContent((val) => {
    emit('monacoValClick', monacoEditor.getValue());
  });
};
watch(() => props.monacoVal, (val) => {
  // init();
});
defineExpose({
  monacoEditor
});
</script>
  
<style lang="scss" scoped>
 .monaco{
 border: 1px solid #DCDFE6;
}
 </style>
  
