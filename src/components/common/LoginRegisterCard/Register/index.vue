<template>
  <div class="login-form-card">
    <zr-form :model="formData">
      <zr-form-item
        v-for="(item, key) in formConfig"
        :key="key"
      >
        <zr-input
          v-model="formData[item.key]"
          :placeholder="`${item.label}`"
          :show-password="item.key === 'password'"
          clearable
        >
          <template #prefix>
            <Icon :name="item.icon" />
          </template>
        </zr-input>
      </zr-form-item>
    </zr-form>
    <div class="login-card-handle">
      <zr-button type="primary" @click="loginHandle">注册</zr-button>
    </div>
  </div>
</template>
<script setup>
const emits = defineEmits(['change']);
const formData = reactive({
  username: '',
  email: '',
  phone: '',
  password: '',
  cPassword: ''
});
const formConfig = reactive([
  {
    label: '用户名',
    key: 'username',
    icon: 'zr-user'
  },
  {
    label: '邮箱',
    key: 'email',
    icon: 'zr-email-fill'
  },
  {
    label: '手机号',
    key: 'phone',
    icon: 'zr-phone-fill'
  },
  {
    label: '密码',
    key: 'password',
    icon: 'zr-lock-fill'
  },
  {
    label: '确认密码',
    key: 'cPassword',
    icon: 'zr-lock-fill'
  }
]);
function loginHandle() {
  emits('change', formData);
}
</script>
<style lang="scss" scoped>
.login-form-card {
  position: relative;
  width: 75%;
  height: 100%;
  margin: auto;
  .el-form {
    position: absolute;
    top: 3%;
    width: 100%;
  }
  .login-card-handle {
    position: absolute;
    bottom: 5%;
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
    .el-button {
      width: 100%;
    }
  }
  :deep(.el-input__wrapper) {
    height: 45px !important;
  }
}
</style>
