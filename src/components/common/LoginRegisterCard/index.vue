<template>
  <zr-tabs v-model="tabActiveName" class="zr-tabs">
    <zr-tab-pane
      v-for="(item, key) in tabConfig"
      :key="key"
      :label="item.label"
      :name="item.key"
    >
      <component :is="item.component" @change="componentChange"></component>
    </zr-tab-pane>
  </zr-tabs>
</template>
<script setup>
// 组件
import LogIn from './LogIn';
import Register from './Register';
const emits = defineEmits(['change']);
const props = defineProps({
  cardType: {
    type: String,
    default: 'logIn'
  }
});

const tabActiveName = ref('');
const tabConfig = reactive([
  {
    label: '登录',
    key: 'logIn',
    component: shallowRef(LogIn)
  },
  {
    label: '注册',
    key: 'register',
    component: shallowRef(Register)
  }
]);

// 初始化相关操作
init();
function init() {
  tabActiveName.value = props.cardType;
}
// 表单操作
function componentChange() {
  emits('change');
}
</script>
<style lang="scss" scoped>

:deep(.el-tabs__nav-wrap) {
  &:after {
    display: none;
  }
}
:deep(.el-tabs__nav) {
  transform: translateX(-50%) !important;
  margin-left: 50%;
}
.el-tab-pane {
  position: relative;
  height: 440px;
}
</style>
