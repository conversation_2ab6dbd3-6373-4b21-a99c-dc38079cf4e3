<template>
  <div class="login-form-card">
    <zr-form :model="formData">
      <zr-form-item
        v-for="(item, key) in formConfig"
        :key="key"
      >
        <zr-input
          v-model="formData[item.key]"
          :placeholder="`请输入${item.label}`"
          :show-password="item.key === 'password'"
          clearable
        >
          <template #prefix>
            <Icon :name="item.icon" />
          </template>
        </zr-input>
      </zr-form-item>
      <zr-form-item>
        <zr-checkbox v-model="formData.memory" label="记住登录信息" size="large" />
      </zr-form-item>
    </zr-form>
    <div class="login-card-handle">
      <zr-button type="primary" @click="loginHandle">登录</zr-button>
    </div>
  </div>
</template>
<script setup>
const emits = defineEmits(['change']);
const formData = reactive({
  username: '',
  password: '',
  memory: false
});
const formConfig = reactive([
  {
    label: '用户名',
    key: 'username',
    icon: 'zr-user'
  },
  {
    label: '密码',
    key: 'password',
    icon: 'zr-lock-fill'
  }
]);
function loginHandle() {
  emits('change', formData);
}
</script>
<style lang="scss" scoped>
.login-form-card {
  position: relative;
  width: 75%;
  height: 100%;
  margin: auto;
  .el-form {
    position: absolute;
    top: 15%;
    width: 100%;
  }
  .login-card-handle {
    position: absolute;
    bottom: 20%;
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
    .el-button {
      width: 100%;
    }
  }
  :deep(.el-input__wrapper) {
    height: 45px !important;
  }
}
</style>
