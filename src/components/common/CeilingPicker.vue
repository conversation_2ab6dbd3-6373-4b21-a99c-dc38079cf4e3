<!-- 吸顶时间组件 -->
<template>
  <div class="ceiling-picker">
    <div
      v-if="type.indexOf('select') !== -1"
      class="select"
    >
      <zr-select
        v-model="form.selectNum"
        size="default"
        @change="inquireChange"
      >
        <zr-option
          v-for="item in selectOption"
          :key="item.label"
          :label="item.label"
          :value="item.value"
        />
      </zr-select>
    </div>
    <div
      v-if="type.indexOf('time') !== -1"
      class="time"
    >
      <!-- <span><Icon name="zr-date" /> 日期时间：</span> -->
      <zr-date-picker
        v-model="form.time"
        :clearable="false"
        type="datetimerange"
        :default-time="defaultTime"
        :shortcuts="shortcuts"
        unlink-panels
        range-separator="至"
        start-placeholder="开始时间"
        end-placeholder="结束时间"
        value-format="x"
        @change="inquireChange"
      />
    </div>
  </div>
</template>
<script setup>
import { ref, defineEmits, onMounted } from 'vue';
const emits = defineEmits(['inquireChange']);
const props = defineProps({
  type: { // 需要使用哪些查询条件,目前支持 time、select
    type: Array
  },
  selectOption: { // 下拉选项
    type: Array
  },
  selectToler: { // 下拉默认选项
    type: String
  }
});
const formPicker = ref({});
const defaultTime = [ // default-time控制选中起始与结束日期时所使用的具体时刻
  new Date(2000, 1, 1, 0, 0, 0),
  new Date(2000, 2, 1, 23, 59, 59)
];
onMounted(() => {
  formPicker.value = sessionStorage.getItem('formPicker');
  // const startDate = new Date(new Date().toLocaleDateString()).getTime();
  const start = new Date(new Date().setHours(0, 0, 0, 0));
  start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
  const startDate = start.getTime();
  const endDate = new Date(new Date().setHours(23, 59, 59, 59)).getTime();
  form.value.time = formPicker.value ? JSON.parse(formPicker.value).time : [startDate, endDate];
});
onUpdated(() => {
  form.value.selectNum = props.selectToler;
});
const form = ref({
  selectNum: '',
  time: ''
});


const shortcuts = [
  {
    text: '最近7天',
    value: () => {
      const end = new Date(new Date().setHours(23, 59, 59, 59));
      const start = new Date(new Date().setHours(0, 0, 0, 0));
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    }
  },
  {
    text: '最近30天',
    value: () => {
      const end = new Date(new Date().setHours(23, 59, 59, 59));
      const start = new Date(new Date().setHours(0, 0, 0, 0));
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    }
  },
  {
    text: '最近90天',
    value: () => {
      const end = new Date(new Date().setHours(23, 59, 59, 59));
      const start = new Date(new Date().setHours(0, 0, 0, 0));
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
      return [start, end];
    }
  },
  {
    text: '重置',
    value: () => {
      const end = new Date(new Date().setHours(23, 59, 59, 59));
      const start = new Date(new Date().setHours(0, 0, 0, 0));
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    }
  }

];
function inquireChange() {
  sessionStorage.setItem('formPicker', JSON.stringify(form.value));
  emits('inquireChange', form.value);
}
</script>
<style lang="scss" scoped>
  .ceiling-picker{
    // padding-left: 20px;
    // position: fixed;
    // height: 35px;
    // top: 57px;
    // right: 60px;
    // border-radius: 0 0 5px 5px;
    // z-index: 2;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    >div{
      margin: 0 0 12px 10px;
      // background: #79bbff;
    }
  }
  // :deep(.el-select .el-input__wrapper) {
  //   background: rgba(121, 187, 255, 0);
  //   border:none
  // }
  // :deep(.el-input--prefix .el-input__inner){
  //   color: #fff;
  // }
  // :deep(.el-icon svg){
  //   color: #fff;
  // }
  // :deep(.el-range-editor.el-input__wrapper){
  //   border: none !important;
  //   box-shadow: none !important;
  //   background: rgba(121, 187, 255, 0);
  // }
  // :deep(.el-date-editor .el-range-input){
  //   color: #fff;
  // }
  // :deep(.el-date-editor .el-range-separator){
  //   color: #fff;
  // }
  // :deep(.el-input__inner::placeholder) {
  //   color: #fff;
  // }

</style>
