<!-- tab标签页组件 -->
<template>
  <zr-tabs
    v-model="activeName"
    class="zr-tabs"
    @tab-click="handleClick"
  >
    <zr-tab-pane
      v-for="item in option"
      :key="item.name"
      :label="item.label"
      :name="item.name"
    >
      <template #label>
        <span class="custom-tabs-label">
          <p>{{ item.label }}</p>
        </span>
      </template>
    </zr-tab-pane>
  </zr-tabs>
</template>
<script setup>
import { ref, defineProps, defineEmits } from 'vue';

const emits = defineEmits(['handleClick']);
const props = defineProps({
  option: { // 来自父组件的tab选项，包含label和name
    type: Array
  }
});
const activeName = ref('1');

function handleClick() {
  nextTick(() => {
    emits('handleClick', activeName.value);
  });
}
</script>
<style lang="scss" scoped>
.custom-tabs-label{
    p{
        padding: 0 30px;
    }
}
:deep(.el-tabs__item){
    padding: 0 !important;
}
:deep(.el-tabs__header){
  margin-bottom: 20px;
}
:deep(.el-tabs__item){
  font-weight: 700;
}
</style>
