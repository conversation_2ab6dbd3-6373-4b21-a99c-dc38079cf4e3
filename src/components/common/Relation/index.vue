<template>
  <zr-dialog
    v-model="dialogVisible"
    title="关联分析"
    :fullscreen="true"
    class="relation-dialog-card"
    :destroy-on-close="true"
  >
    <SearchDetail
      v-if="dialogVisible"
      card-type="dialog"
      :p-search-data="searchData"
    ></SearchDetail>
  </zr-dialog>
</template>
<script setup>
// 组件
import SearchDetail from '@/views/searchDetail';
// 插件
import { useVModel } from '@vueuse/core';
const props = defineProps({
  relationDialogVisible: {
    type: Boolean,
    default: false
  },
  searchData: {
    type: Object,
    default: () => {
      return {};
    }
  }
});
const emits = defineEmits(['update:relationDialogVisible']);
const dialogVisible = useVModel(props, 'relationDialogVisible', emits);
</script>
<style lang="scss">
.el-overlay {
  .el-overlay-dialog{
    margin: 10px !important;
  }
}
</style>
