<template>
  <zr-dialog
    v-model="dialogVisible"
    custom-class="ccc_dialog"
    :title="title"
    :close-on-click-modal="false"
    :width="w ? w + 'px' : '750px'"
    destroy-on-close
    append-to-body
    @close="closeMyself"
  >
    <slot />
    <template #footer>
      <div
        v-if="!hideFoot"
        class="dialog-footer"
      >
        <zr-button @click="closeMyself">取 消</zr-button>
        <zr-button
          v-if="!disableConfirm"
          type="primary"
          :loading="submitLoading"
          @click="confirmEvent"
        >{{ confirmText }}</zr-button>
      </div>
    </template>
  </zr-dialog>
</template>

<script>
import { $emit } from '../../utils/gogocodeTransfer';
export default {
  props: {
    visible: Boolean,
    title: String,
    hideFoot: Boolean,
    loading: Boolean,
    submitLoading: Boolean,
    confirmText: {
      type: String,
      default: '确 定'
    },
    disableConfirm: <PERSON><PERSON><PERSON>,
    w: [Number, String]
  },
  emits: ['update:visible', 'closeEvent', 'close', 'confirmEvent', 'confirm'],
  data() {
    return {
      dialogVisible: false
    };
  },
  watch: {
    visible() {
      this.dialogVisible = this.visible;
    }
  },
  mounted() {},
  methods: {
    // 对话框关闭事件的回调
    closeMyself() {
      this.dialogVisible = false;
      $emit(this, 'closeEvent');
      $emit(this, 'close');
      $emit(this, 'update:visible', false);
    },
    confirmEvent() {
      $emit(this, 'confirmEvent');
      $emit(this, 'confirm');
    }
  }
};
</script>

<style lang="scss" scoped>
.ccc_dialog {
    height: auto;
    max-height: 95%;
    margin-top: 0px !important;
    overflow: auto;
    position: relative;
    top: 50%;
    transform: translate(0, -50%);
    border-radius: 2px;
    box-sizing: border-box;
    padding: 0px 0;
    .el-dialog__header {
      position: sticky;
      top: 0;
      width: 100%;
      height: 60px;
      box-sizing: border-box;
      background-color: white;
      padding-top: 10px;
      margin-right: 0;
      z-index: 10000;
    }
    .el-dialog__body {
      padding: 10px 20px 0px 20px;
    }
    .el-dialog__footer {
      position: sticky !important;
      bottom: 0;
      padding-bottom: 10px;
      width: 100%;
      height: 60px;
      box-sizing: border-box;
      background-color: white;
      z-index: 10000;
    }
  

  .el-cascader {
    width: 100%;
  }
}
</style>
