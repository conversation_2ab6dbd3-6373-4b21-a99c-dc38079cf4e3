<template>
  <div class="search-handle-card">
    <h3>图标搜索</h3>
    <zr-alert title="使用图片搜索，而不限于文本" type="warning" show-icon :closable="false" />
    <zr-upload
      action="https://run.mocky.io/v3/9d059bf9-4660-45f2-925d-ce80ad6c4d15"
      :show-file-list="false"
      drag
      multiple
      :on-change="uploadChange"
    >
      <template #trigger>
        <Icon name="zr-cloud-upload-fill-b" class="zr-circle-picture"></Icon>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
      </template>
    </zr-upload>
  </div>
</template>
<script setup>
const searchData = reactive({
  value: ''
});
const emits = defineEmits(['search']);
function uploadChange() {
  searchData.value = '测试';
  emits('search', searchData);
}
</script>
<style lang="scss" scoped>
.search-handle-card {
  h3 {
    margin: 0;
  }
  .el-alert {
    margin-bottom: 5px;
  }
  :deep(.el-upload) {
    :deep(.el-upload-dragger) {
      padding: 5px 10px;
    }
    .el-icon {
      font-size: 30px;
    }
  }
}
</style>
