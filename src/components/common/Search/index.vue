<template>
  <div :class="`search-input-wrap search-input-wrap-${styleType}`">
    <zr-input
      v-model.trim="searchData.value"
      placeholder="port:'8080' OR service:'http'"
      @keyup.enter="search"
    >
      <template #prefix>
        <!-- <i :class="'search-logo-' + size"></i> -->
        <Icon name="zr-search" />
      </template>
      <template #suffix>
        <div class="search-input-wrap-handle">
          <zr-popover
            v-for="(item, key) in btnConfig"
            :key="key"
            :ref="popRefList.set"
            placement="bottom-end"
            trigger="click"
            :width="item.width"
            :visible.sync="item.popoverVisible"
          >
            <div class="popover-content-card" style="position: relative;">
              <Icon
                class="popover-content-card-close"
                style="position: absolute;right: 0px;cursor: pointer;"
                name="zr-circle-close-fill"
                size="20"
                @click="closePopover"
              />
              <component
                :is="item.component"
                :ref="item.key"
                :key="popoverKey"
                @search="handleSearch"
                @updateSearchValue="updateSearchValue"
                @cancelSarching="cancelSarching"
              ></component>
            </div>
            <template #reference>
              <span>
                <zr-tooltip
                  class="box-item"
                  :effect="styleType === 'home' ? 'light' : 'dark'"
                  :content="item.label"
                  placement="top"
                >
                  <Icon
                    :name="item.icon"
                    :size="iconSize"
                    @click="openPopover(item)"
                  />
                </zr-tooltip>
              </span>
            </template>
          </zr-popover>
        </div>
      </template>
    </zr-input>
    <zr-button class="search-input-wrap-button" type="primary" @click="search()">搜索</zr-button>
  </div>
</template>

<script setup>
// 组件
import { ZrPopover } from 'qianji-ui';
import Batch from './Batch';
import Keyword from './Keyword';
import Picture from './Picture';
import Product from './Product';
// 插件
import { useTemplateRefsList } from '@vueuse/core';
const props = defineProps({
  styleType: {
    type: String,
    default: 'default'
  },
  echoSearch: {
    type: Object,
    default: () => {
      return {};
    }
  }
});
const emits = defineEmits(['search']);
// btn配置
const btnConfig = reactive([
  {
    label: 'IP/域名批量搜索',
    icon: 'zr-project-a',
    key: 'batch',
    component: shallowRef(Batch),
    width: 400,
    popoverVisible: false
  },
  {
    label: '使用关键词搜索',
    icon: 'zr-form',
    key: 'keyword',
    component: shallowRef(Keyword),
    width: 900,
    popoverVisible: false
  },
  {
    label: '使用网页图标搜索',
    icon: 'zr-circle-picture',
    key: 'icon',
    component: shallowRef(Picture),
    width: 300,
    popoverVisible: false
  },
  {
    label: '产品搜索',
    icon: 'zr-server-fill',
    key: 'product',
    component: shallowRef(Product),
    width: 1000,
    popoverVisible: false
  }
]);
const iconSize = ref('20');
// 搜索配置
let searchData = reactive({
  value: ''
});
// 监听
watch(() => props.echoSearch, (value) => {
  handleSearch(value);
}, {
  deep: true
});


init();
// --------- 初始化 ----------
function initSearch() {
  if (JSON.stringify(props.echoSearch) !== '{}') {
    searchData = props.echoSearch;
  }
}

function init() {
  initSearch();
}

// --------- 操作相关功能 -------------
// 获取谈框的ref
const popRefList = useTemplateRefsList();
// 搜索
function search() {
  emits('search', searchData);
}
// 关闭card操作
const popoverKey = ref(0);
function closePopover() {
  popoverKey.value += 1;
  btnConfig.forEach(item => {
    item.popoverVisible = false;
  });
}
// 操作触发搜索
function handleSearch(handleSearchData) {
  for (const key in handleSearchData) {
    searchData[key] = handleSearchData[key];
  }
  closePopover();
  emits('search', searchData);
}

// 打开操作card
function openPopover(item) {
  btnConfig.forEach(fItem => {
    if (fItem.key === item.key) {
      fItem.popoverVisible = true;
    } else {
      fItem.popoverVisible = false;
    }
  });
}
// 同步拼接的文字
function updateSearchValue(item) {
  searchData.value = item.value;
}
function cancelSarching() {
  closePopover();
}
</script>

<style lang="scss" scoped>
$searchHomeBgColor: rgba(40, 64, 114, 0.6);
$searchHomeFontColor:  #0185FF;
.search-input-wrap {
  height: 46px;
  display: flex;
  .el-input {
    :deep(.el-input__wrapper) {
      .search-input-wrap-handle {
        .el-tooltip__trigger {
          display: inline-block;
          margin-right: 5px;
        }
        .el-icon {
          cursor: pointer;
        }
      }
    }
  }
  .search-input-wrap-handle {
  }
  .search-input-wrap-button {
    display: inline-block;
    width: 100px;
    height: 100%;
    border-radius: 0 4px 4px 0;
    margin-left: -3px;
  }
  &.search-input-wrap-default {
  }
  &.search-input-wrap-home {
    .el-input {
      background-color: $searchHomeBgColor;
      :deep(.el-input__wrapper) {
        border: 1px solid #0185FF;
        background-color: $searchHomeBgColor;
        box-shadow: none !important;
        .el-input__inner {
          color: #fff;
          &::placeholder {
            color: $searchHomeFontColor !important;
          }
        }
        .search-input-wrap-handle {
          .el-icon {
            cursor: pointer;
          }
        }
        .el-icon {
          font-size: 20px;
          color: $searchHomeFontColor;
          vertical-align: middle;
        }
      }
    }
  }
}
</style>
