<template>
  <div class="search-handle-card">
    <h3>关键词搜索</h3>
    <div class="search-handle-card-table">
      <zr-table
        :data="tableData"
        :header-cell-style="{ 'text-align': 'center' }"
        height="100%"
      >
        <zr-table-column
          v-for="(item, key) in tableConfig"
          :key="key"
          :label="item.label"
          align="center"
        >
          <template #default="{ $index }">
            <template v-if="item.type === 'select'">
              <zr-select
                v-model="tableData[$index][item.key]"
                :teleported="true"
                filterable
                :options="item.options"
                :disabled="item.disabled ? item.disabled($index, tableData) : false"
                optiongroup
              > </zr-select>
            </template>
            <template v-else-if="item.type === 'input'">
              <zr-input v-model="tableData[$index][item.key]"></zr-input>
            </template>
          </template>
        </zr-table-column>
        <zr-table-column width="120" label="操作" align="center">
          <template #default="{ $index }">
            <zr-button
              size="small"
              type="success"
              left-icon="zr-add"
              circle
              @click="addTableItemData($index)"
            ></zr-button>
            <zr-button
              v-if="tableData.length !== 1"
              size="small"
              type="danger"
              left-icon="zr-delete-fill"
              circle
              @click="delTableDataItem($index)"
            ></zr-button>
          </template>
        </zr-table-column>
      </zr-table>
    </div>
    <div class="search-handle-card-keyword">
      <zr-tag size="large" effect="dark" type="info" round @click.native="searching">
        <template v-if="searchValue === ''">
          请在上方配置语句
        </template>
        <template v-else>
          过滤器语句：{{ searchValue }}
        </template>
      </zr-tag>
    </div>
    <div class="search-handle-card-handle">
      <!-- <div class="search-handle-card-handle-text">
        <template v-if="searchValue !== ''">
          过滤器语句： {{ searchValue }}
        </template>
      </div> -->
      <zr-button type="primary" @click="searching">检索</zr-button>
      <zr-button type="info" @click="cancelSarching">取消</zr-button>
    </div>
  </div>
</template>
<script setup>
// 配置
import { keywordOptionMap, logicOption } from '@/utils/map';
// const keywordOptionMapList = reactive(keywordOptionMap);
const emits = defineEmits(['search', 'updateSearchValue', 'cancelSarching']);
const tableConfig = reactive([
  {
    label: '关键字',
    key: 'keyword',
    type: 'select',
    options: keywordOptionMap
  },
  {
    label: '值',
    type: 'input',
    key: 'value'
  },
  {
    label: '查询逻辑',
    type: 'select',
    key: 'logic',
    options: logicOption,
    disabled: function(index, tableData) {
      return (index + 1) === tableData.length;
    }
  }
]);
// 数据
const tableData = reactive([]);
const tableItemDataConfig = {
  keyword: '',
  value: '',
  logic: ''
};

init();
// ------- 初始化操作 ---------
function initTableData() {
  tableData.push(reactive(JSON.parse(JSON.stringify(tableItemDataConfig))));
}

// 添加
function addTableItemData(index) {
  tableData.splice(index + 1, 0, reactive(JSON.parse(JSON.stringify(tableItemDataConfig))));
}

// 删除
function delTableDataItem(index) {
  tableData.splice(index, 1);
}

// 搜索
const searchValue = computed(() => {
  let str = '';
  tableData.forEach((item, key) => {
    // 如果有值才
    if (item.value !== '') {
      let value = `${item.keyword} = '${item.value}'`;
      if ((key + 1) !== tableData.length) {
        value += ` ${item.logic} `;
      }
      str += value;
    }
  });
  return str;
});

watch(searchValue, (newValue) => {
  // emits('updateSearchValue', {
  //   value: newValue
  // });
});

function searching() {
  emits('search', {
    value: searchValue
  });
}

function init() {
  initTableData();
}

// 取消面板
function cancelSarching() {
  emits('cancelSarching');
}
</script>
<style lang="scss" scoped>
.search-handle-card {
  height: 500px;
  h3 {
    margin: 0;
  }
  .search-handle-card-table {
    height: calc(100% - 180px);
  }
  .search-handle-card-keyword {
    margin: 10px 0;
    padding: 40px;
    border-bottom: 1px solid #e1e1e1;
    text-align: center;
    .el-tag {
      cursor: pointer;
      background: #4d4d4d;
      border: none;
      font-size: 14px;
    }
  }
  .search-handle-card-handle {
    // margin-top: 10px;
    text-align: right;
    // display: flex;
    // justify-content: space-between;
    // padding-top: 10px;
    // .search-handle-card-handle-text {
    //   // vertical-align: middle;
    //   line-height: 30px;
    // }
  }
}
</style>
