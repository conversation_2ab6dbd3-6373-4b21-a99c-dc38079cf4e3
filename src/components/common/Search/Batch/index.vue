<template>
  <div class="search-handle-card">
    <h3>IP/域名批量搜索</h3>
    <zr-alert title="上传包含IP/域名的.txt文件，数量不超过1000个" type="warning" show-icon :closable="false" />
    <zr-upload
      action="https://run.mocky.io/v3/9d059bf9-4660-45f2-925d-ce80ad6c4d15"
      :show-file-list="false"
      drag
      multiple
      :on-change="uploadChange"
    >
      <template #trigger>
        <Icon name="zr-cloud-upload-fill-b" class="el-icon--upload"></Icon>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
      </template>
    </zr-upload>
    <zr-input
      v-model="searchData.value"
      placeholder="请输入IP/域名，每行一个，多个请换行输入， 最多不超过1000个"
      show-word-limit
      :rows="4"
      type="textarea"
    />
    <div class="search-handle-card-handle">
      <zr-button type="primary" @click="searching">检索</zr-button>
    </div>
  </div>
</template>
<script setup>
const searchData = reactive({
  value: ''
});
const emits = defineEmits(['search']);
// 搜索
function searching() {
  emits('search', searchData);
}
// 文件上传解析
function uploadChange() {
  searchData.value = '测试';
  searching();
}
</script>
<style lang="scss" scoped>
.search-handle-card {
  h3 {
    margin: 0;
  }
  .el-alert {
    margin-bottom: 5px;
  }
  :deep(.el-upload) {
    margin-bottom: 10px;
    :deep(.el-upload-dragger) {
      padding: 5px 10px;
    }
    .el-icon--upload {
      font-size: 30px;
    }
  }
  .search-handle-card-handle {
    margin-top: 10px;
    text-align: right;
  }
}
</style>
