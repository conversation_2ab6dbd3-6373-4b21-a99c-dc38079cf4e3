<template>
  <div class="search-handle-card">
    <h3>产品搜索</h3>
    <zr-table
      :data="tableData"
      class="search-handle-card-table"
      @row-click="tableRowClick"
    >
      <zr-table-column
        v-for="(item, key) in tableConfig"
        :key="key"
        :prop="item.key"
        :label="item.label"
        :width="item.colWidth || ''"
      >
        <template #default="{ row }">
          <template v-if="item.type">
            <zr-tag type="danger" effect="dark" round>
              {{ row[item.key] }}
            </zr-tag>
          </template>
          <template v-else>
            {{ row[item.key] }}
          </template>
        </template>
      </zr-table-column>
    </zr-table>
  </div>
</template>
<script setup>
const emits = defineEmits(['search']);
// 配置
const tableConfig = reactive([
  {
    label: '产品名称',
    key: 'product_name'
  },
  {
    label: '厂商名称',
    colWidth: '300',
    key: 'vendor_name'
  },
  {
    label: '测绘数据量',
    key: 'ip_count'
  },
  {
    label: '关联漏洞数',
    type: 'tag',
    key: 'vul_count'
  }
]);
const tableData = reactive([{
  'is_new': false,
  'product_name': 'Ubuntu',
  'vul_count': 0,
  'vendor_name': 'Canonical Ltd.',
  'ip_count': 173439030,
  'is_competitive': true,
  'is_hot': false
}, {
  'is_new': false,
  'product_name': 'Unix',
  'vul_count': 0,
  'vendor_name': 'The Open Group',
  'ip_count': 24493914,
  'is_competitive': true,
  'is_hot': false
}, {
  'is_new': false,
  'product_name': 'uc-httpd',
  'vul_count': 0,
  'vendor_name': 'uc-httpd',
  'ip_count': 16322210,
  'is_competitive': false,
  'is_hot': false
}, {
  'is_new': false,
  'product_name': 'UPnP',
  'vul_count': 2,
  'vendor_name': 'UPnP',
  'ip_count': 10825886,
  'is_competitive': false,
  'is_hot': false
}, {
  'is_new': false,
  'product_name': 'Ubiquiti AirOS\u8def\u7531\u5668',
  'vul_count': 0,
  'vendor_name': 'Ubiquiti Networks\u7f8e\u56fd\u4f18\u6bd4\u5feb\u79d1\u6280\u6709\u9650\u516c\u53f8',
  'ip_count': 6223366,
  'is_competitive': true,
  'is_hot': false
}, {
  'is_new': false,
  'product_name': 'uc-httpd-1.0.0',
  'vul_count': 0,
  'vendor_name': '-',
  'ip_count': 4291338,
  'is_competitive': false,
  'is_hot': false
}, {
  'is_new': false,
  'product_name': 'Ubiquiti Networks_EdgeOS',
  'vul_count': 0,
  'vendor_name': 'Ubiquiti Networks\u7f8e\u56fd\u4f18\u6bd4\u5feb\u79d1\u6280\u6709\u9650\u516c\u53f8',
  'ip_count': 1770925,
  'is_competitive': false,
  'is_hot': false
}, {
  'is_new': false,
  'product_name': 'UniFi',
  'vul_count': 0,
  'vendor_name': '-',
  'ip_count': 1690726,
  'is_competitive': false,
  'is_hot': false
}]);
// 行点击
function tableRowClick(row) {
  emits('search', {
    value: row.product_name
  });
}
</script>
<style lang="scss" scoped>
.search-handle-card-table {
  :deep(.el-table__header) {
    .el-table__cell {
      background-color: #fff !important;
      .cell {
        color: #bcbcbc;
      }
    }
  }
  :deep(.el-table__body) {
    .el-table__row {
      cursor: pointer;
    }
  }
  :deep(.el-table__cell) {
    border-bottom: none !important;
  }
}
</style>
