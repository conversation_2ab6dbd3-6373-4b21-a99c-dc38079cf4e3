<template>
  <div class="">
    <zr-tabs v-model="activeName" class="demo-tabs">
      <zr-tab-pane label="端口响应" name="first">
        <div class="contentMess">{{ detail.response }}</div>
      </zr-tab-pane>
      <zr-tab-pane v-if="detail.cert" label="ssl证书" name="second">
        <div class="contentMess">{{ detail.cert }}</div>
      </zr-tab-pane>
      <zr-tab-pane label="http" name="third">
        <!-- <vue-json-editor
          v-model="resultInfo"
          :show-btns="false"
        :mode="'code'"
        /> -->
        {{ detail.body }}
      </zr-tab-pane>
    </zr-tabs>
  </div>
</template>
<script setup>
// import vueJsonEditor from 'vue-json-editor';

defineProps(['detail']);

const activeName = ref('first');
// json数据
const resultInfo = reactive({
  'employees': [
    {
      'firstName': '<PERSON>',
      'lastName': 'Gates'
    },
    {
      'firstName': '<PERSON>',
      'lastName': 'Bush'
    },
    {
      'firstName': '<PERSON>',
      'lastName': '<PERSON>'
    }
  ]
});
</script>

<style lang="scss" scoped>
:deep(.el-tabs__header){
  padding-left: 16px;
  padding-right: 20px;
  margin-bottom:0;
}
:deep(.el-tabs__nav-wrap::after){
  background: transparent;
}
:deep(.el-tabs__item){
  color:#a3a9b3;
}
:deep(.el-tabs__item.is-active){
  color:#1890FF;
}
:deep(.el-tabs){
  height:100%;
}
:deep(.el-tabs__content){
  height: calc(100% - 50px);
  padding: 12px 20px 0px 16px;
  box-sizing:border-box;
  font-size: 14px;
  color: #676f77;
}
:deep(.el-tabs__content){
  background: rgba(234,237,242,.3);
  .contentMess{
    height: 200px;
    word-break: break-all;
    overflow: auto;
  }
}

</style>
