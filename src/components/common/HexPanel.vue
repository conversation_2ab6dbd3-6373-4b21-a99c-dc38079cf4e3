<template>
  <div>
    <div class="hex-panel-box">
      <div class="content-box">
        <div class="header-box">
          <div class="header-offset">OFFSET</div>
          <div class="header-hex">
            <span
              v-for="item in hexTabel"
              :key="item"
              class="hex-word"
            >
              {{ item }}
            </span>
          </div>
          <div class="header-bin">ANSI ASCII</div>
          <!-- <div class="header-scroll" /> -->
        </div>
        <div
          class="body-box"
        >
          <div
            class="body-inner-box"
            style="overflow: auto;"
          >
            <div
              class="body-offset"
            >
              <div
                v-for="(value,key) in hexList.data"
                :key="key"
              >{{ value.line }}</div>
            </div>
            <div
              v-if="hexList.data&&hexList.data.length>0"
              class="body-hex"
            >
              <div
                v-for="(value,key) in hexList.data"
                :key="key"
                class="row hex-row"
              >
                <span
                  v-for="(data,index) in value.hex"
                  :key="index"
                  class="hex-word"
                  :class="[{active:isHighLightWord(hexList,index,key)}]"
                >{{ data }}</span>
              </div>
            </div>
            <div
              v-else
              class="body-hex"
            >
              <p class="tol-msg">选择列表项查看详情</p>
            </div>
            <div class="body-bin">
              <div
                v-for="(value,key) in hexList.data"
                :key="key"
                class="row bin-row"
              >
                <span
                  v-for="(data,index) in value.hex"
                  :key="index"
                  class="bin-word"
                  :class="[{active:isHighLightWord(hexList,index,key)}]"
                >{{ hex2bin(data) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
  
<script setup>
import {} from 'vue';
const props = defineProps({
  hexData: Object
});

const hexList = ref({});
const hexTabel = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F'];

watch(() => props.hexData, (newVal) => {
  hexList.value = newVal || {};
});
/*
      * 判断是否为高亮字节
      * */
function isHighLightWord(data, offset, key) {
  var num = (key * 16) + offset;
  return num >= data.start_offset && num < data.end_offset;
}
/*
      * 十六进制转换二进制
      * */
function hex2bin(hex) {
  return String.fromCharCode(parseInt(hex, 16));
}
</script>
  
  <style scoped>
  /* * {
    margin: 0;
    padding: 0; */
  /* } */
  html, body {
    width: 100%;
    height: 100%;
    padding: 20px;
    box-sizing: border-box;
    min-width: 1366px;
    overflow: auto;
  }
  
  .hex-panel-box {
    font-size: 14px;
    /*等宽字符*/
    font-family: Consolas, Monaco, monospace;
    width: 669px;
    display: flex;
    /* flex-direction: column; */
    margin: 0 auto;
    border-radius: 2px;
    --border-color: #e3e3e3;
  }
  
  .hex-panel-box .control-box {
    padding: 5px 0;
  }
  
  .hex-panel-box .control-box>*{
    display: inline-block;
    vertical-align: middle;
  }
  
  .hex-panel-box .content-box {
    border: 1px solid var(--border-color);
  }
  
  .hex-panel-box .header-box, .hex-panel-box .body-box {
    display: flex;
    flex-direction: row;
  }
  
  .hex-panel-box .header-box {
    height: 40px;
    line-height: 40px;
    background: #f1f1f1;
  }
  
  .hex-panel-box .alert-box {
    text-align: center;
    flex-direction: row;
    font-family: 微软雅黑, "Raleway", sans-serif;
    padding: 5px 0;
    color: #409eff;
    font-size: 13px;
  }
  
  .hex-panel-box .header-box > div {
    text-align: center;
    box-sizing: border-box;
  }
  
  .hex-panel-box .body-box {
    height: 555px;
    overflow: auto;
  }
  
  .hex-panel-box .body-box .body-inner-box {
    width: 100%;
    height: auto;
  }
  
  .hex-panel-box .body-box .body-inner-box > div {
    float: left;
    height: auto;
    min-height: 555px;
    line-height: 25px;
    box-sizing: border-box;
  }
  
  .hex-panel-box .header-box .header-offset {
    width: 175px;
    border-right: 1px solid var(--border-color);
    border-bottom: 1px solid var(--border-color);
    color: #409eff;
  }
  
  .hex-panel-box .body-box .body-inner-box .body-offset {
    width: 175px;
    padding: 10px;
    border-right: 1px solid var(--border-color);
    text-align: center;
    color: #409eff;
  }
  
  .hex-panel-box .header-box .header-hex {
    width: 390px;
    border-right: 1px solid var(--border-color);
    border-bottom: 1px solid var(--border-color);
    color: #409eff;
  }
  
  .hex-panel-box .body-box .body-inner-box .body-hex {
    width: 390px;
    border-right: 1px solid var(--border-color);
    padding: 10px;
    box-sizing: border-box;
  }
  
  .hex-panel-box .header-box .header-bin {
    width: 200px;
    border-right: 1px solid var(--border-color);
    border-bottom: 1px solid var(--border-color);
    color: #409eff;
  }
  
  .hex-panel-box .body-box .body-inner-box .body-bin {
    width: 185px;
    padding: 10px;
    border-right: 1px solid var(--border-color);
  }
  
  .hex-panel-box .header-box .header-scroll {
    width: 16px;
    border-bottom: 1px solid var(--border-color);
  }
  
  .hex-panel-box .body-box .hex-word.active,.hex-panel-box .body-box .bin-word.active {
    color: #fff;
    background: #f00;
  }
  
  .hex-word,.bin-word{
    font-size: 14px;
    display: inline-block;
    width: calc(100% / 16);
    text-align: center;
  }
  
  /*preprae*/
  .hex-panel-box .prepare-box{
    display: inline-block;
    font-size: 0;
  }
  .hex-panel-box .prepare-box .prepare-label{
    text-align: center;
    height: 100%;
    box-sizing: border-box;
    border-radius: 0;
    font-size: 12px;
    width: 90px;
    padding: 0;
    line-height: 24px;
    text-align: center;
    background-color: #F5F7FA;
    color: #909399;
    position: relative;
    border: 1px solid #DCDFE6;
    border-right: 0;
    white-space: nowrap;
    display: inline-block;
    vertical-align: middle;
  }
  
  .hex-panel-box .prepare-box .prepare-input{
    border: 1px solid #DCDFE6;
    line-height: 26px;
    height: 26px;
    vertical-align: middle;
    border-radius: 0;
    padding-left: 5px;
    outline: none;
    box-sizing: border-box;
  }
  .hex-panel-box .prepare-box .prepare-label.offset{
    width: 60px;
  }
  
  .hex-panel-box .prepare-box .prepare-input.offset{
    width: 100px;
  }
  
  /*button*/
  .hex-panel-box .control-box button{
    font-family: 微软雅黑, 'Raleway', sans-serif;
    line-height: 24px;
    box-sizing: border-box;
    outline: none;
    border: 1px solid #DCDFE6;
    cursor: pointer;
    font-size: 12px;
    padding: 0 10px;
    min-width: 65px;
    border-radius: 0;
    color: #343434;
  }
  
  .hex-panel-box .control-box button:hover{
    background: #e1e1e1;
  }
  
  /*表格*/
  .table-box{
    display: block;
    --border-color: #e3e3e3;
  }
  .table-box .el-table{
    display: inline-block;
    width: 600px;
    margin-top: 15px;
    border: 1px solid var(--border-color);
  }
  .el-table .danger-row {
    background: rgba(255, 0, 0, 0.2);
  }
  /*表格*/
  .tol-msg{
    text-align: center;
    color: #888;
    margin-top: 20%;
  }
  </style>
  
