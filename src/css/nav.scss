// 定制导航样式
$activeBGColor: #1890ff; //激活时 背景颜色
$textColor: rgba(255, 255, 255, 0.651); //正常显示时 文本颜色
$hoverBGColor: #1c4e80; //hover背景色
$activeColor: #fff; //激活时 文本颜色
$navHeight: 48px; //导航 单个项目高度

.main_navi_list {
  .el-menu {
    border-right: none;
    background-color: transparent;

    .el-sub-menu__title {
      color: $textColor;
      height: $navHeight;
      line-height: 20px;
      display: flex;
      align-items: center;
      padding-left: 14px !important;

      span {
        flex: 1;
      }

      .el-submenu__icon-arrow {
        position: static;
        margin-top: 0;
        font-size: 14px;
      }
    }

    .el-menu-item {
      color: $textColor;
      height: $navHeight;
      line-height: 20px;
      display: flex;
      align-items: center;

      &.is-active {
        background-color: $activeBGColor;
        color: $activeColor;
      }

      span {
        flex: 1;
      }
    }

    .one_nav {
      &.el-menu-item {
        padding-left: 14px !important;
      }

      &.is-active {

        .el-sub-menu__title,
        i {
          color: $activeColor;
        }
      }

      .two_nav {

        .el-menu-item,
        &.el-menu-item,
        .el-sub-menu__title {
          color: $textColor;
          padding-left: 43px !important;

          i {
            color: $textColor;
          }
        }

        &.is-active {
          color: $activeColor;

          .el-sub-menu__title,
          i {
            color: $activeColor;
          }
        }

        .three_nav {
          &.el-menu-item {
            padding-left: 54px !important;
          }

          &.is-active {
            color: $activeColor;
          }
        }
      }
    }

    .el-menu--inline {
      background-color: #000c17;
    }

    .el-menu-item:hover,
    .el-sub-menu__title:hover {
      &:not(.is-active) {
        outline: none;
        background-color: $hoverBGColor;
        color: $activeColor;

        i,
        span {
          color: $activeColor;
        }
      }
    }
  }
}

.el-menu--vertical {

  // left: 48px !important;
  .el-menu--popup-right-start {
    margin: 0 !important;
    padding: 0 !important;
  }

  .el-menu--popup {

    .el-menu-item,
    .el-sub-menu__title {
      background-color: #0a1936;
      color: $textColor;
      height: 48px;
      line-height: 20px;
      display: flex;
      align-items: center;

      &.is-active {
        background-color: $activeBGColor;
        color: $activeColor;
      }

      &:not(.is-active):hover {
        background-color: $hoverBGColor;
        color: $activeColor;
      }
    }
  }
}

.main_nav {

  i,
  span {
    // transition: border-color 0.3s, background-color 0.3s, color 0.3s;
    transition: all 0.2s !important;
    transition-timing-function: linear;
  }
}

.el-menu-item:focus {
  // background-color:unset !important;
}
