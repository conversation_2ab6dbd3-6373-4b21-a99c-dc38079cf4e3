html,body{
    margin: 0;
    width: 100%;
    height: 100%;
}
h1,h2,h3,h4,h5,p,div{
    margin: 0;
}
.el-table th.el-table__cell{
    background: #F5F7FA !important;
}
  .el-form--inline .el-form-item{
    margin: 0 0 0 12px;
  }
  .el-drawer__header{
    border-bottom: 1px solid #ddd;
    margin-bottom: 0;
    padding-bottom: 20px;
  }
  .el-drawer__footer{
    text-align: left;
    border-top: 1px solid #ddd;
    padding-top: 20px;
  }
  .el-drawer__body{
    padding: 32px 20px;
  }
  .el-message-box__status+.el-message-box__message{
    padding-left: 28px;
  }
  .main-content{
    background: #fff;
    min-height: 100%;
    padding: 20px;
  }
  .el-main{
    padding: 12px
  }
  .el-form-item.is-required:not(.is-no-asterisk).asterisk-left>.el-form-item__label:before{
    content: '' !important;
  }
  .el-form-item.is-required:not(.is-no-asterisk).asterisk-left>.el-form-item__label::after{
    content: '*';
    color: var(--el-color-danger);
    margin-left: 4px;
  }
  //列表分页间距样式
  .el-table{
    margin-top: 20px;
  }
  .el-pagination{
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }

  //分级背景色样式
  .color-box {
    padding-right: 10px;
    .color-item {
      display: inline-block;
      margin: 0 5px 0 0;
      padding: 0 5px;
      height: 24px;
      line-height: 24px;
      font-size: 12px;
      text-align: center;
      font-weight: 400;
      border-radius: 4px;
      width:48.69px;
      // margin-bottom: 5px;
      // color: #fff;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .one{
      color:#FD4747;
      border: 1px solid #FEF0F0;
      background-color: #FEF0F0;
    }
    .two{
      color:#FB65B7;
      border: 1px solid #FEF0F8;
      background-color: #FEF0F8;
    }
    .three{
      color:#FF9900;
      border: 1px solid #FCF6EC;
      background-color: #FCF6EC;
    }
    .four{
      color:#1890FF;
      border: 1px solid #ECF5FF;
      background-color: #ECF5FF;
    }
    .five{
      color:#1094A3;
      border: 1px solid #E7F4F6;
      background-color: #E7F4F6;
    }
    .six{
      color:#CC1878;
      border: 1px solid #FAE8F2;
      background-color: #FAE8F2;
    }
    .seven{
      color:#373799;
      border: 1px solid #EBEBF5;
      background-color: #EBEBF5;
    }
    .eight{
      color:#13C2C2;
      border: 1px solid #E7F9F9;
      background-color: #E7F9F9;
    }
    .nine{
      color:#07B630;
      border: 1px solid #F0F9EB;
      background-color: #F0F9EB
      ;
    }
    .ten{
      color:#909399;
      border: 1px solid #F4F4F5;
      background-color: #F4F4F5;
    }
   
 }

//  列表内容不超过两行-文本
.table-item-content{
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  word-break: break-all;
}
//  列表内容不超过1行
.table-item-content-one{
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
  word-break: break-all;
}
// 列表标签超出两行滚动
.scrollbar-wrapper {
  overflow-x: hidden !important;
  // padding-bottom: 100px;
}
.wrapper-custom.scrollbar-wrapper:after{
    content: '';
    display: inline-block;
    vertical-align: middle;
    height: 100%;
    width: 0;
}
.wrapper-custom.scrollbar-wrapper .el-scrollbar__view{
    vertical-align: middle;
    display: inline-block;
}


// 修改确认框按钮位置
.el-message-box__btns {
  padding: 5px 15px 0;
  display: flex;
  flex-direction: row-reverse;
  gap: 10px;
  float: right;
}

// .el-alert--info.is-light{
//   background: #ECF5FF;
//   color: #1890ff;
// }

// 统一修改列表字体颜色
.el-table td.el-table__cell div{
  color: #4E5155;
}
// 输入框上下间距
.el-form--label-top .el-form-item{
  margin-bottom: 16px;
}
// 表单标题与输入框上下间距
.el-form--default.el-form--label-top .el-form-item .el-form-item__label{
  margin-bottom: 4px;
}
.title-pie{
  display: flex;
  align-items: center;
  font-size: 16px;
  >span{
    width: 5px;
    height: 14px;
    background: #1890FF;
    border-radius: 1px;
    display: inline-block;
    margin-right: 8px;
  }
}
.el-popper.is-dark{
  max-width: 30vw;
}

//单个查询布局
.single-inspection{
  width: 300px;
  float: right;
  margin-bottom: 16px;
}

// 文件详情统一布局
.file-msg-content{
  padding-left: 12px;
  .el-col{
    display: flex;
    align-items: center;
    line-height: 28px;
  }
}

//内存镜像视角详情列表统一高度
.scroll-bar{
  .el-scrollbar{
    height: 550px;
    overflow: auto;
  }
}

// 期限时间字体颜色
.time-limit{
  color: #1890FF;
}

// 列表展开详情样式
.detail-msg{
  background: #eee;
  padding: 10px;
}

// 系统类型图标大小
.system-icon-style{
  svg.svg-icon{
    width: 16px;
    height: 16px;
  }
}

// 模板引用次数  自定义样式
.col-history {
  background: #53a9ff;
  color: #fff;
  padding: 3px 8px;
  border-radius: 3px;
  cursor: pointer;
}

// 威胁等级 自定义标签 top-tag
.top-tag {
  display: inline-block;
  height: 22px;
  line-height: 20px;
  font-size: 12px;
  box-sizing: border-box;
  border-radius: 2px;
  min-width: 65px;
}

.top-tag[size=mini] {
  height: 20px;
  line-height: 18px;
}

.top-tag[type="critical"] {
  border: 1px solid #AE0000;
  color: #AE0000;

  .left-icon {
      background: #AE0000;
  }
}
.top-tag[type="high"] {
  border: 1px solid #fd4747;
  color: #fd4747;

  .left-icon {
      background: #fd4747;
  }
}

.top-tag[type="medium"] {
  border: 1px solid #ff9900;
  color: #ff9900;

  .left-icon {
      background: #ff9900;
  }
}
.top-tag[type="low"] {
  border: 1px solid #909399;
  color: #909399;

  .left-icon {
      background: #909399;
  }
}

.top-tag[type="information"] {
  border: 1px solid #1890ff;
  color: #1890ff;

  .left-icon {
      background: #1890ff;
  }
}
.top-tag[type="safe"] {
  border: 1px solid #07b630;
  color: #07b630;

  .left-icon {
      background: #07b630;
  }
}

.top-tag {
  .left-icon {
      display: inline-block;
      // line-height: 20px;
      width: 24px;
      color: white;
      font-size: 14px;
      text-align: center;
  }

  .text {
      display: inline-block;
      min-width: 34px;
      line-height: 20px;
      padding: 0 5px;
      font-size: 14px;
      text-align: center;
  }
}

//自定义威胁标签
.tag-custom{
  display: inline-block;
  line-height: 20px;
  font-size: 14px;
  padding: 0 8px;
  border-radius: 4px;
  margin-right: 4px;
  white-space: nowrap;
  margin-bottom: 4px;
  &.critical{
    border: 1px solid rgba(174, 0, 0,.2);
    background: rgba(174, 0, 0,.1);
    color: #AE0000;
  }
  &.high{
    border: 1px solid rgba(253, 71, 71,.2);
    background: rgba(253, 71, 71,.1);
    color: #fd4747;
  }
  &.medium{
    border: 1px solid rgba(255, 153, 0,.2);
    background: rgba(255, 153, 0,.1);
    color: #ff9900;
  }
  &.low{
    border: 1px solid rgba(144, 147, 153,.2);
    background: rgba(144, 147, 153,.1);
    color: #909399;
  }
  &.information{
    border: 1px solid rgba(24, 144, 255,.2);
    background: rgba(24, 144, 255,.1);
    color: #1890ff;
  }
  &.safe{
    border: 1px solid rgb(7, 182, 48,.2);
    background: rgba(7, 182, 48,.1);
    color: #07b630;
  }
}

// 标记---tag标签
.tag-container {
  max-height: 46px; 
  overflow-y: auto;
  .tag-item {
    margin-left: 0px;
    margin-right: 5px;
    margin-top: 2px;
  }
}
