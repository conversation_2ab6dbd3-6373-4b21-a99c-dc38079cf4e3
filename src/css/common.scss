@import './nav.scss';
$maincolor: #409eff;
$errorcolor: #409eff;

* {
  box-sizing: border-box;
}

#app {
  background: #eee;
  height: 100%;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
}

textarea {
  font-family: '微软雅黑';
}

code,
pre {
  font-family: Consolas, 'Courier New', monospace, '微软雅黑' !important;
}

@font-face {
  font-family: 'pmzdb-TiTi';
  src: url('../assets/font/PangMenZhengDaoBiaoTiTi-1.ttf');
}

@font-face {
  font-family: 'YouSheBiaoTiHei';
  src: url('../assets/font/YouSheBiaoTiHei-2.ttf');
}

html {
  height: 100%;
}

body {
  min-width: 1200px;
  // height: 100%;
  // overflow-y: hidden;
  // overflow-x: auto;
  margin: 0px;
  padding: 0px;
}

.container {
  width: 100%;
  height: 100%;
}

.center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.no-border.el-input input {
  border: none;
}

/* 定制滚动条 */

// ::-webkit-scrollbar {
//   padding-left: 2px;
//   width: 4px;
//   height: 4px;
//   background-color: #f5f5f5;
// }

// ::-webkit-scrollbar-thumb {
//   border-radius: 8px;
//   background-color: rgb(218, 218, 218);
// }

// ::-webkit-scrollbar-track {
//   border-radius: 8px;
//   background-color: #f5f5f5;
// }
// ::-webkit-scrollbar-track {
//   background: rgba(0, 0, 0, .0);
//   border-radius: 0;
// }

// ::-webkit-scrollbar {
//   // display: none;
//   // -webkit-appearance: none;
//   background: rgba(0, 0, 0, .0);
//   width: 6px;
//   height: 6px;
// }

// ::-webkit-scrollbar-thumb {
//   // cursor: pointer;
//   // border-radius: 5px;
//   // width: 10px;
//   // height: 10px;
//   // background: rgba(0, 0, 0, .25);
//   // transition: color .2s ease;
// }

::-webkit-scrollbar {
  width: 5px;
  height: 5px;
}

::-webkit-scrollbar-thumb {
  border-radius: 5px;
  -webkit-box-shadow: inset 0 0 5px rgba(0,0,0,0.2);
  background-color: #99a9bf;
}

::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 5px rgba(0,0,0,0.2);
  border-radius: 5px;
  background-color: #d3dce6;
}
                      

.flex_box {
  display: flex;
}

.flex1 {
  flex: 1;
}

.flexColumn {
  display: flex;
  flex-flow: column;
  flex: 1;
  height: 100%;
}

.flex_center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.operation {
  display: inline-block;
  margin: 0px 8px;
  font-size: 14px;
  cursor: pointer;
  color: #2772f0;
}

.seachCard,
.seachCardMin {
  display: flex;
  flex-wrap: wrap;
  background-color: #fff;
  padding: 14px 20px;
  margin-bottom: 15px;

  .seachItem,
  .seachBtn {
    white-space: nowrap;
    line-height: 32px;
    height: 32px;
    align-content: flex-start;
    display: flex;
    align-items: center;
    width: 25% !important;
    padding-right: 14px;
    margin-top: 7px;
    margin-bottom: 7px;

    .lable {
      display: inline-block;
      margin-right: 10px;
      width: 120px;
      text-align: right;
    }

    &>.el-input,
    &>.el-select,
    &>.el-date-editor {
      flex: 1;
      width: calc(100% - 120px);
    }
  }

  .seachBtn {
    padding-left: 130px;
  }

  // .el-input {
  //   width: 196px;
  // }
}

@media screen and (max-width: 1580px) {

  .seachCard,
  .seachCardMin {

    .seachItem,
    .seachBtn {
      width: calc(100% / 3) !important;
      padding-right: 8px;

      .lable {
        width: 110px;
        margin-right: 5px;
      }
    }

    .seachBtn {
      padding-left: 115px;
    }
  }
}

// 通用样式
.main {
  width: 1200px;
  margin: 0 auto;
}

.content {
  margin-top: 16px;
  background-color: #fff;
  overflow: auto;
}

.title {
  margin: 16px;
  font-size: 16px;
  font-weight: bold;
  color: #303133;
  line-height: 24px;
}

.el-table {
  --el-table-text-color: #4e5155;
  --el-table-header-bg-color: #f5f7fa;
}

.el-table thead {
  --el-table-header-text-color: #606266;
}

// 情报描述tabs
.domain-panel-tabs {
  margin-top: 13px;
  margin: 16px;
  border: none;
  box-shadow: none;

  &.el-tabs--border-card {
    &>.el-tabs__header .el-tabs__item.is-active {
      border: 1px solid #f5f7fa;
      border-bottom: 1px solid #e4e7ed;
      background-color: #fff;
      box-shadow: inset 1px 1px 0px 0px #e4e7ed, inset 0px 1px 0px 0px #e4e7ed, inset -1px 0px 0px 0px #e4e7ed;
      border-radius: 0;
      font-size: 14px;
      font-weight: bold;
      --el-color-primary: #1890ff;
    }

    &>.el-tabs__content {
      padding: 15px 0;
    }
  }
}

// 情报评价tabs
.evaluate_tabs {
  &.el-tabs--left .el-tabs__header.is-left {
    margin-right: 18px;
  }
}

//@at-root
// .apt-intelligence {
//   .el-table {
//     --el-table-header-bg-color: #f5f7fa;
//   }

//   .el-button {
//     border-top-left-radius: 0px;
//     border-bottom-left-radius: 0px;
//   }

//   .el-input__wrapper {
//     border-top-right-radius: 0px;
//     border-bottom-right-radius: 0px;
//   }

//   .el-input__inner {
//     padding-left: 15px;
//   }
// }

// 资产测绘tabs
.assetMap_tabs {
  .el-tabs__nav {
    padding: 4px 0;
  }
}

// 折叠面板去掉border
.el-collapse {
  border: 0;
}

.el-collapse-item__header {
  border: 0;
}

.el-collapse-item__wrap {
  border: 0;
}

// 大屏-进度条组件
.progressGroup {
  .progressGroup {
    --el-border-color-lighter: rgba(236, 169, 77, 0.1);
  }

  .el-progress-bar__outer {
    overflow: visible;
  }

  .red .el-progress-bar__inner {
    background: linear-gradient(102deg, #ff5a5a 0%, #ff903e 100%);
  }

  .blue .el-progress-bar__inner {
    background: linear-gradient(90deg, #1882ff 0%, #36ebca 100%);
  }

  .el-progress-bar__outer {
    --el-border-color-lighter: rgba(236, 169, 77, 0.1);
  }
}

/*---滚动条默认显示样式--*/

::-webkit-scrollbar-thumb {
  // background: rgba(144,147,153,.2);
}

/*---鼠标点击滚动条显示样式--*/

// ::-webkit-scrollbar-thumb:hover {

//   background-color: #FB4446;

//   height: 50px;

//   -webkit-border-radius: 4px;

// }

.window {
  // ::-webkit-scrollbar {
  //   width: 4px;
  //   background: rgba(255, 255, 255, 0.1);
  //   border-radius: 5px 5px 5px 5px;
  //   opacity: 1;
  // }

  // ::-webkit-scrollbar-thumb {
  //   width: 2px;
  //   height: 4px;
  //   background: rgba(24, 144, 255, 0.7);
  //   border-radius: 5px 5px 5px 5px;
  //   opacity: 1;
  // }

  // ::-webkit-scrollbar-track {
  //   border-radius: 8px;

  //   background: rgba(255, 255, 255, 0.1);
  // }
}

// 大屏-select
.largeScreen {
  .attackTimeSelect {
    .el-input__wrapper {
      width: 180px;
      height: 32px;
      background: linear-gradient(90deg, #1c76be 0%, rgba(9, 58, 96, 0.4) 100%);
      border-radius: 0px 0px 0px 0px;
      opacity: 1;
      border: 1px solid;
      border-image: linear-gradient(134deg, rgba(180, 180, 180, 0), rgba(29, 229, 235, 1), rgba(26, 150, 236, 0.1)) 1 1;
      box-shadow: 0 0 0 0;

      .el-input__inner {
        color: #fff;
      }
    }
  }
}

// 菜单
.el-menu-item .el-menu-tooltip__trigger {
  padding-left: 14px;
}

.el-collapse-item__header {
  &:hover {
    background-color: #fafafa;
  }
}

.large-screen-select-option {
  background: linear-gradient(90deg, #1c76be 0%, rgba(28, 118, 190, 0.9) 100%) !important;
  border-radius: 0px 0px 0px 0px !important;
  opacity: 1 !important;
  border: 1px solid !important;
  border-image: linear-gradient(134deg, rgba(180, 180, 180, 0), rgba(29, 229, 235, 1), rgba(26, 150, 236, 0.1)) 1 1 !important;

  .el-select-dropdown__item {
    span {
      color: #e4f2ff;
    }
  }

  .el-select-dropdown__item.hover {
    background-color: #1766a3;
  }

  .el-popper__arrow {
    display: none;
  }
}

.el-message-box__content {
  padding: 10px 15px !important;
}



ul,
li {
  list-style: none;
  margin: 0;
  padding: 0;
}

.country {
  margin-left: 5px;

  .country-span {
    font-size: 14px;
    color: #676f77;
    margin-right: 5px;
    cursor: pointer;

    &:hover {
      color: #409eff;
    }
  }
}

p {
  margin: 0;
  padding: 0;
}

.itemBox {
  display: flex;
  justify-content: space-between;
  padding: 6px 0;

  .name {
    font-size: 13px;
    color: #409eff;
    cursor: pointer;
    padding-right: 10px;
    box-sizing: border-box;
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .number {
    font-size: 13px;
    color: #a3a9b3;
  }
}

.login-dialog {
  .el-dialog__header {
    padding: 0;

    .el-dialog__headerbtn {
      width: 32px;
      height: 32px;
    }

    .login-dialog-header-img {
      width: 82px;
      margin-top: 10px;
      margin-left: 10px;
    }
  }

  .el-dialog__body {
    padding-top: 10px !important;
    padding-bottom: 10px !important;
  }
}


// 四个视角的功能样式
.c_time {
  color: #676f77;
  font-size: 14px;
}

.card-header {
  height: 48px !important;

  .c_title {
    font-weight: 600;
    color: #1890FF !important;
  }
}

// .el-tag+.el-tag {
//   margin-left: 8px !important;
// }

.country_icon {
  margin-right: 5px;
}

.loadingGif {
  height: 16px;
  position: relative;
  top: 2px;
}

.el-table__inner-wrapper:before {
  display: none;
}

.relative {
  position: relative !important;
}

// :deep(.el-loading-spinner) {
//   top: 10% !important;
// }