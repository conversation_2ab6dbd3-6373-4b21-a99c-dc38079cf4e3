// 大一点的数字转换
export function simplifyNum(number) {
  if (!number && number !== 0) return number;
  let str_num;
  if (number >= 1e3 && number < 1e6) {
    str_num = (number / 1e4).toFixed(2);
    return str_num + ' 万';
  } else if (number >= 1e6 && number < 1e7) {
    str_num = (number / 1e6).toFixed(2);
    return str_num + ' 百万';
  } else if (number >= 1e7 && number < 1e8) {
    str_num = (number / 1e7).toFixed(2);
    return str_num + ' 千万';
  } else if (number >= 1e8 && number < 1e10) {
    str_num = (number / 1e8).toFixed(2);
    return str_num + ' 亿';
  } else if (number >= 1e10 && number < 1e11) {
    str_num = (number / 1e10).toFixed(2);
    return str_num + ' 百亿';
  } else if (number >= 1e11 && number < 1e12) {
    str_num = (number / 1e11).toFixed(2);
    return str_num + ' 千亿';
  } else if (number >= 1e12) {
    str_num = (number / 1e12).toFixed(2);
    return str_num + ' 万亿';
  } else {
    // 一千以下
    return number;
  }
}

export default {
  simplifyNum
};
