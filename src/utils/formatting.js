// 直接传入原始时间节点--日期时间编译
export const timeFormat = (time) => {
  const date = time + '';
  if (date.length == 10) {
    time = time * 1000;
  }
  var data = new Date(time);
  // 封装年份时间
  var y = data.getFullYear();
  // 封装月份时间
  var m = (data.getMonth() + 1).toString().padStart(2, '0');
  // 封装日期时间
  var d = data.getDate().toString().padStart(2, '0');
  // 转换详细时分
  var hours =
      data.getHours() < 10 ? "0" + data.getHours() : data.getHours();
  var minutes =
      data.getMinutes() < 10 ? "0" + data.getMinutes() : data.getMinutes();
  var second =
      data.getSeconds() < 10 ? "0" + data.getSeconds() : data.getSeconds();
    // 输出转换后时间节点
  return `${y}-${m}-${d} ${hours}:${minutes}:${second}`;
};

// 日期编译
export const dateFormat = (time) => {
  const date = time + '';
  if (date.length == 10) {
    time = time * 1000;
  }
  var data = new Date(time);
  // 封装年份时间
  var y = data.getFullYear();
  // 封装月份时间
  var m = (data.getMonth() + 1).toString().padStart(2, '0');
  // 封装日期时间
  var d = data.getDate().toString().padStart(2, '0');
  // 输出转换后时间节点
  return `${y}-${m}-${d}`;
};

// value和label条件调换格式化
export const optionFormat = (option, val) => {
  let str;
  option.map(item => {
    if (item.value == val) {
      str = item.label;
    }
  });
  return str || val;
};

// 分级背景色值
export const levelColor = (num) => {
  const color = {
    1: 'one',
    2: 'two',
    3: 'three',
    4: 'four',
    5: 'five',
    6: 'six',
    7: 'seven',
    8: 'eight',
    9: 'nine',
    10: 'ten'
  };
  let val = '';
  for (const key in color) {
    if (num == key) val = color[key];
  }
  return val;
};

