/**
* @desc  [自定义校验规则]
* @example
* import { regPassword, regPort } from "~/utils/validate.js";
* rules: [
*   { validator: regPort('端口号').test, required: true }
* ]
*/


/**
* 验证某些必填项
*/
export const regRequired = (title = '必填项') => {
  return {
    test: (rule, value, callback) => {
      if (!value) {
        callback(new Error(`请输入${title}`));
      } else {
        callback();
      }
    }
  };
};

/**
* 端口号
* 规则： (1 - 65535) 数字
*/
export const regPort = (title = '端口号') => {
  return {
    test: (rule, value, callback) => {
      const reg = /^[0-9]*$/;
      if (!value) {
        callback(new Error(`请输入${title}`));
      } else if (!reg.test(value)) {
        callback(new Error(`${title}格式不正确，必须输入数字`));
      } else if (value < 1 || value > 65535) {
        callback(new Error(`检测到${title}范围超出（提示：1 - 65535）`));
      } else {
        callback();
      }
    }
  };
};

/**
* 端口号及范围
* 规则： (1 - 65535) 数字
*/
export const rangePort = (title = '端口号') => {
  return {
    test: (rule, value, callback) => {
      const reg = /^[0-9]*$/;
      if (!value) {
        callback(new Error(`请输入${title}`));
      } else {
        const val = (value && value.indexOf(',') == -1) ? value.split('，') : value.split(',');
        const range = [];
        let num = 0;
        let ran = 0;
        val.forEach(item => {
          if (item.indexOf('-') !== -1) {
            const arr = item.split('-');
            range.push(...arr);
          } else {
            range.push(item);
          }
        });
        range.forEach(item => {
          if (!reg.test(item)) {
            num++;
          } else if (item < 1 || item > 65535) {
            ran++;
          }
        });
        if (num > 0) {
          callback(new Error(`${title}格式不正确，必须输入数字`));
        } else if (ran > 0) {
          callback(new Error(`检测到${title}范围超出（提示：1 - 65535）`));
        } else {
          callback();
        }
      }
    }
  };
};

/**
* 邮箱
* 规则： 邮箱
*/
export const regEmail = (title = '邮箱地址') => {
  return {
    test: (rule, value, callback) => {
      const reg = /^[A-Za-z\d]+([-_.][A-Za-z\d]+)*@([A-Za-z\d]+[-.])+[A-Za-z\d]{2,4}$/;
      if (!value) {
        callback(new Error(`请输入${title}`));
      } else if (!reg.test(value)) {
        callback(new Error(`${title}格式不正确，请重新输入！`));
      } else {
        callback();
      }
    }
  };
};
/**
* 邮箱-非必填
* 规则： 邮箱
*/
export const regMustEmail = (title = '邮箱地址') => {
  return {
    test: (rule, value, callback) => {
      const reg = /^[A-Za-z\d]+([-_.][A-Za-z\d]+)*@([A-Za-z\d]+[-.])+[A-Za-z\d]{2,4}$/;
      if (value && !reg.test(value)) {
        callback(new Error(`${title}格式不正确，请重新输入！`));
      } else {
        callback();
      }
    }
  };
};

/**
* 手机号码
* 规则： 手机号码
*/
export const regPhone = (title = '手机号码') => {
  return {
    test: (rule, value, callback) => {
      const reg = /^[1][3-9][0-9]{9}$/;
      if (!value) {
        callback(new Error(`请输入${title}`));
      } else if (!reg.test(value)) {
        callback(new Error(`${title}格式不正确，请重新输入！`));
      } else {
        callback();
      }
    }
  };
};
/**
* 手机号码-非必填
* 规则： 手机号码
*/
export const regMustPhone = (title = '手机号码') => {
  return {
    test: (rule, value, callback) => {
      const reg = /^[1][3-9][0-9]{9}$/;
      if (value && !reg.test(value)) {
        callback(new Error(`${title}格式不正确，请重新输入！`));
      } else {
        callback();
      }
    }
  };
};

/**
* IP
* 规则： 支持IPV4/IPV6
*/
export const regIP = (title = 'IP地址') => {
  return {
    test: (rule, value, callback) => {
      // IPV4/IPV6 正则表达式
      const reg = /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:){6}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^::([\da-fA-F]{1,4}:){0,4}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:):([\da-fA-F]{1,4}:){0,3}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:){2}:([\da-fA-F]{1,4}:){0,2}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:){3}:([\da-fA-F]{1,4}:){0,1}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:){4}:((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:){7}[\da-fA-F]{1,4}$|^:((:[\da-fA-F]{1,4}){1,6}|:)$|^[\da-fA-F]{1,4}:((:[\da-fA-F]{1,4}){1,5}|:)$|^([\da-fA-F]{1,4}:){2}((:[\da-fA-F]{1,4}){1,4}|:)$|^([\da-fA-F]{1,4}:){3}((:[\da-fA-F]{1,4}){1,3}|:)$|^([\da-fA-F]{1,4}:){4}((:[\da-fA-F]{1,4}){1,2}|:)$|^([\da-fA-F]{1,4}:){5}:([\da-fA-F]{1,4})?$|^([\da-fA-F]{1,4}:){6}:$/;
      if (!value) {
        callback(new Error(`请输入${title}`));
      } else if (!reg.test(value)) {
        callback(new Error(`${title}格式错误，请重新输入！`));
      } else {
        callback();
      }
    }
  };
};

/**
* 密码
* 规则：至少包含数字、大写字母、小写字母中的两种且不少于8位
*/
export const regPassword = (title = '密码') => {
  return {
    test: (rule, value, callback) => {
      const reg = /^(?![\d]+$)(?![a-z]+$)(?![A-Z]+$)[\da-zA-z]{8,}$/g;
      if (!value) {
        callback(new Error(`请输入${title}`));
      } else if (!reg.test(value)) {
        callback(new Error(`${title}至少包含数字、大写字母、小写字母中的两种，且不少于8位`));
      } else {
        callback();
      }
    }
  };
};

/**
* 大小写字母数字（长度）校验
* 规则：大小写字母或者数字不能为空 最小长度minx 最大长度max
*/
export const regLetter = (title = '内容', minLen = 2, maxLen = 12) => {
  return {
    test: (rule, value, callback) => {
      const reg = new RegExp(`^[a-zA-Z0-9]{${minLen},${maxLen}}$`);
      if (!value) {
        callback(new Error(`请输入${title}`));
      } else if (!reg.test(value)) {
        callback(new Error(`请输入${minLen}-${maxLen}位大小写字母或者数字`));
      } else {
        callback();
      }
    }
  };
};

// IP正则，支持 IP后缀掩码
export const IP_SUFFIX = /^((?:(?:25[0-5]|2[0-4]\d|((1\d{2})|([1-9]?\d)))\.){3}(?:25[0-5]|2[0-4]\d|((1\d{2})|([1-9]?\d))))(\/([1-9]|[1-2]\d|3[0-2]))?$/;
/**
* 验证IP，支持 IP后缀掩码
*/
export const regIP_SUFFIX = (title = 'IP地址') => {
  return {
    test: (rule, value, callback) => {
      if (!value) {
        callback(new Error(`请输入${title}`));
      } else if (!IP_SUFFIX.test(value)) {
        callback(new Error(`${title}格式错误，请重新输入！`));
      } else {
        callback();
      }
    }
  };
};
