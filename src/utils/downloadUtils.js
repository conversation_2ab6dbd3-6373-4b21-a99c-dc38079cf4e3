import { useCookies } from 'vue3-cookies';
const { cookies } = useCookies();

// 下载
export const downloadFun = (url, params) => {
  axios({
    url: window.BASE_URL + url,
    method: 'get',
    responseType: 'blob',
    headers: { Token: cookies.get('dsp_token') },
    params: params
  }).then(res => {
    console.log(res);
    const blob = new Blob([res.data], { type: res.headers['content-type'] });
    // 下载
    var num = res.headers['content-disposition'].indexOf('utf-8');
    var filename = res.headers['content-disposition'].split(num === -1 ? 'filename=' : "filename*=utf-8''");
    var name = window.decodeURI(filename[filename.length - 1]);
    const url = window.URL.createObjectURL(blob);
    var link = document.createElement('a');
    link.download = name || '';
    link.href = url;
    link.click();
    window.URL.revokeObjectURL(link.href);
  });
};
  
