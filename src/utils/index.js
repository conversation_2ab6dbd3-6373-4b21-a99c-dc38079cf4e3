import isURL from 'validator/lib/isURL';
import isEmail from 'validator/lib/isEmail';
import isIP from 'validator/lib/isIP';
import isPort from 'validator/lib/isPort';
import dayjs from 'dayjs';
import xss from 'xss';
import SparkMD5 from 'spark-md5';
import CryptoJS from 'crypto-js';

/**
 * Parse the time to string
 * @param {(Object|string|number)} time
 * @param {string} cFormat
 * @returns {string | null}
 */
export function parseTime(time, cFormat) {
  if (arguments.length === 0) {
    return null;
  }
  const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}';
  let date;
  if (typeof time === 'object') {
    date = time;
  } else {
    if (typeof time === 'string' && /^[0-9]+$/.test(time)) {
      time = parseInt(time);
    }
    if (typeof time === 'number' && time.toString().length === 10) {
      time = time * 1000;
    }
    date = new Date(time);
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  };
  // eslint-disable-next-line camelcase
  const time_str = format.replace(/{([ymdhisa])+}/g, (result, key) => {
    const value = formatObj[key];
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') {
      return ['日', '一', '二', '三', '四', '五', '六'][value];
    }
    return value.toString().padStart(2, '0');
  });
  // eslint-disable-next-line camelcase
  return time_str;
}

export const formatTime = (time, val = 'YYYY-MM-DD') => {
  // YYYY-MM-DD HH:mm:ss
  if (!['-', undefined, '', 0].includes(time)) {
    return dayjs(time * 1000).format(val);
  }
  return '-';
};

/**
 * @param {string} url
 * @returns {Object}
 */
export function param2Obj(url) {
  const search = url.split('?')[1];
  if (!search) {
    return {};
  }
  return JSON.parse('{"' + decodeURIComponent(search).replace(/"/g, '\\"').replace(/&/g, '","').replace(/=/g, '":"').replace(/\+/g, ' ') + '"}');
}

// 判断对象是否为空
export function objectIsEmpty(obj, objEveryExist = false) {
  if (Object.prototype.toString.call(obj) !== '[object Object]') {
    throw new TypeError('Expected a object');
  }
  const keysArray = Object.keys(obj);
  const valArray = Object.values(obj);
  const newVal = valArray.filter(item => {
    const val = JSON.stringify(item);
    if (val !== '{}' && val !== '[]') {
      return item;
    }
  });
  if (!objEveryExist) {
    if (newVal.length > 0) {
      return false;
    } else {
      return true;
    }
  } else {
    if (keysArray.length === newVal.length) {
      return true;
    } else {
      return false;
    }
  }
}

// 密码校验
export const validatePassword = (rule, value, callback) => {
  if (!value) {
    callback(new Error('请输入密码'));
  } else if (value.length < 6) {
    callback(new Error('密码长度不得小于六位'));
  } else {
    callback();
  }
};

const formatNumber = n => {
  n = n.toString();
  return n[1] ? n : '0' + n;
};

// 时间格式
export const formatMinute = date => {
  const hours = date.getHours();
  const minutes = date.getMinutes();
  const second = date.getSeconds();
  return [hours, minutes, second].map(formatNumber).join(':');
};

// 时间格式 yy-mm-dd hh:mm:ss
export const formatDate = date => {
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();

  const hours = date.getHours();
  const minutes = date.getMinutes();
  const second = date.getSeconds();
  return [year, month, day].map(formatNumber).join('-') + ' ' + [hours, minutes, second].map(formatNumber).join(':');
};

// 时间格式 yy-mm-dd hh:mm:ss加星期
export const formatDate2 = date => {
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  const we = date.getDay();
  const hours = date.getHours();
  const minutes = date.getMinutes();
  const second = date.getSeconds();
  return [year, month, day].map(formatNumber).join('-') + '\xa0\xa0' + [hours, minutes, second].map(formatNumber).join(':') + '\xa0\xa0' + getDays(we);
};

// 时间格式 yy-mm-dd
export const formatDateEasy = date => {
  date = new Date(date);
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  return [year, month, day].map(formatNumber).join('-');
};

// 获取到月一号
export const formatDate4 = date => {
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  return [year, month].map(formatNumber).join('-') + '-01';
};
// 获取到月一号
export const formatDate5 = date => {
  const year = date.getFullYear();
  return [year].map(formatNumber).join('-') + '-01-01';
};

export const formatDateToHMS = date => {
  const hours = date.getHours();
  const minutes = date.getMinutes();
  const second = date.getSeconds();
  return [hours, minutes, second].map(formatNumber).join(':');
};

export const getMonday = dd => {
  var week = dd.getDay(); // 获取时间的星期数
  var minus = week ? week - 1 : 6;
  dd.setDate(dd.getDate() - minus); // 获取minus天前的日期
  var y = dd.getFullYear();
  var m = dd.getMonth() + 1; // 获取月份
  var d = dd.getDate();
  return [y, m, d].map(formatNumber).join('-');
};

const getDays = days => {
  let day = '';
  switch (days) {
    case 1:
      day = '星期一';
      break;
    case 2:
      day = '星期二';
      break;
    case 3:
      day = '星期三';
      break;
    case 4:
      day = '星期四';
      break;
    case 5:
      day = '星期五';
      break;
    case 6:
      day = '星期六';
      break;
    case 0:
      day = '星期日';
      break;
  }
  return day;
};
// 随机十六进制颜色
export const color16 = () => {
  const r = Math.floor(Math.random() * 256);
  const g = Math.floor(Math.random() * 256);
  const b = Math.floor(Math.random() * 256);
  const color = '#' + r.toString(16) + g.toString(16) + b.toString(16);
  return color;
};

import md5 from 'crypto-js/md5';

export const MD5Password = (usernmae, password) => {
  const u = md5(usernmae + '').toString();
  const p = md5(password + '').toString();
  const r = md5(u + p).toString();
  return r;
};

export const guid = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    var r = (Math.random() * 16) | 0;
    var v = c == 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
};

export const validUrl = (rule, value, callback) => {
  // if (!isURL(value)) {
  //   callback(new Error("请填写正确的url格式"));
  // } else {
  //   callback();
  // }
  if (value !== '') {
    callback();
  } else {
    callback(new Error('请填写url'));
  }
};
export const validEmail = (rule, value, callback) => {
  if (value == '' || value == undefined || value == null) {
    callback();
  } else if (!isEmail(value)) {
    callback(new Error('请填写正确的Email格式'));
  } else {
    callback();
  }
};
export const validPhone = (rule, value, callback) => {
  const reg = /^[1][3,4,5,7,8][0-9]{9}$/;
  if (value == '' || value == undefined || value == null) {
    callback();
  } else {
    if (!reg.test(value) && value != '') {
      callback(new Error('请输入正确的电话号码'));
    } else {
      callback();
    }
  }
};

export const commonRex = {
  md5: /^([a-f0-9]{32}|[A-F0-9]{32})$/, // hash
  sha1: /^([a-f0-9]{40}|[A-F0-9]{40})$/, // hash
  sha256: /^([a-f0-9]{64}|[A-F0-9]{64})$/, // hash
  sha512: /^([a-f0-9]{128}|[A-F0-9]{128})$/, // hash
  ssdeep: /^((\d{3}):(\w*)\+(\w*):(\w*))$/, // hash
  ip: /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:){6}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^::([\da-fA-F]{1,4}:){0,4}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:):([\da-fA-F]{1,4}:){0,3}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:){2}:([\da-fA-F]{1,4}:){0,2}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:){3}:([\da-fA-F]{1,4}:){0,1}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:){4}:((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:){7}[\da-fA-F]{1,4}$|^:((:[\da-fA-F]{1,4}){1,6}|:)$|^[\da-fA-F]{1,4}:((:[\da-fA-F]{1,4}){1,5}|:)$|^([\da-fA-F]{1,4}:){2}((:[\da-fA-F]{1,4}){1,4}|:)$|^([\da-fA-F]{1,4}:){3}((:[\da-fA-F]{1,4}){1,3}|:)$|^([\da-fA-F]{1,4}:){4}((:[\da-fA-F]{1,4}){1,2}|:)$|^([\da-fA-F]{1,4}:){5}:([\da-fA-F]{1,4})?$|^([\da-fA-F]{1,4}:){6}:$/,
  domain: /^(?:[a-z0-9](?:[a-z0-9-_]{0,61}[a-z0-9])?\.)+[a-z0-9][a-z0-9-_]{0,61}[a-z0-9]$/,
  ca: /(^CA:([a-f0-9]{32}|[A-F0-9]{32})$)|(^CA:([a-f0-9]{40}|[A-F0-9]{40})$)|(^CA:([a-f0-9]{64}|[A-F0-9]{64})$)|(^((([A-F0-9]{2})\:){19})([A-F0-9]{2})$)|(^((([a-f0-9]{2})\:){19})([a-f0-9]{2})$)/,
  cve: /^(CVE|cve|CNNVD|cnnvd|CNVD|cnvd|LDY|ldy)-\d{1,9}-\d{1,9}$/,
  email: /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+(.[a-zA-Z0-9_-])+/,
  url: /^(https?|ftp|file):\/\/[-A-Za-z0-9+&@#/%?=~_|!:,.;]+[-A-Za-z0-9+&@#/%=~_|]$/,
  port: /(^[1-9]\d{0,3}$)|(^[1-5]\d{4}$)|(^6[0-4]\d{3}$)|(^65[0-4]\d{2}$)|(^655[0-2]\d$)|(^6553[0-5]$)/
};

// 格式化数组 千分位加逗号
export function formatTotal(total) {
  return String(total).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
}
export function isHash(value) {
  return commonRex.md5.test(value) || commonRex.sha1.test(value) || commonRex.sha256.test(value) || commonRex.sha512.test(value) || commonRex.ssdeep.test(value);
}
export function isIp(value) {
  return commonRex.ip.test(value);
}
export function isDomain(value) {
  return /^(?=^.{3,255}$)[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+$/.test(value);
}
export function isMail(value) {
  return commonRex.email.test(value);
}
// 计算指定区间的开始时间和结束时间
export function computedSETime(type) {
  let starttime;
  const endtime = dayjs().unix();
  if (type === 1) {
    starttime = dayjs().subtract(1, 'day').unix();
  } else if (type === 2) {
    starttime = dayjs().subtract(7, 'day').unix();
  } else if (type === 3) {
    starttime = dayjs().subtract(30, 'day').unix();
  } else if (type === 4) {
    starttime = dayjs().subtract(1, 'year').unix();
  }
  return { starttime, endtime };
}
// 退出全屏
export function onExitScreen() {
  if (document.fullscreenElement) {
    document.exitFullscreen();
  }
  // else {
  //   document.documentElement.requestFullscreen();
  // }
}

// 进入全屏
export function changeFullScreen() {
  const element = document.documentElement;
  if (element.webkitRequestFullScreen) {
    element.webkitRequestFullScreen();
  }
}

// 格式化字节
export function formatBytes(value) {
  let bytes = '-';
  if (value < 1024 * 1024) {
    bytes = parseFloat(value / 1024).toFixed(2) + ' KB';
  } else if (value >= 1024 * 1024 && value < 1024 * 1024 * 1024) {
    bytes = parseFloat(value / (1024 * 1024)).toFixed(2) + ' MB';
  } else if (value >= 1024 * 1024 * 1024 && value < 1024 * 1024 * 1024 * 1024) {
    bytes = parseFloat(value / (1024 * 1024 * 1024)).toFixed(2) + ' GB';
  } else if (value >= 1024 * 1024 * 1024 * 1024) {
    bytes = parseFloat(value / (1024 * 1024 * 1024 * 1024)).toFixed(2) + ' TB';
  }
  return bytes;
}

// XSS过滤
export function filterXSS(context) {
  const filterContext = xss(context, {
    whiteList: [], // 白名单为空，表示过滤所有标签
    stripIgnoreTag: true, // 过滤所有非白名单标签的HTML
    stripIgnoreTagBody: ['script', 'noscript'] // script标签较特殊，需要过滤标签中间的内容
  });
  return filterContext.replace(/\[removed\]/g, '');
}

// 字符串转换
// CN=GoDaddy.com\\, Inc.,O=GoDaddy.com\\, Inc.
// CN=GoDaddy.com, Inc.
// O=GoDaddy.com, Inc.
export function strTransformStr(val) {
  if (val) {
    const modifyVal = val.split('\\,').map(item => {
      return item.replace(new RegExp(',', 'g'), '\n');
    });
    return modifyVal.join(',');
  } else {
    return '-';
  }
}

/**
 * 获取文件hash值  MD5
 * @param {*} file
 * @returns
 */
export function getFileHash(file) {
  return new Promise(resolve => {
    const reader = new FileReader();
    reader.readAsArrayBuffer(file);
    reader.onload = ev => {
      const buffer = ev.target.result;
      const spark = new SparkMD5.ArrayBuffer();
      spark.append(buffer);
      const HASH = spark.end();
      resolve(HASH);
    };
  });
}

/**
 * 获取文件hash值  SHA256
 * @param {*} file
 * @returns
 */
export function getFileSHA256Hash(file) {
  return new Promise(resolve => {
    const reader = new FileReader();
    reader.readAsArrayBuffer(file);
    reader.onload = () => {
      var wordArray = CryptoJS.lib.WordArray.create(reader.result);
      const hash = CryptoJS.SHA256(wordArray).toString();
      resolve(hash);
    };
  });
}

/** 检测上传样本的格式时候正确 */
export function checkSampleType(file) {
  const mimeTypeList = [
    'exe',
    'dll',
    'com',
    'doc',
    'docx',
    'dot',
    'xls',
    'xlsx',
    'ppt',
    'pptx',
    'pps',
    'pdf',
    'html',
    'js',
    'sh',
    'bat',
    'shell',
    'asp',
    'jar',
    'vbs',
    'wsf',
    'wsh',
    'xml',
    'lnk',
    'msi',
    'swf',
    'lnk',
    'elf'
  ];
  const suffix = file.name.substring(file.name.lastIndexOf('.') + 1);
  return mimeTypeList.some(item => item === suffix);
}

// 根据是开发环境还是生产环境 返回对应的登录链接地址
export function getLoginUrl() {
  const host = process.env.NODE_ENV === 'production' ? `${window.location.protocol}//${window.location.hostname}` : 'http://**************';
  return `${host}:60001/#/login`;
}

// 根据是开发环境还是生产环境 返回对应的搜索主界面地址
export function getMainSearchUrl() {
  const host = process.env.NODE_ENV === 'production' ? `${window.location.protocol}//${window.location.hostname}` : 'http://localhost';
  return `${host}:60004/#/portal/search`;
}

// 根据是开发环境还是生产环境 返回对应的系統更新地址
export function getUpdateUrl() {
  const host = process.env.NODE_ENV === 'production' ? `${window.location.protocol}//${window.location.hostname}` : 'http://**************';
  return `${host}:60001/#/system-config/sys/update`;
}

/**
 * 10000 => "10,000"
 * @param {number} num
 */
export function toThousandFilter(num) {
  return (+num || 0).toString().replace(/^-?\d+/g, m => m.replace(/(?=(?!\b)(\d{3})+$)/g, ','));
}

// 大一点的数字转换
export function simplifyNum(number) {
  if (!number && number !== 0) return number;
  let str_num;
  if (number >= 1e3 && number < 1e6) {
    str_num = number / 1e4;
    return str_num + ' 万';
  } else if (number >= 1e6 && number < 1e7) {
    str_num = number / 1e6;
    return str_num + ' 百万';
  } else if (number >= 1e7 && number < 1e8) {
    str_num = number / 1e7;
    return str_num + ' 千万';
  } else if (number >= 1e8 && number < 1e10) {
    str_num = number / 1e8;
    return str_num + ' 亿';
  } else if (number >= 1e10 && number < 1e11) {
    str_num = number / 1e10;
    return str_num + ' 百亿';
  } else if (number >= 1e11 && number < 1e12) {
    str_num = number / 1e11;
    return str_num + ' 千亿';
  } else if (number >= 1e12) {
    str_num = number / 1e12;
    return str_num + ' 万亿';
  } else {
    // 一千以下
    return number;
  }
}
