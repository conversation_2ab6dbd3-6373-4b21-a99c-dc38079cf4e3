// 关键字的option选项
export const keywordOptionMap = [
  {
    label: '基本信息',
    options: [
      {
        label: 'ip地址（支持ip、ip掩码）',
        value: 'ip'
      },
      {
        label: '域名',
        value: 'ip.domain'
      },
      {
        label: '端口',
        value: 'ip.port'
      },
      {
        label: '国家',
        value: 'ip.country'
      },
      {
        label: '省份',
        value: 'ip.province'
      },
      {
        label: '城市',
        value: 'ip.city'
      },
      {
        label: '发现时间',
        value: 'ip.createtime'
      },
      {
        label: '更新时间',
        value: 'ip.updatetime'
      },
      {
        label: '组织',
        value: 'ip.org'
      },
      {
        label: 'ISP',
        value: 'ip.isp'
      },
      {
        label: '操作系统',
        value: 'ip.os'
      },
      {
        label: 'APP应用',
        value: 'ip.app'
      },
      {
        label: '标签',
        value: 'ip.tag'
      }
    ]
  },
  {
    label: 'header信息',
    options: [
      {
        label: 'header信息',
        value: 'header.title'
      },
      {
        label: '状态码',
        value: 'header.statuscode'
      },
      {
        label: '服务',
        value: 'header.server'
      }
    ]
  },
  {
    label: 'web网站信息',
    options: [
      {
        label: '网站标题',
        value: 'web.title'
      },
      {
        label: '网站正文',
        value: 'web.body'
      },
      {
        label: '网站icon',
        value: 'web.title'
      },
      {
        label: '网站标题',
        value: 'web.icon'
      },
      {
        label: '是否存在web',
        value: 'web.isweb'
      },
      {
        label: '网站标签',
        value: 'web.tag'
      }
    ]
  },
  {
    label: 'ICP备案信息',
    options: [
      {
        label: '备案号',
        value: 'icp.number'
      },
      {
        label: '备案网站',
        value: 'icp.webname'
      },
      {
        label: '备案单位',
        value: 'icp.company'
      }
    ]
  },
  {
    label: '协议信息',
    options: [
      {
        label: '协议名称',
        value: 'proto.name'
      },
      {
        label: '传输层协议',
        value: 'proto.transport'
      },
      {
        label: '端口响应',
        value: 'proto.banner'
      }
    ]
  },
  {
    label: '证书信息',
    options: [
      {
        label: '证书名称',
        value: 'cert.name'
      },
      {
        label: '证书使用者',
        value: 'cert.subject'
      },
      {
        label: '证书使用组织',
        value: 'cert.subject.org'
      },
      {
        label: '证书颁发者',
        value: 'cert.issuer'
      },
      {
        label: '证书颁发组织',
        value: 'cert.issuer.org'
      },
      {
        label: '证书签名算法',
        value: 'cert.sha'
      },
      {
        label: '证书状态',
        value: 'cert.status.is_expired'
      }
    ]
  },
  {
    label: 'ASN信息',
    options: [
      {
        label: 'ASN编号',
        value: 'asn.number'
      },
      {
        label: 'ASN名称',
        value: 'asn.name'
      },
      {
        label: 'ASN注册机构',
        value: 'asn.org'
      }
    ]
  }
];
// 查询逻辑的option选项
export const logicOption = [
  {
    label: '查询逻辑',
    options: [
      {
        label: '或（或者）',
        value: '||'
      },
      {
        label: '且（包含）',
        value: '&&'
      }
      // {
      //   label: '非（去除）',
      //   value: '!='
      // }
    ]
  }
];
// 通用查询的右键菜单配置
export const inquireConfig = [
  {
    label: '关联分析',
    key: 'relation',
    icon: 'zr-data-analysis'
  },
  {
    label: '查询结果',
    key: 'inquire',
    icon: 'zr-search-documents'
  },
  {
    label: '详情',
    key: 'details',
    icon: 'zr-module-fill'
  },
  {
    label: '复制',
    key: 'copy',
    icon: 'zr-copy-file'
  }
];
