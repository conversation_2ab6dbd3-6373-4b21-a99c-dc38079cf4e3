<template>
  <div :class="`layout_container_sub_app layout_container_${pageType}`">
    <!-- <MainHeader></MainHeader> -->
    <!-- <MainHeader :show-input="false"></MainHeader> -->
    <!-- 独立启动时 -->
    <template v-if="!powered">
      <individual-menu />
    <!-- <div class="main_content">
        <zr-card class="box-card">
          <router-view v-if="routerFlag" />
        </zr-card>
      </div> -->
    </template>

    <!-- 从qiankun加载时 -->
    <template v-else>
      <!-- 内容页，业务页面 -->
      <div class="main_content">
        <!-- 二级路由页 -->
        <zr-card class="box-card">
          <router-view v-if="routerFlag" />
        </zr-card>
      </div>
    </template>
  </div>
</template>

<script>
// import MainHeader from '@/components/common/MainHeader.vue';
import IndividualMenu from './components/IndividualMenu.vue';
import {
  User as ElIconUser,
  Setting as ElIconSetting,
  SwitchButton as ElIconSwitchButton
} from '@element-plus/icons-vue';
export default {
  name: 'Layout',
  components: {
    IndividualMenu
    // MainHeader
  },
  provide() {
    return {
      refreshRouterView: this.refreshRouterView
    };
  },
  data() {
    return {
      // 路由组件vif标记，主要用作刷新路由页,
      routerFlag: true,
      powered: false,
      // 用户路由菜单集合
      userMenus: [],
      showListPop: false,
      ElIconUser,
      ElIconSetting,
      ElIconSwitchButton
    };
  },
  computed: {
    pageType() {
      return this.$router.currentRoute.value.fullPath !== '/search' ? 'page' : 'home';
    }
  },
  created() {
    this.powered = !!window.__POWERED_BY_QIANKUN__;
    // if (!this.$store.state.__POWERED_BY_QIANKUN__) {
    //   this.userMenus = this.$store.state.routes;
    // } else {
    //   ce.on('reload', window.$parentStore.state.activeApp.id, () => {
    //     this.refreshRouterView();
    //   });
    // }
    if (window.__POWERED_BY_QIANKUN__) {
      ce.on('reload', window.$parentStore.state.activeApp.id, () => {
        this.refreshRouterView();
      });
    }
  },
  mounted() {
  },
  beforeUnmount() {
    if (window.__POWERED_BY_QIANKUN__) {
      ce.off('reload', window.$parentStore.state.activeApp.id);
    }
  },
  methods: {
    refreshRouterView() {
      this.routerFlag = false;
      this.$nextTick(() => {
        this.routerFlag = true;
      });
    },
    // 用户信息 下拉事件
    handleCommand(e) {
      if (e in this) {
        this[e]();
      }
    },
    // 退出登录
    logOut() {
      // this.$confirmDialog("确定要退出登录吗？").then(() => {
      //   this.keycloak.logout();
      // }).catch(() => { })
    }
  }
};
</script>

<style lang="scss" scoped>
$bgColor: #0a1936;

.layout_container_sub_app {
  height: 100%;
  // display: flex;
  position: relative;
  // padding: 20px;
  /*// flex-flow: column;*/
  &.layout_container_page {
    background-color: #f5f7fa;
  }
  .left_content {
    width: 200px;
    background-color: $bgColor;
    display: flex;
    flex-flow: column;

    .logos {
      display: flex;
      align-items: center;
      height: 60px;
      padding-left: 20px;

      .logo_img {
        height: 20px;
      }

      .logo_txt {
        color: white;
        font-weight: 500;
        font-size: 16px;
        margin-left: 10px;
      }
    }

    .navi {
      width: 200px;
      background: $bgColor;
      box-sizing: border-box;
    }
  }

  .content_box {
    flex: 1;
    display: flex;
    width: 0px;
    flex-flow: column;

    .right_top_header {
      height: 60px;
      background-color: $bgColor;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding-right: 20px;

      .navi_btn {
        font-size: 16px;
        color: #cccccc;
        // 导航按钮
        transition: all 0.15s;

        .icon_arrow {
          margin-left: 5px;
        }

        cursor: pointer;

        &:hover {
          color: white;
        }

        &.acitve {
          color: #006eff;

          .icon_arrow {
            transform: rotate(180deg);
          }
        }
      }

      .pserson_info {
        display: flex;
        align-items: center;
        cursor: pointer;

        &.el-dropdown,
        &.el-dropdown-selfdefine {
          height: 100%;
        }

        .user_name {
          height: 100%;
          color: white;
          font-weight: 500;
          font-size: 16px;
          margin-left: 10px;
          display: flex;
          flex-flow: column;
          justify-content: center;
          align-items: center;
          font-size: 16px;
          justify-content: space-evenly;

          .user_roles {
            color: #006eff;
            font-size: 12px;
          }
        }
      }
    }

    &>.main_content {
      // flex: 1;
      // height: 0;
      // overflow: auto;
      padding: 14px;

      #sub_app {
        height: 100%;
        display: flex;
        flex: 1;

        &>div {
          flex: 1;
        }
      }
    }
  }

  &>.main_content {
    // flex: 1;
    // height: 0;
    // overflow: auto;
    padding: 14px;

    #sub_app {
      height: 100%;
      display: flex;
      flex: 1;

      &>div {
        flex: 1;
      }
    }
  }
}

.main_content {
  height: calc(100vh - 63px);
}
</style>
