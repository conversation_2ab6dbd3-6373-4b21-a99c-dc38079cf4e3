<template>
  <div v-show="show" class="navi_list_Pop" @click="bgClick">
    <!-- 一级菜单 -->
    <div class="one_list">
      <div v-for="(item, index) in list" :key="index + ''" class="one item">
        <div class="sub_item">
          <i :class="item.icon + ' icon_item'"></i><span class="item_text">{{
            item.title
          }}</span>
        </div>
        <!-- 二级 -->
        <div v-if="hasChildren(item)" class="two_list">
          <div
            v-for="(item, index) in item.children"
            :key="index + ''"
            class="two item"
          >
            <div
              :class="['sub_item', checkSelected(item) ? 'select' : '']"
              @click="toPush(item)"
            >
              <span class="item_text">{{ item.title }}</span>
            </div>
            <!-- 三级 -->
            <div v-if="hasChildren(item)" class="three_list">
              <div
                v-for="(item, index) in item.children"
                :key="index + ''"
                class="three item"
              >
                <div
                  :class="['sub_item', checkSelected(item) ? 'select' : '']"
                  @click="toPush(item)"
                >
                  <span class="item_text">{{ item.title }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { $on, $off, $once, $emit } from '../../utils/gogocodeTransfer';
import * as Vue from 'vue';
export default {
  name: 'NaviListPop',
  inject: ['refreshRouterView'],
  props: {
    list: Array, // 数据源。父级传入
    show: Boolean // 控制显示隐藏
  },
  emits: ['update:show'],
  data() {
    return {};
  },
  mounted() {},
  methods: {
    bgClick() {
      $emit(this, 'update:show', false);
    },
    hasChildren(item) {
      return item.children && item.children.length > 0;
    },
    // 检测是否选中
    checkSelected(item) {
      const flag = item.path === this.$route.path;
      return flag;
    },
    // 路由跳转时
    toPush(config) {
      if (config.path) {
        if (config.path !== this.$route.path) {
          this.$router.push(config.path);
        } else {
          this.refreshRouterView();
        }
      }
    }
  }
};
</script>

<style lang="scss">
.navi_list_Pop {
  z-index: 10000;
  position: absolute;
  top: 60px;
  left: 200px;
  right: 0px;
  bottom: 0px;
  box-sizing: border-box;
  background-color: #0411293a;
  .one_list {
    display: flex;
    width: 800px;
    padding: 24px;
    background-color: #041129;
    flex-wrap: wrap;
    .one {
      width: calc(100% / 4);
    }
  }
  .item {
    font-size: 14px;
    color: #666666;
    line-height: 22px;

    &.two {
      color: #cccccc;
      padding-left: 30px;
    }
    &.three {
      color: #cccccc;
      padding-left: 10px;
    }
  }
  .sub_item {
    cursor: pointer;
    transition: 0.15s all;
    margin-bottom: 18px;
    display: flex;
    justify-items: center;
    &:hover {
      color: white;
    }
    .icon_item {
      font-size: 20px;
      margin-right: 10px;
    }
    &.select {
      color: #006eff;
    }
  }
}
</style>
