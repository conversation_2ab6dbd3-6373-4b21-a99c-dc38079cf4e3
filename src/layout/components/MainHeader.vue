<template>
  <div :class="`main-header-wrap main-header-wrap-${pageType}`">
    <div class="main-header-content">
      <div class="main-header-content-logo">
        <img :src="require(`@/assets/logo/${pageType === 'page' ? 'logo_title_black' : 'logo_title_white'}.png`)" @click="goHome" />
      </div>
      <!-- <div class="main-header-content-search">
        <SearchInput></SearchInput>
      </div> -->
      <div class="main-header-content-right">
        <!-- 暂时写成静态的 -->
        <template v-if="pageType === 'home'">
          <span
            v-for="(item, key) in loginConfig"
            :key="key"
            class="main-header-content-right-login-btn"
            @click="openLoginRegisterCard(item)"
          >
            {{ item.label }}
          </span>
        </template>
        <template v-else>
          <zr-dropdown>
            <div class="main-header-content-right-handle">
              <!-- <Icon name="zr-user" class="el-icon--right"></Icon> -->
              <!-- <zr-button
                type="primary"
                left-icon="zr-user-fill"
                class="user-handle-icon"
                circle
              ></zr-button> -->
              <img :src="require('@/assets/imgs/user.svg')" alt="">
              <zr-link :underline="false">
                <span class="main-header-content-right-handle-text">
                  超级管理员
                </span>
                <Icon name="zr-down-fill" color="#919398"></Icon>
              </zr-link>
            </div>
            <template #dropdown>
              <zr-dropdown-menu>
                <zr-dropdown-item
                  v-for="(item, key) in userConfig"
                  :key="key"
                  @click.native="adminHandle(item)"
                >
                  <Icon :name="item.icon" />
                  {{ item.label }}
                </zr-dropdown-item>
                <!-- <zr-dropdown-item>
                  个人中心
                </zr-dropdown-item>
                <zr-dropdown-item divided>
                  退出登录
                </zr-dropdown-item> -->
              </zr-dropdown-menu>
            </template>
          </zr-dropdown>
        </template>
      </div>
    </div>
    <!-- 登录注册 -->
    <zr-dialog
      v-model="isLoginRegisterDialog"
      :width="480"
      class="login-dialog"
    >
      <template #header>
        <img class="login-dialog-header-img" :src="require('@/assets/logo/logo_title_black.png')" alt="">
      </template>
      <LoginRegisterCard
        v-if="isLoginRegisterDialog"
        :card-type="loginRegisterCardType"
        @change="LoginRegisterChange"
      >
      </LoginRegisterCard>
    </zr-dialog>
  </div>
</template>
<script setup>
// 组件
import LoginRegisterCard from '@/components/common/LoginRegisterCard/index.vue';
// 插件
import { useRouter } from 'vue-router';
const router = useRouter();
// 用户配置
const userConfig = reactive([
  {
    label: '个人中心',
    key: 'personalCenter',
    icon: 'zr-project-a-fill'
  },
  {
    label: '退出登录',
    key: 'logOut',
    divided: true,
    icon: 'zr-square-logout-fill'
  }
]);
// 登录配置
const loginConfig = reactive([
  {
    label: '登录',
    key: 'logIn'
  },
  {
    label: '注册',
    key: 'register'
  }
]);
// 回到首页
const goHome = () => {
  router.push({
    name: 'search'
  });
};
// 判断当前页面类型
const pageType = computed(() => {
  return router.currentRoute.value.fullPath !== '/search' ? 'page' : 'home';
});
// 用户的功能菜单
function adminHandle(itemConfig) {
  switch (itemConfig.key) {
    case 'personalCenter':
      router.push({
        name: 'personalCenter'
      });
      break;
    case 'logOut':
      goHome();
      break;
    default:
      break;
  }
}
// 登录注册的操作
const loginRegisterCardType = ref('');
const isLoginRegisterDialog = ref(false);
function openLoginRegisterCard(item) {
  loginRegisterCardType.value = item.key;
  isLoginRegisterDialog.value = true;
}
function LoginRegisterChange() {
  isLoginRegisterDialog.value = false;
}
</script>


<style lang="scss" scoped>
.main-header-wrap {
  width: 100%;
  height: 64px;
  background-color: #fff;
  border-bottom: 1px solid #e4e7ed;
  position: fixed;
  top:0;
  left:0;
  z-index:100;
  .main-header-content {
    height: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .main-header-content-logo {
      width: 30%;
      display: inline-block;
      vertical-align: middle;
      margin-left: 32px;
      padding-top: 5px;
      img {
        cursor: pointer;
        // width: 100px;
      }
    }
    .main-header-content-right {
      margin-right: 32px;
      .user-handle-icon {
        margin-right: 10px;
        background-color: #D9ECFF;
        border-color: transparent !important;
        :deep(.iconfont) {
          color: #1890FF !important;
          font-size: 18px;
        }
      }
      .main-header-content-right-handle {
        img {
          vertical-align: middle;
          margin-right: 8px;
        }
        .el-link {
          .main-header-content-right-handle-text {
            display: inline-block;
            margin-right: 10px;
            font-size: 14px;
            color: #606266;
          }
        }
      }
    }
    .main-header-content-search {
      width: 40%;
    }
  }
  &.main-header-wrap-home {
    background-color: transparent !important;
    border-color: transparent !important;
    .main-header-content-right-login-btn {
      display: inline-block;
      margin-right: 10px;
      color: #ffffff65;
      cursor: pointer;
      transition: all .3s;
      &:hover {
        color: #fff;
      }
    }
  }
  &.main-header-wrap-page {
    // background-color: #edeff5;
  }
}

.title {
  text-align: center;
  font-weight: 400;
  color: #303133;
  font-size: 20px;
  line-height: 23px;
  margin-top: -25px;
  margin-bottom: 30px;
}

.tip {
  text-align: center;

  span:first-child {
    vertical-align: top;
  }
}

.register-form {
  padding: 0 20px;
}
</style>
