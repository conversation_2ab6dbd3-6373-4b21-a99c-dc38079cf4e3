<template>
  <div
    id="individual-menu"
  >
    <zr-container>
      <zr-aside
        :width="!fold?'220px':'70px'"
        :class="sysLogoAddr?'logo-show':'logo-hide'"
      >
        <div
          v-if="!fold"
          class="logo"
        >
          <div
            v-if="sysLogoAddr"
            class="logo-img"
          >
            <img
              :src="sysLogoAddr"
              alt=""
            >
          </div>
          
          <h1 v-if="sysLogoAddr">{{ sysObj.sys_name }}</h1>
          <h1
            v-else
            style="margin-top: 28px;"
          >{{ sysObj.sys_name }}</h1>
        </div>
        <!-- <div
          v-else
          class="logo"
        >
          <img
            v-show="sysLogoAddr"
            class="logo-sole"
            :src="sysLogoAddr"
            alt=""
          >
        </div> -->
        <div class="scrollbar-item">
          <zr-menu
            :default-openeds="defaultOpeneds"
            background-color="#0A1936"
            text-color="#eee"
            active-text-color="#fff"
            :collapse="fold"
            :default-active="defaultActive"
            router
            @select="menuSelect"
          >
            <!-- 全部菜单 -->
            <template
              v-for="(item, index) in routerMenuList"
              :key="index"
            >
              <template v-if="item.path!=='/'">
                <!-- 一级菜单 -->
                <zr-menu-item
                  v-if="item.redirect"
                  :index="item.children[0].path"
                  @click="menuClick(item)"
                >
                  <zr-icon
                    :class="item.icon"
                    size="16"
                  />
                  <template #title>{{ item.meta.title }}</template>
                </zr-menu-item>
                <!-- 多级菜单 -->
                <zr-sub-menu
                  v-if="!item.redirect&&item.hidden"
                  :index="index+''"
                >
                  <template
                    #title
                  >
                    <zr-icon
                      :class="item.icon"
                      size="16"
                    />
                    <span>{{ item.meta.title }}</span>
                  </template>
                  <template
                    v-for="(ite,ind) in item.children?item.children:[]"
                    :key="ind"
                  >
                    <zr-menu-item
                      v-if="ite.hidden"
                      :index="ite.path"
                      @click="menuClick(item,ite)"
                      @mouseenter="collectId=ite.name"
                      @mouseleave="collectId=0"
                    >
                      <div
                        class="collect"
                      >
                        <div>
                          <!-- <zr-tooltip
                            class="box-item"
                            effect="dark"
                            :content="collectIdList.indexOf(ite.menu_id) === -1 ? '点击收藏' : '取消收藏'"
                            placement="bottom"
                          >
                            <zr-button
                              v-show="collectId===ite.name"
                              link
                              @click="collectClick(ite, collectIdList.indexOf(ite.menu_id) === -1)"
                            >
                              <Icon
                                :name="collectIdList.indexOf(ite.menu_id) === -1 ? 'zr-collection' : 'zr-collection-fill'"
                                :size="14"
                                color="#fff"
                              />
                            </zr-button>
                          </zr-tooltip> -->
                        </div>
                      </div>
                      {{ ite.meta.title }}
                    </zr-menu-item>
                  </template>
                </zr-sub-menu>
              </template>
            </template>
          </zr-menu>
        </div>
      </zr-aside>
      <zr-container>
        <zr-header>
          <div class="hrader-left">
            <div
              class="fold"
              @click="foldClick"
            >
              <p :class="fold?'iconfont zr-right-fold':'iconfont zr-left-fold'" />
            </div>
            <zr-breadcrumb separator="/">
              <!-- <zr-breadcrumb-item
                class="breadcrumb"
                @click="breadcrumbClick('home')"
              >首页</zr-breadcrumb-item> -->
              <zr-breadcrumb-item
                v-for="(item) in breadcrumbList"
                :key="item.path"
              ><p
                :class="item.leve>1?'breadcrumb':''"
                @click="breadcrumbClick(item)"
              >{{ item.meta.title }}</p>
              </zr-breadcrumb-item>
            </zr-breadcrumb>
          </div>

          <div class="toolbar">
            <zr-badge
              v-if="uploadProgressList.length>0"
              :is-dot="isDotFun()"
              class="item upload-window"
              @click="uploadClick"
            >
              <Icon
                name="zr-cloud-upload"
                size="20"
              />
            </zr-badge>
            
            <zr-avatar><Icon
              name="zr-user-fill"
              size="20"
              color="#1890FF"
            /></zr-avatar>
            <zr-dropdown @command="handleCommand">
              <span class="el-dropdown-link">
                {{ account.username }}<Icon
                  name="zr-down-fill"
                  style="margin-left:8px;"
                />
              </span>
              <template #dropdown>
                <zr-dropdown-menu>
                  <zr-dropdown-item command="edit_password"><zr-icon
                    name="Edit"
                    size="18"
                  />修改密码</zr-dropdown-item>
                  <zr-dropdown-item command="msg"><zr-icon
                    name="InfoFilled"
                    size="18"
                  />版本信息</zr-dropdown-item>
                  <zr-dropdown-item command="out_login"><zr-icon
                    name="SwitchButton"
                    size="18"
                  />注销</zr-dropdown-item>
                </zr-dropdown-menu>
              </template>
            </zr-dropdown>
          </div>
        </zr-header>
        <zr-main>
          <!-- 数据包上传进度窗口 -->
          <UploadProgress />

          <router-view />
        </zr-main>
      </zr-container>
    </zr-container>
    <!-- 修改密码 -->
    <zr-dialog
      v-model="passVisible"
      class="pass-dialog"
      title="修改密码"
      :width="480"
      :close-on-click-modal="false"
      :destroy-on-close="true"
    >
      <zr-form
        ref="resetFormRef"
        :rules="rules"
        :model="resetForm"
        label-width="100"
      >
        <zr-form-item
          label="原密码"
          prop="old_passwd"
        >
          <zr-input
            v-model="resetForm.old_passwd"
            type="password"
            maxlength="64"
            show-password
            placeholder="原密码"
            size="default"
          />
        </zr-form-item>
        <zr-form-item
          label="新密码"
          prop="password"
        >
          <zr-input
            v-model="resetForm.password"
            type="password"
            maxlength="64"
            show-password
            placeholder="新密码"
            size="default"
          />
        </zr-form-item>
        <zr-form-item
          label="确认新密码"
          prop="password_re"
        >
          <zr-input
            v-model="resetForm.password_re"
            type="password"
            maxlength="64"
            show-password
            placeholder="确认新密码"
            size="default"
          />
        </zr-form-item>
      </zr-form>
      <template #footer>
        <span class="dialog-footer">
          <zr-button
            type="primary"
            @click="saveClick(resetFormRef)"
          >确定</zr-button>
          <zr-button @click="passVisible=false">取消</zr-button>
        </span>
      </template>
    </zr-dialog>
    <!-- 版本信息 -->
    <zr-dialog
      v-model="msgVisible"
      class="msg-dialog"
      title="版本及授权信息"
      width="700px"
    >
      <div
        v-if="sysLogoAddr"
        class="msg-logo"
      ><img
        :src="sysLogoAddr"
        alt=""
      ></div>
      <div class="information">
        <h3>{{ sysObj.sys_name }}</h3>
        <p><span>系统版本：</span>{{ sysObj.sys_version || '- -' }}</p>
        <p><span>系统更新时间：</span>{{ sysObj.sys_update_time || '- -' }}</p>
        <zr-divider>产品授权信息</zr-divider>
        <p><span>系统ID：</span>{{ authData.system_id || '- -' }}<zr-button
          type="primary"
          link
          left-icon="zr-copy-file"
          @click="copyClick(authData.system_id)"
        /></p>
        <p><span>产品期限授权：</span>
          <span v-if="authData.product_authorized_status == 'Unauthorized'"><zr-tag type="danger">未授权</zr-tag></span>
          <span v-else-if="authData.product_authorized_status == ''">
            <zr-tag :type="authData.is_formal ? 'success' : 'warning'">{{authData.is_formal ? '正式授权' : '测试授权' }}：{{ authData.product_is_permanent ? '永久' : '截止' }}
              <span v-if="!authData.product_is_permanent">{{ authData.product_date }}</span>
            </zr-tag>
            <span v-if="authData.product_is_last_month" style="color: red;margin-left:20px">距授权到期不足1个月，请即时更新授权</span>
          </span>
        </p>
        <p><span>服务期限授权：</span>
          <span v-if="authData.service_authorized_status == 'Unauthorized'"><zr-tag type="danger">未授权</zr-tag></span>
          <span v-else-if="authData.service_authorized_status == ''">
            <zr-tag :type="authData.is_formal ? 'success' : 'warning'">{{authData.is_formal ? '正式授权' : '测试授权' }}：{{ authData.service_is_permanent ? '永久' : '截止' }}
              <span v-if="!authData.service_is_permanent">{{ authData.service_date }}</span>
            </zr-tag>
            <span v-if="authData.service_is_last_month" style="color: red;margin-left:20px">距授权到期不足1个月，请及时更新授权</span>
          </span>
        </p>
        <p><zr-button
          type="primary"
          link
          @click="authorizationClick"
        >请输入授权码</zr-button></p>
      </div>
      <div
        v-if="sysObj.sys_copyright"
        class="copyright"
      ><span>{{ sysObj.sys_copyright }}</span></div>
    </zr-dialog>
    <!-- 授权码 -->
    <zr-dialog
      v-model="authVisible"
      class="msg-dialog"
      title="授权"
      :width="480"
      :close-on-click-modal="false"
      center
      @closed="authTextarea=''"
    >
      <zr-input
        v-model="authTextarea"
        placeholder="请输入有效授权码"
        :rows="5"
        type="textarea"
      />
      <template #footer>
        <span class="dialog-footer">
          <zr-button
            type="primary"
            @click="authClick"
          >确定</zr-button>
          <zr-button @click="authVisible=false">取消</zr-button>
        </span>
      </template>
    </zr-dialog>
    <!-- 授权提示 -->
    <span class="hintDialog">
    <zr-dialog
      v-model="hintVisible"
      :width="480"
      :show-close="false"
      :close-on-click-modal="false"
      center
    >
      <div style="line-height: 30px">
        系统尚未授权或者授权已过期，已限制部分功能使用
        <p style="display: flex;align-items: center;justify-content: center;">
          请输入授权码更新授权：<zr-button
            type="primary"
            link
            @click="authorizationClick"
          >请输入授权码</zr-button>
        </p>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <zr-button
            type="primary"
            :disabled="hintNum>0"
            @click="hintVisible=false"
          >确定<span v-if="hintNum>0"> ({{ hintNum }})</span></zr-button>
        </span>
      </template>
    </zr-dialog>
  </span>
  </div>
</template>
<script setup>
import { ref, getCurrentInstance } from 'vue';
import { logoutApi, resetSelfPassword, getAuthMsg, collectApi, collectList, unCollectApi, authorizationApi } from '@/api/login/index';
import { dateFormat } from '@/utils/formatting';
import { ZrMessageBox, ZrMessage, ZrLoading } from 'qianji-ui';
import { useCookies } from 'vue3-cookies';
import { router } from '@/main';
import UploadProgress from '@/components/common/UploadProgress.vue';
import { useStore } from '@/store/store';
import { storeToRefs } from 'pinia';
import { transformComponent } from '@/router/getRoutes/getRoutes';
const { proxy } = getCurrentInstance();
const store = useStore();
const { uploadProgressList, routerMenuList, collectIdList } = storeToRefs(store);
const { cookies } = useCookies();
const fold = ref(false);
const msgVisible = ref(false);
const passVisible = ref(false);
const resetFormRef = ref();
const resetForm = ref({});
const defaultActive = ref('/threat/mirror-angle');
const sysCopyright = ref(''); // 版本信息
const sysLogoAddr = ref(''); // logo
const defaultOpeneds = ref(['0']);
const sysObj = ref({});

const account = JSON.parse(localStorage.getItem('form'));
// 路由地址锁定
const address = sessionStorage.getItem('defaultActive');
defaultActive.value = sessionStorage.getItem('defaultActive');
// 面包屑锁定
const breadcrumb = sessionStorage.getItem('breadcrumbList');
const rules = reactive({
  "old_passwd": [
    { required: true,
      message: '原密码不能为空',
      trigger: 'bulr' }
  ],
  "password": [
    { required: true,
      message: '密码不能为空',
      trigger: 'bulr' }
  ],
  "password_re": [
    { required: true,
      message: '确认密码不能为空',
      trigger: 'bulr' }
  ]
});
const breadcrumbList = ref([]);
const collectId = ref();
const authVisible = ref(false);
const authTextarea = ref('');
const hintVisible = ref(false);
const hintNum = ref(5);
const authData = ref({});

function authorizationClick() {
  authVisible.value = true;
}
function authClick() { // 授权
  if (authTextarea.value === '') {
    ZrMessage.error('请输入授权码！');
    return;
  }
  authorizationApi({ 'auth_code': authTextarea.value }).then(res => {
    if (res.data.code === 0) {
      ZrMessage.success('授权成功');
      authVisible.value = false;
      getAuthMsgFun();
    } else {
      ZrMessage.error(res.data.msg);
    }
  });
}

const saveClick = async(formEl) => { // 修改密码
  if (!formEl) return;
  await formEl.validate((valid, fields) => {
    if (valid) {
      const form = JSON.parse(localStorage.getItem('form'));
      const obj = {
        ...resetForm.value,
        username: form.username
      };
      resetSelfPassword(obj).then(res => {
        passVisible.value = false;
        ZrMessage.success(res.data.msg);
        ZrMessageBox.confirm('密码修改成功，请重新登录！', '重新登录', {
          confirmButtonText: '确定',
          showClose: false,
          showCancelButton: false,
          closeOnClickModal: false,
          closeOnPressEscape: false,
          type: 'warning'
        })
          .then(() => {
            cookies.remove('dsp_token');
            // localStorage.removeItem('menuList');
            router.push({ name: 'Login' });
          });
      });
    } else {
      console.log('error submit!', fields);
    }
  });
};

watch(() => router.currentRoute.value, (newVal) => { // 路由监听
  defaultActive.value = newVal.path; // 菜单高亮
  sessionStorage.setItem('defaultActive', newVal.path);
  menuClick(newVal.matched[0], newVal.matched[1]); // 面包屑更新
});

// onBeforeMount(() => {
// });

// function collectClick(row, collect) { // 菜单收藏
//   var obj = {
//     'menu_ids': [row.menu_id]
//   };
//   if (collect) {
//     collectApi(obj).then(res => {
//       if (res.data.code === 0) {
//         ZrMessage.success('收藏成功');
//         collectFun();
//       } else {
//         ZrMessage.error(res.data.msg);
//       }
//     });
//   } else {
//     unCollectApi(obj).then(res => {
//       if (res.data.code === 0) {
//         ZrMessage.success('取消收藏成功');
//         collectFun();
//       } else {
//         ZrMessage.error(res.data.msg);
//       }
//     });
//   }
// }

// function collectFun() { // 收藏菜单之后，刷新我的收藏
//   var list = [];
//   collectList().then(res => {
//     const routeList = routerMenuList.value;
//     routeList.shift();
//     const newCollect = [];
//     collectIdList.value = res.data.data.map(item => item.menu_id);
//     res.data.data.forEach(item => {
//       item.path = '/collect' + item.path;
//       item.name = 'Collect' + item.name;
//       list.push(item);
//     });
//     newCollect.unshift({
//       'path': '/collect',
//       'component': 'Layout',
//       'name': 'Collect',
//       'icon': 'iconfont zr-collection',
//       'hidden': true,
//       'meta': {
//         'title': '我的收藏'
//       },
//       'sort': 999,
//       children: list
//     });
//     routeList.unshift(newCollect[0]);
//     routerMenuList.value = routeList;
//     router.removeRoute('Collect');
//     newCollect.forEach(route => {
//       transformComponent(route);
//       if (route.children && route.children.length > 0) {
//         route.children.forEach(child => {
//           transformComponent(child);
//           if (child.children && child.children.length > 0) {
//             child.children.forEach(sun => {
//               transformComponent(sun);
//             });
//           }
//         });
//       }
//       router.addRoute(route);
//     });
//   });
// }
function handleCommand(command) { // 退出登录
  if (command == 'out_login') {
    const token = cookies.get('dsp_token');
    ZrMessageBox.confirm('确定注销并退出系统吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        logoutApi({
          headers: { Token: token }
        }).then(res => {
          ZrMessage.success(res.data.msg);
          cookies.remove('dsp_token');
          // localStorage.removeItem('menuList');
          sessionStorage.removeItem('defaultActive');
          sessionStorage.removeItem('breadcrumbList');
          router.push({ name: 'Login' });
        });
      });
  } else if (command == 'msg') {
    getAuthMsgFun();
  } else if (command == 'edit_password') {
    passVisible.value = true;
  }
}
getAuthMsgFun(true);
function getAuthMsgFun(bool) { // 授权信息
  var obj = {
    'business_type': 'mfp'
  };
  getAuthMsg(obj).then(res => {
    authData.value = res.data.data;
    // 授权提示
    if (bool && authData.value.product_authorized_status == 'Unauthorized') {
      hintVisible.value = true;
      var interval;
      if (hintNum.value > 0) {
        interval = setInterval(() => {
          hintNum.value--;
        }, 1000);
      } else {
        clearInterval(interval);
      }
    } else {
      hintVisible.value = false;
    }
  }).finally(() => {
    if (!bool) {
      msgVisible.value = true;
    }
  });
}
function foldClick() { // 菜单折叠
  fold.value = !fold.value;
}
function menuSelect(index) { // 路由地址记录-锁定用
  defaultActive.value = index;
  sessionStorage.setItem('defaultActive', index);
}
function menuClick(item, ite) { // 记录面包屑
  breadcrumbList.value = [];
  var arr = [];
  if (ite) {
    arr = [
      { meta: item.meta, path: item.path, name: item.name, leve: 1 },
      { meta: ite.meta, path: ite.path, name: ite.name, leve: 2 }
    ];
  } else {
    arr = [
      { meta: item.meta, path: item.path, name: item.name, leve: 1 }
    ];
  }

  breadcrumbList.value.push(...arr);
  sessionStorage.setItem('breadcrumbList', JSON.stringify(arr));
}
function breadcrumbClick(item) {
  if (item == 'home') {
    defaultActive.value = routerMenuList.value[0].children[0].path;
    router.push({ name: routerMenuList.value[0].children[0].name });
  }
  if (item.leve > 1) {
    router.push({ name: item.name });
  }
}
const loading = ZrLoading.service({
  lock: true,
  text: '拼命加载中...',
  background: 'rgba(255,255,255,.8)'
});
setTimeout(() => {
  axios({
    url: window.BASE_URL + '/eos-service/api/v6/system/sys_info/logo/mfp',
    method: 'get',
    responseType: 'blob'
  }).then(({ data }) => {
    if (data.type == 'application/octet-stream') {
      const blob = new Blob([data]); // 返回的文件流数据
      const url = window.URL.createObjectURL(blob); // 将他转化为路径
      sysLogoAddr.value = url;
    } else {
      sysLogoAddr.value = '';
    }
  });
  // routeList.value = JSON.parse(localStorage.getItem('menuList'));
  var list = routerMenuList.value;
  defaultActive.value = (address && address !== '/') ? address : list[1].children[0].path;
  breadcrumbList.value = JSON.parse(breadcrumb) || [list[1], list[1].children[0]];
  sysObj.value = JSON.parse(localStorage.getItem('sysLocal'));
  loading.close();
}, 500);

function isDotFun() {
  var a = 0;
  uploadProgressList.value.map(item => {
    if (item.percentCompleted < 100) {
      a++;
    }
  });
  return a > 0;
}
function uploadClick() {
  var e = document.getElementById('upload-progress');
  if (e.style.visibility === 'hidden') {
    e.style.visibility = 'visible';
  } else {
    e.style.visibility = 'hidden';
  }
}
function copyClick(text) { // 复制
  proxy.copyFun(text);
}

</script>
<style lang="scss" scoped>
#individual-menu{
    width: 100%;
    height: 100%;
    .el-container{
        width: 100%;
        height: 100%;
    }
}
.breadcrumb{
  cursor: pointer;
}
.breadcrumb:hover{
  color: #1890FF;
}
.el-aside{
    position: relative;
    background: #0A1936;
    &.logo-show{
      padding-top: 120px;
      .logo{
        height: 120px;
        width: 220px;
      }
    }
    &.logo-hide{
      padding-top: 80px;
      .logo{
        height: 80px;
        width: 220px;
      }
    }
    .scrollbar-item{
        background: #0A1936;
    }
    .logo{
        // width: 100%;
        // height: 80px;
        // height: 120px;
        // width: 220px;
        position: fixed;
        z-index: 999;
        // position: absolute;
        top: 0;
        left: 0;
        background: #0A1936;
          text-align: center;
        .logo-img{
          width: 90px;
          height: 35px;
          margin: 23px auto 10px;
          >img{
            width: 100%;
            height: 100%;
          }
        }
        .logo-sole{
          width: 35px;
          height: 35px;
          margin: 23px 0 10px;
        }
        h1{
          font-style: normal;
          font-size: 18px;
          line-height: 26px;
          // font-weight: 400;
          color: #fff;
        }
        p{
          font-size: 13px;
          line-height: 22px;
          color: #A5ADBA;
          margin: 10px 0 23px;
        }
    }
}
.el-main{
    background: #eee;
    // background: #F5F7FA;
}
.el-menu{
    border-right: none;
    // .el-sub-menu:nth-child(1){
    //   position: fixed;
    //   top: 120px;
    //   left: 0;
    // }
}
.el-menu-item{  //二级菜单未选中时的颜色
  color: #bdb8b8
}
.el-menu-item.is-active{
    background: rgba(10, 120, 200, 1) !important;
    color: #fff
}
.el-menu-item:hover{
    background: rgba(10, 120, 200, 1) !important;
    color: #fff
}
// .el-sub-menu:nth-child(1){
//   display: none;
// }
.el-sub-menu .el-menu-item{
    // background: #172644;
    background: #223355;
    width: 100%;
    // display: flex;
    // justify-content: space-between;
    padding-left: 20px !important;
    // padding-left: 48px !important;
  }
.icon-size{
    width: 1rem;
    height: 1rem;
    margin-right: 8px;
}
.el-header{
    background: #fff;
    text-align: right;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #DCDFE6;
    .hrader-left{
      display: flex;
      align-items: center;
      .fold{
        margin-right: 20px;
        .zr-left-fold:before{
          font-size: 20px;
          font-weight: bold;
          cursor: pointer;
        }
        .zr-right-fold:before{
          font-size: 20px;
          font-weight: bold;
          cursor: pointer;
        }
      }
    }
    .toolbar{
        display: flex;
        align-items: center;
    }
    .el-avatar--circle{
        margin-right: 10px;
    }
    .el-dropdown-link{
      cursor: pointer;
    }
}
.el-header .el-avatar--circle{
  background: #D9ECFF;
}
.msg-dialog{
  .msg-logo{
    width: 100%;
    padding: 10px;
    text-align:center;
    /* background: #0A1936; */
    color: #fff;
    img{
      width: 150px;
      height: 60px;
    }
  }
  .information{
    margin: 0 0 50px;
    h3{
      margin-bottom: 20px;
      font-size: 18px;
      font-weight: 600;
    }
    p{
      margin-bottom: 6px;
    }
    :deep(.el-divider__text.is-center){
      font-weight:600
    }
    /* // p:last-child{
    //   display: flex;
    //   align-items: center;
    //   justify-content: center;
    // } */
  }

  .copyright{
    font-size: 14px;
    // color: #999;
    margin-top: 15px;
    text-align: center;
    font-weight:600;
    span{
      /* // color: #1890FF; */
    }
  }
}
:deep(.el-dialog__body){
    /* text-align: center; */
    /* padding: 10px 0 32px; */
    padding:25px 40px;
    font-weight:600 ;
  }
  .el-dropdown-menu__item i{
    margin-right: 8px;
  }
.upload-window{
  cursor: pointer;
}
.collect{
  width: 30px;
  height: 18px;
  line-height: 0;
}

:deep(.el-sub-menu__title){
  font-weight: 700;
}
.hintDialog{
  :deep(.el-dialog__body){
    text-align:center
  }
}
</style>
    
  
