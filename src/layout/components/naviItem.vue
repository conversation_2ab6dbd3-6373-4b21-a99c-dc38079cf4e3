<template>
  <div
    :class="[
      'navi_item',
      'level' + level,
      selectd ? 'selectd' : '',
      expand ? 'expand' : '',
    ]"
    @click="click"
  >
    <i :class="config.icon" style="font-size: 20px"></i>
    <div class="name">{{ config.title }}</div>
  </div>
</template>

<script>
import { $on, $off, $once, $emit } from '../../utils/gogocodeTransfer';
import * as Vue from 'vue';
export default {
  props: {
    config: Object, // 菜单配置项{icon:"",title:""}
    level: {
      type: [String, Number], // 层级 1、2}
      default: '1'
    },
    selectd: Boolean, // 是否选中
    expand: Boolean // 是否展开（作为一级目录时）
  },
  emits: ['click'],
  data() {
    return {};
  },
  mounted() {},
  methods: {
    click(...args) {
      $emit(this, 'click', ...args);
    }
  }
};
</script>

<style lang="scss">
.navi_list {
  transition: 0.25s all;
  .navi_item {
    color: rgba(255, 255, 255, 0.651);
    font-size: 15px;
    line-height: 50px;
    display: flex;
    align-items: center;
    transition: 0.25s all;
    cursor: pointer;
    box-sizing: border-box;
    padding-left: 10px;
    &:not(.expand, .selectd):hover {
      background-color: #1c4e80;
      color: white;
    }
    &.expand:hover {
      color: #7abcff;
    }
    .name {
      margin-left: 10px;
    }
    &.level2 {
      .name {
        margin-left: 30px;
      }
    }
    &.level3 {
      .name {
        margin-left: 40px;
      }
    }
    &.selectd {
      background-color: #53a8ff;
      color: white;
      position: relative;
      // &:before {
      //   position: absolute;
      //   content: "";
      //   top: 0;
      //   bottom: 0;
      //   left: 0;
      //   width: 3px;
      //   background-color: #409eff;
      // }
    }
    &.expand {
      color: #53a8ff;
    }
  }
}
</style>
