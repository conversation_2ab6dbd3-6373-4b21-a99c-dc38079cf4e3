<template>
  <div class="navi_list">
    <template v-if="myList && myList.length > 0">
      <div v-for="(item, index) in myList" :key="index + ''">
        <!-- 一级目录 -->
        <div :class="['navi_i', item.expand ? 'turn' : '']">
          <naviItem
            :config="item"
            :selectd="checkSelected(item)"
            :expand="item.expand"
            @click="expand(item, 1)"
          />

          <!-- 如果二级目录存在 -->
          <!-- <el-icon class="icon_arrow"><el-icon-arrow-down /></el-icon> -->
        </div>
        <div
          v-if="item.children && item.children.length > 0"
          :class="['sub_list', item.expand ? 'expandOn' : 'expandOff']"
        >
          <div
            v-for="(item, son) in item.children"
            :key="index + 'son' + son"
            class="sub_item"
          >
            <template v-if="item.children && item.children.length > 0">
              <div :class="['navi_i', item.expand ? 'turn' : '']">
                <naviItem
                  level="2"
                  :selectd="checkSelected(item)"
                  :config="item"
                  @click="expand(item, 2)"
                />
                <!-- <el-icon class="icon_arrow"><el-icon-arrow-down /></el-icon> -->
              </div>
              <!-- 如果三级菜单存在 -->
              <div
                :class="['sub_list', item.expand ? 'expandOn' : 'expandOff']"
              >
                <div
                  v-for="(item, three) in item.children"
                  :key="three + 'three'"
                  class="sub_item"
                >
                  <!-- 三级菜单 -->
                  <naviItem
                    level="3"
                    :selectd="checkSelected(item)"
                    :config="item"
                    @click="toPush(item)"
                  />
                </div>
              </div>
            </template>
            <template v-else>
              <!-- 单个二级菜单 -->
              <naviItem
                level="2"
                :selectd="checkSelected(item)"
                :config="item"
                @click="toPush(item)"
              />
            </template>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script>
import { ArrowDown as ElIconArrowDown } from '@element-plus/icons-vue';
import * as Vue from 'vue';
import naviItem from './naviItem.vue';
import _ from 'lodash';
export default {
  // 导航栏组件
  name: 'Navi',
  components: {
    naviItem,
    ElIconArrowDown
  },
  props: {
    list: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      myList: [],
      // myList: [
      //   {
      //     title: "首页", icon: "el-icon-house", path: "/",
      //     children: []
      //   },
      //   {
      //     title: "系统管理", icon: "el-icon-setting", path: "",
      //     children: [
      //       { title: "用户管理", icon: "", path: "/sys/user", },
      //       { title: "角色管理", icon: "", path: "/sys/role", },
      //       { title: "菜单管理", icon: "", path: "/sys/menu", },
      //     ]
      //   },
      //   {
      //     title: "其他管理", icon: "el-icon-star-off", path: "",
      //     children: [
      //       { title: "管理1", icon: "", path: "/other/router1", },
      //       { title: "管理2", icon: "", path: "/other/router2", },
      //       { title: "管理3", icon: "", path: "/other/router3", },
      //     ]
      //   }
      // ],
      // 记住上一个被展开的一级目录
      lastExpand: null,
      // 记住上一个展开的二级目录
      lastTwoExpand: null
    };
  },
  watch: {
    list: {
      deep: true,
      immediate: true,
      handler(v) {
        this.myList = _.cloneDeep(v);
        this.$nextTick(() => {
          this.checkExpand();
        });
      }
    }
  },
  mounted() {},
  methods: {
    // 路由跳转时
    toPush(config) {
      if (config.path) {
        if (config.path !== this.$route.path) {
          this.$router.push(config.path);
        } else {
          this.$parent.refreshRouterView();
        }
      }
    },
    // 检测是否选中
    checkSelected(item) {
      const flag = item.path === this.$route.path;
      return flag;
    },
    // 监测一级目录是否默认展开
    checkExpand() {
      // let flag = item.router === this.$route.path;
      // if (flag) {
      this.myList.forEach((one, index) => {
        if (one.children && one.children.length > 0) {
          const twoChild = one.children.find(
            (two) => two.path == this.$route.path
          );
          // 如果找到展开的一级目录
          if (twoChild) {
            one['expand'] = true;
            this.lastExpand = one;
          } else {
            one.children.forEach((two) => {
              // 如果找到展开的二级目录
              if (two.children && two.children.length > 0) {
                const threeChild = two.children.find(
                  (three) => three.path == this.$route.path
                );
                if (threeChild) {
                  one['expand'] = true;
                  this.lastExpand = one;
                  two['expand'] = true;
                  this.lastTwoExpand = two;
                }
              }
            });
          }
        }
      });
      // }
    },
    // 菜单可以展开时
    expand(item, level) {
      if (item.children && item.children.length > 0) {
        item['expand'] = !item.expand;

        if (level == 1) {
          if (this.lastExpand && this.lastExpand != item) {
            this.lastExpand.expand = false;
          }
          this.lastExpand = item;
        } else if (level == 2) {
          if (this.lastTwoExpand && this.lastTwoExpand != item) {
            this.lastTwoExpand = item;
          }
        }
      } else {
        this.toPush(item);
      }
    },
    getChildHeight(item, level) {
      if (item.expand) {
        // 单个菜单栏的高度（假设1/2/3级菜单高度相同）
        const h = document.querySelector('.navi_item').clientHeight;
        const two = item.children.length; // 有多少二级菜单
        let three = 0; // 有多少三级菜单

        if (item.children && level == 3) {
          item.children.forEach((each, index) => {
            if (each.children) {
              each.children.forEach((son, index) => {
                three++;
              });
            }
          });
        }
        const total = (two + three) * h + 'px';
        return total;
      } else {
        return 0;
      }
    }
  }
};
</script>

<style lang="scss">
.navi_list {
  .navi_i {
    position: relative;
    transition: 0.25s all;

    .icon_arrow {
      position: absolute;
      right: 15px;
      top: calc(50% - 8px);
      font-size: 16px;
      color: rgba(255, 255, 255, 0.651);
      transition: 0.25s all;
      transform: rotate(0deg);
    }
    &.turn {
      .icon_arrow {
        transform: rotate(180deg);
      }
    }
  }
  .sub_list {
    background-color: #041129;
    overflow: hidden;
    transition: 0.25s all;
    .sub_item {
      position: relative;
    }
  }
  .expandOn {
    height: auto;
  }
  .expandOff {
    height: 0;
  }
}
</style>
