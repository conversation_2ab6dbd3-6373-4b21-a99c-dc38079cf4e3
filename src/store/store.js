import { defineStore } from 'pinia';
export const useStore = defineStore('store', {
  state: () => {
    return { metaSearchValue: '', uploadProgressList: [], routeBool: false, routerMenuList: [], collectIdList: [] };
  },
  getters: {},
  actions: {
    changeSearchValue(data) {
      this.metaSearchValue = data;
    },
    uploadProgressFun(arr) {
      this.uploadProgressList = arr;
    }
  },
  persist: {
    enabled: true,
    strategies: [
      {
        key: 'uploadProgressList',
        storage: sessionStorage
      }
    ]
  }
});
