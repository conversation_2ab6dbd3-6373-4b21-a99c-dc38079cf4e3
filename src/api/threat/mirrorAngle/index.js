import { deleteRequest, postRequest, getRequest, putRequest } from '../../request';
// 内存镜像视角接口

// 镜像统计
export const getCount = (data) => {
  return postRequest('/mfp-service/api/v1/images/count', data);
};

// 威胁等级结果统计
export const getRisklevelCount = (data) => {
  return getRequest('mfp-service/api/v1/images/risk_level_count', data);
};

// 研判结果统计
export const getJudgementCount = (data) => {
  return getRequest('mfp-service/api/v1/images/judgement_count', data);
};


// 列表参数下拉内容获取
export const getOptions = (data) => {
  return postRequest('/mfp-service/api/v1/images/get_options', data);
};

// 镜像列表
export const getimagesList = (data) => {
  return getRequest('/mfp-service/api/v1/images', data);
};
    
// 镜像单条删除
export const deleteImages = (id, data) => {
  return deleteRequest('/mfp-service/api/v1/images/' + id, data);
};

// 批量删除
export const batchDeleteImages = id => {
  return postRequest('/mfp-service/api/v1/images/batch_delete', id);
};

// 内存镜像视角--研判
export const judgement = (id, data) => {
  return putRequest('/mfp-service/api/v1/judgement/images/' + id, data);
};


// 镜像详情概览信息
export const basicInfoList = (id) => {
  return getRequest('/mfp-service/api/v1/images/' + id + '/get_basic_info');
};


// 文件下载
export const fileDownload = (id, detectionRecordId) => {
  return getRequest('/mfp-service/api/v1/threat_detection/result_file/' + id + '/' + detectionRecordId + '/download');
};

// 文件详情
export const fileDetail = (id) => {
  return getRequest('/mfp-service/api/v1/threat_detection/result_file/' + id + '/detail');
};

// 检测结果状态
export const statusApi = (data, id) => {
  return getRequest('/mfp-service/api/v1/threat_detection/' + id + '/status', data);
};
// 内容分析
export const calculateApi = (data, id) => {
  return postRequest('/mfp-service/api/v1/threat_detection/' + id + '/status/start', data);
};

// 威胁检测

// 威胁检测结果
export const threatInfoApi = (id, data) => {
  return getRequest('/mfp-service/api/v1/threat_detection/' + id + '/threat_info', data);
};

// 威胁检测结果-详情
export const threatInfoDetailApi = (id, data) => {
  return getRequest('/mfp-service/api/v1/threat_detection/' + id + '/threat_info/' + data + '/detail');
};

// 威胁检测结果-详情威胁内容
export const threatInfoDetailContentApi = (id, data) => {
  return getRequest('/mfp-service/api/v1/threat_detection/' + id + '/threat_info/' + data.item_id + '/detail/content', data);
};

// 威胁研判结果
export const judgementResult = (id, data) => {
  return getRequest('/mfp-service/api/v1/judgement/threats/' + id + '/' + data);
};

// 更新规则研判
export const rulesJudgement = (id, data, obj) => {
  return putRequest('/mfp-service/api/v1/judgement/rules/' + id + '/' + data, obj);
};

// 更新规则研判
export const threatsJudgement = (id, data, obj) => {
  return putRequest('/mfp-service/api/v1/judgement/threats/' + id + '/' + data, obj);
};

// 研判备注上传图片
export const threatsUpload = (id, threatId, data) => {
  return postRequest('/mfp-service/api/v1/judgement/threats/' + id + '/' + threatId + '/upload', data);
};


// 分析报告

// 分析报告数据
export const analysisReport = (analysisId, id) => {
  return getRequest('/mfp-service/api/v1/threat_detection/analysis_report/' + analysisId + '/' + id + '/analysis_report');
};
