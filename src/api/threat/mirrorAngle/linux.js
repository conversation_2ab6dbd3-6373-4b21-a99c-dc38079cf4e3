import { getRequest, postRequest } from '../../request';

// 临时文件目录
export const scratchFileTreeList = (id) => {
  return getRequest('/mfp-service/api/v1/threat_detection/linux/' + id + '/tmpfs');
};

// 临时文件列表
export const scratchFileList = (data, id) => {
  return getRequest('/mfp-service/api/v1/threat_detection/linux/' + id + '/tmpfs_files', data);
};

// 内核模块列表
export const steinkernList = (data, id) => {
  return getRequest('/mfp-service/api/v1/threat_detection/linux/' + id + '/kernel_modules', data);
};

// 内核模块列表--转储
export const startDumpApi = (data, id) => {
  return postRequest('/mfp-service/api/v1/threat_detection/' + id + '/status/linux_module/start', data);
};

// 网卡信息列表
export const networkCardList = (data, id) => {
  return getRequest('/mfp-service/api/v1/threat_detection/linux/' + id + '/network_cards', data);
};

// 路由缓存列表
export const routeList = (data, id) => {
  return getRequest('/mfp-service/api/v1/threat_detection/linux/' + id + '/route_caches', data);
};

// ARP表列表
export const arpList = (data, id) => {
  return getRequest('/mfp-service/api/v1/threat_detection/linux/' + id + '/arps', data);
};

// 数据包队列列表
export const dataPacketList = (data, id) => {
  return getRequest('/mfp-service/api/v1/threat_detection/linux/' + id + '/pkt_queue', data);
};

// 网络包列表
export const networkPacketList = (data, id) => {
  return getRequest('/mfp-service/api/v1/threat_detection/linux/' + id + '/sk_buff_queue', data);
};

// 报告检索
export const searchReportLinux = (data, id) => {
  return getRequest('/mfp-service/api/v1/threat_detection/linux/' + id + '/search_report', data);
};


// 进程

// 进程树
export const courseTree = (id) => {
  return getRequest('/mfp-service/api/v1/threat_detection/linux/' + id + '/tree');
};

// 进程基本信息
export const basicList = (data, id) => {
  return getRequest('/mfp-service/api/v1/threat_detection/linux/' + id + '/' + data.id + '/basic');
};

// 内存映射信息
export const memMapList = (data, id, form) => {
  return getRequest('/mfp-service/api/v1/threat_detection/linux/' + id + '/' + data.id + '/mem_map', form);
};

// 内存映射--下载进程内存块
export const downloadMemory = (data, id, form) => {
  return getRequest('/mfp-service/api/v1/threat_detection/linux/' + id + '/' + data + '/ps_mem_map/download', form);
};

// 进程映射信息
export const psMapList = (data, id, form) => {
  return getRequest('/mfp-service/api/v1/threat_detection/linux/' + id + '/' + data.id + '/ps_map', form);
};

// 网络连接信息
export const sockStatList = (data, id, form) => {
  return getRequest('/mfp-service/api/v1/threat_detection/linux/' + id + '/' + data.id + '/sock_stat', form);
};

// 动态链接库信息
export const elfsList = (data, id, form) => {
  return getRequest('/mfp-service/api/v1/threat_detection/linux/' + id + '/' + data.id + '/elfs', form);
};

// 环境变量信息
export const envvarsList = (data, id, form) => {
  return getRequest('/mfp-service/api/v1/threat_detection/linux/' + id + '/' + data.id + '/envvars', form);
};
