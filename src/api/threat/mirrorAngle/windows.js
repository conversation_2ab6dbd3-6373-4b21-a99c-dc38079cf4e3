import { getRequest, postRequest, putRequest, deleteRequest } from '../../request';

// 注册表树结构
export const dirTree = (id) => {
  return getRequest('/mfp-service/api/v1/threat_detection/windows/' + id + '/registry_content/dir_tree');
};
// 注册表查询条件
export const filterCondition = (id) => {
  return getRequest('/mfp-service/api/v1/threat_detection/windows/' + id + '/registry_content/filter_condition');
};

// 注册表列表-点击
export const regeditList = (data, id) => {
  return getRequest('/mfp-service/api/v1/threat_detection/windows/' + id + '/registry_content', data);
};

// 注册表配置文件列表
export const regeditFileList = (data, id) => {
  return getRequest('/mfp-service/api/v1/threat_detection/windows/' + id + '/registry_file', data);
};

// 文件列表
export const fileList = (data, id) => {
  return getRequest('/mfp-service/api/v1/threat_detection/windows/' + id + '/file', data);
};

// 历史命令列表
export const commandList = (data, id) => {
  return getRequest('/mfp-service/api/v1/threat_detection/windows/' + id + '/command', data);
};

// 驱动模块列表
export const driverModuleList = (data, id) => {
  return getRequest('/mfp-service/api/v1/threat_detection/windows/' + id + '/driver_module', data);
};

// 服务SID列表
export const sidwList = (data, id) => {
  return getRequest('/mfp-service/api/v1/threat_detection/windows/' + id + '/sid', data);
};

// IE历史列表
export const ieList = (data, id) => {
  return getRequest('/mfp-service/api/v1/threat_detection/windows/' + id + '/ie', data);
};

// 内核函数列表
export const kernelFuncList = (data, id) => {
  return getRequest('/mfp-service/api/v1/threat_detection/windows/' + id + '/kernel_func', data);
};
// 内核函数--获取列表筛选项
export const getFilterCondition = (id, data) => {
  return getRequest('/mfp-service/api/v1/threat_detection/windows/' + id + '/kernel_func/filter_condition', data);
};

// 未加载模块列表
export const notLoadedModuleList = (data, id) => {
  return getRequest('/mfp-service/api/v1/threat_detection/windows/' + id + '/not_loaded_module', data);
};
// 符号链接列表
export const symbolLinkList = (data, id) => {
  return getRequest('/mfp-service/api/v1/threat_detection/windows/' + id + '/symbol_link', data);
};

// 报告检索
export const searchReportWindows = (data, id) => {
  return getRequest('/mfp-service/api/v1/threat_detection/windows/' + id + '/search_report', data);
};

// 进程

// 进程树
export const courseTree = (id) => {
  return getRequest('/mfp-service/api/v1/threat_detection/windows/' + id + '/tree');
};
// 进程基本信息
export const basicList = (data, id) => {
  return getRequest('/mfp-service/api/v1/threat_detection/windows/' + id + '/' + data.id + '/basic');
};
// 动态链接库信息
export const dllList = (data, id, form) => {
  return getRequest('/mfp-service/api/v1/threat_detection/windows/' + id + '/' + data.id + '/dll', form);
};
// 句柄信息
export const handleList = (data, id, form) => {
  return getRequest('/mfp-service/api/v1/threat_detection/windows/' + id + '/' + data.id + '/handle', form);
};
// 安全标识符信息
export const sidList = (data, id, form) => {
  return getRequest('/mfp-service/api/v1/threat_detection/windows/' + id + '/' + data.id + '/sid', form);
};
// 环境变量信息
export const envarList = (data, id, form) => {
  return getRequest('/mfp-service/api/v1/threat_detection/windows/' + id + '/' + data.id + '/envar', form);
};
// 内存映射信息
export const memMapList = (data, id, form) => {
  return getRequest('/mfp-service/api/v1/threat_detection/windows/' + id + '/' + data.id + '/mem_map', form);
};
// 网络连接信息
export const netScanList = (data, id, form) => {
  return getRequest('/mfp-service/api/v1/threat_detection/windows/' + id + '/' + data.id + '/net_scan', form);
};
// 线程块信息
export const threadScanList = (data, id, form) => {
  return getRequest('/mfp-service/api/v1/threat_detection/windows/' + id + '/' + data.id + '/thread_scan', form);
};
// PE版本信息
export const verInfoList = (data, id, form) => {
  return getRequest('/mfp-service/api/v1/threat_detection/windows/' + id + '/' + data.id + '/ver_info', form);
};
// Vad信息
export const vadInfoList = (data, id, form) => {
  return getRequest('/mfp-service/api/v1/threat_detection/windows/' + id + '/' + data.id + '/vad_info', form);
};
// 套接字信息
export const sockScanList = (data, id, form) => {
  return getRequest('/mfp-service/api/v1/threat_detection/windows/' + id + '/' + data.id + '/sock_scan', form);
};


// 密钥
// hashdump列表
export const hashDumpList = (id, form) => {
  return getRequest('/mfp-service/api/v1/threat_detection/windows/' + id + '/hash_dump', form);
};

// hashdump列表
export const lsaDumpList = (id, form) => {
  return getRequest('/mfp-service/api/v1/threat_detection/windows/' + id + '/lsa_dump', form);
};


// 检测结果标记
// 获取标记类型
export const getMarkList = (data, id, form) => {
  return getRequest('/mfp-service/api/v1/threat_detection/mark/' + data + '/' + id, form);
};

// 新增标记类型
export const addMark = (data, form) => {
  return postRequest('/mfp-service/api/v1/threat_detection/mark/' + data, form);
};

// 为检测结果新增标记
export const checkMark = (data, id, form) => {
  return putRequest('/mfp-service/api/v1/threat_detection/mark/' + data + '/' + id + '/mark', form);
};

// 为检测结果取消标记
export const cancleMark = (data, id, form) => {
  return putRequest('/mfp-service/api/v1/threat_detection/mark/' + data + '/' + id + '/unmark', form);
};

// 删除标记
export const deleteMark = (data, form) => {
  return deleteRequest('mfp-service/api/v1/threat_detection/mark/' + data, form);
};

// 自定义插件
// 获取插件列表
export const pluginResultsList = (id, form) => {
  return getRequest('/mfp-service/api/v1/plugin_results/search/' + id, form);
};
// 下载插件运行结果
export const pluginResultsDownload = (id, form) => {
  return getRequest('/mfp-service/api/v1/plugin_results/' + id + '/download', form);
};
