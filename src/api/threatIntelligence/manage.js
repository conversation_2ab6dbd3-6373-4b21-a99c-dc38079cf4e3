import { postRequest, getRequest } from '../request';

// 威胁情报列表
export const iocListApi = data => {
  return getRequest('/resources-center/api/v1/resources/rule/ioc', data);
};
  
// 威胁情报类型选项
export const typeOptions = data => {
  return getRequest('/resources-center/api/v1/resources/rule/ioc/typeOptions', data);
};

// 自定义威胁情报批量删除
export const batchDelete = data => {
  return postRequest('/resources-center/api/v1/resources/rule/ioc/batchDelete', data);
};

// 自定义威胁情报导入
export const importCustom = data => {
  return postRequest('/resources-center/api/v1/resources/rule/ioc/import', data);
};
