import { deleteRequest, postRequest, getRequest, putRequest } from '../../request';

// 获取全部来源选项
export const sourceOptions = data => {
  return getRequest('/resources-center/api/v1/resources/rule/templates/sourceOptions', data);
};
// 获取全部平台选项
export const osOptions = data => {
  return getRequest('/resources-center/api/v1/resources/rule/templates/osOptions', data);
};


// 规则模板列表
export const templatesList = data => {
  return getRequest('/mfp-service/api/v1/tasks/getTemplatesList', data);
};

// 规则模板添加
export const templatesAdd = data => {
  return postRequest('/resources-center/api/v1/resources/rule/templates', data);
};

// 规则模板复制
export const templatesCopy = id => {
  return postRequest('/resources-center/api/v1/resources/rule/templates/' + id + '/copy');
};

// 规则模板删除
export const templatesDel = id => {
  return deleteRequest('/mfp-service/api/v1/tasks/deleteTemplateById/' + id);
};

// 规则模板内置详情
export const builtInTemplate = id => {
  return getRequest('/mfp-service/api/v1/tasks/getTemplateListById/' + id);
};
// 规则模板编辑
export const templateEdit = (id, data) => {
  return putRequest('/resources-center/api/v1/resources/rule/templates/' + id, data);
};

// 规则模板详情-级别选项
export const isBuiltInLevelOptions = id => {
  return getRequest('/resources-center/api/v1/resources/rule/templates/' + id + '/bindingLevelOptions');
};

// 规则模板详情-类型选项
export const bindingSourceOptions = id => {
  return getRequest('/resources-center/api/v1/resources/rule/templates/' + id + '/bindingSourceOptions');
};

// 规则模板详情-规则列表
export const yaraRulesList = (id, data) => {
  return getRequest('/resources-center/api/v1/resources/rule/templates/' + id + '/yaraRulesList', data);
};

// 规则模板详情-未绑定类型选项
export const getUnBindingSourceOptions = id => {
  return getRequest('/resources-center/api/v1/resources/rule/templates/' + id + '/getUnBindingSourceOptions');
};

// 规则模板详情-未绑定平台选项
export const getUnBindingOsOptions = id => {
  return getRequest('/resources-center/api/v1/resources/rule/templates/' + id + '/getUnBindingOsOptions');
};

// 规则模板详情-未绑定级别选项
export const getUnBindingLevelOptions = id => {
  return getRequest('/resources-center/api/v1/resources/rule/templates/' + id + '/getUnBindingLevelOptions');
};

// 规则模板详情-未绑定规则列表
export const getUnBindingRules = (id, data) => {
  return getRequest('/resources-center/api/v1/resources/rule/templates/' + id + '/getUnBindingRules', data);
};

// 规则模板绑定规则
export const rulesAdd = (id, data) => {
  return postRequest('/resources-center/api/v1/resources/rule/templates/' + id + '/rules/batchPost', data);
};

// 规则模板删除绑定规则
export const rulesDel = (id, data) => {
  return postRequest('/resources-center/api/v1/resources/rule/templates/' + id + '/rules/batchDelete', data);
};
