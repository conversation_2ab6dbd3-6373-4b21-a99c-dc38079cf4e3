import { deleteRequest, postRequest, getRequest, putRequest } from '../../request';

// 自定义规则接口
// 获取全部级别选项
export const levelOptions = data => {
  return getRequest('/resources-center/api/v1/resources/rule/yara/levelOptions', data);
};

// 获取全部平台选项
export const osOptions = data => {
  return getRequest('/resources-center/api/v1/resources/rule/yara/osOptions', data);
};
  

// 获取列表
export const yaraList = data => {
  return getRequest('/resources-center/api/v1/resources/rule/yara', data);
};
  
// 获取详情
export const yaraDeatil = (id, data) => {
  return getRequest('/resources-center/api/v1/resources/rule/yara/' + id, data);
};

// 判断yara规则合法性
export const isYara = data => {
  return postRequest('/resources-center/api/v1/resources/rule/yara/isYara', data);
};

// 添加规则
export const addYara = data => {
  return postRequest('/resources-center/api/v1/resources/rule/yara', data);
};
// 编辑规则
export const editYara = (id, data) => {
  return putRequest('/resources-center/api/v1/resources/rule/yara/' + id, data);
};

// 批量删除
export const batchDelete = data => {
  return postRequest('/resources-center/api/v1/resources/rule/yara/batchDelete', data);
};

// 导入规则
export const importRule = data => {
  return postRequest('/resources-center/api/v1/resources/rule/yara/import', data);
};
