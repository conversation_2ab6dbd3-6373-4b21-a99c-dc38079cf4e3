import { deleteRequest, postRequest, getRequest, putRequest } from '../request';

// 获取系统分类下拉选择内容
export const getOsOptions = data => {
  return getRequest('/mfp-service/api/v1/symbols/getOsOptions', data);
};

// 获取文件类型下拉选择内容
export const getTypeOptions = data => {
  return getRequest('/mfp-service/api/v1/symbols/getTypeOptions', data);
};


// 获取symbol文件列表
export const symbolsList = data => {
  return getRequest('/mfp-service/api/v1/symbols', data);
};

// 新增symbol文件
export const addSymbols = data => {
  return postRequest('/mfp-service/api/v1/symbols', data);
};

// 获取symbol文件详情
export const symbolsDetail = (id) => {
  return getRequest('/mfp-service/api/v1/symbols/' + id);
};

// 修改symbol文件
export const editsymbols = (id, data) => {
  return putRequest('/mfp-service/api/v1/symbols/' + id, data);
};

// 删除symbol文件
export const deletesymbols = (id) => {
  return deleteRequest('/mfp-service/api/v1/symbols/' + id);
};

// 下载
export const downloadsymbol = (id, data) => {
  return getRequest('/mfp-service/api/v1/symbols/' + id + '/download', data);
};
