import { postRequest, getRequest } from '../request';

// 登录接口
export const loginApi = data => {
  return postRequest('/eos-service/api/v1/user/login', data);
};

// 登出接口
export const logoutApi = data => {
  return getRequest('/eos-service/api/v1/user/logout', data);
};

// 菜单
export const menusList = data => {
  return getRequest('eos-service/api/v6/sys_menus/mfp', data);
};

// 修改密码
export const resetSelfPassword = data => {
  return postRequest('/eos-service/api/v6/users/resetSelfPassword', data);
};

// 授权信息
export const getAuthMsg = data => {
  return getRequest('/resources-center/api/v1/resources/authorization/getVersion', data);
};
// 授权
export const authorizationApi = data => {
  return postRequest('/resources-center/api/v1/resources/authorization', data);
};

// 菜单收藏
export const collectApi = data => {
  return postRequest('eos-service/api/v6/user_menus/mfp', data);
};

// 菜单取消收藏
export const unCollectApi = data => {
  return postRequest('eos-service/api/v6/user_menus/batch_delete/mfp', data);
};

// 收藏列表
export const collectList = () => {
  return getRequest('eos-service/api/v6/user_menus/mfp');
};
