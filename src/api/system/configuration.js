import { getRequest, putRequest } from '../request';

// 保存基本信息

export const saveConfigs = (data) => {
  return request({
    url: 'eos-service/api/v6/system/sys_info',
    data: data,
    method: 'PUT',
    headers: { 'Content-Type': 'multipart/form-data' }
  });
};

// 获取基本信息
export const acquireConfigs = query => {
  return getRequest('eos-service/api/v6/system/sys_info/basic/mfp', query);
};

// 获取安全信息
export const securityConfigs = query => {
  return getRequest('eos-service/api/v6/system/security/mfp');
};

// 保存安全信息
export const securitySubmit = data => {
  return putRequest('eos-service/api/v6/system/security', data);
};


// 服务监控列表
export const getContainersList = query => {
  return getRequest('/ops-center/api/v1/containers', query);
};

// 重启服务
export const containersRestart = query => {
  return postRequest('/ops-center/api/v1/containers/' + query + '/restart');
};

