import { getRequest, postRequest, deleteRequest, putRequest } from "../request";

// 获取列表
export const systemApiList = query => {
  return getRequest('/resources-center/api/v1/resources/system_api', query);
};

// 编辑
export const editSystemApi = (id, query) => {
  return putRequest('/resources-center/api/v1/resources/system_api/' + id, query);
};

// 添加
export const addSystemApi = (query) => {
  return postRequest('/resources-center/api/v1/resources/system_api', query);
};

// 单条删除
export const deleteSystemApi = (id, query) => {
  return deleteRequest('/resources-center/api/v1/resources/system_api/' + id, query);
};

// 详情
export const detailSystemApi = (id, query) => {
  return getRequest('/resources-center/api/v1/resources/system_api/' + id, query);
};

// 开启关闭搜索引擎
export const switchSystemApi = (id, query) => {
  return putRequest('/resources-center/api/v1/resources/system_api/switch/' + id, query);
};
