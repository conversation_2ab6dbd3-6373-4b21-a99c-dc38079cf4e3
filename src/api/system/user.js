import { getRequest, postRequest, putRequest } from '../request';

// 用户列表
export const userList = query => {
  return getRequest('/eos-service/api/v6/users', query);
};

// 添加用户
export const addUser = query => {
  return postRequest('/eos-service/api/v6/users', query);
};

// 修改用户
export const editUser = (id, query) => {
  return putRequest('/eos-service/api/v6/users/' + id, query);
};

// 修改密码
export const editresetPassword = query => {
  return postRequest('/eos-service/api/v6/users/resetPassword', query);
};

// 删除/批量删除用户
export const batchDelete = query => {
  return postRequest('/eos-service/api/v6/users/batchDelete', query);
};

// 角色筛选条件
export const getValidRole = query => {
  return getRequest('/eos-service/api/v6/roles/getValidRole', query);
};

// 解锁用户
export const unlockUser = id => {
  return putRequest('eos-service/api/v6/users/unlock/' + id);
};
