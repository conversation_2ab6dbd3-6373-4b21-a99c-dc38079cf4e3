import { getRequest, postRequest } from "../request";

// 查询日志列表
export const logsList = query => {
  return getRequest('/eos-service/api/v6/logs', query);
};

// 批量删除日志
export const batchDelete = query => {
  return postRequest('/eos-service/api/v6/logs/batchDelete', query);
};

// 清空日志
export const clearAll = query => {
  return postRequest('/eos-service/api/v6/logs/clearAll', query);
};

// 获取查询筛选项
export const options = query => {
  return getRequest('/eos-service/api/v6/logs/options', query);
};
