import { deleteRequest, postRequest, getRequest, putRequest } from '../request';
// 内存检测接口

// 获取搜索项下拉内容
export const getOptions = data => {
  return postRequest('/mfp-service/api/v1/detection_items/get_options', data);
};

// 获取列表
export const getDetectionList = data => {
  return getRequest('/mfp-service/api/v1/detection_items', data);
};

// 单条删除
export const deleteDetection = id => {
  return deleteRequest('/mfp-service/api/v1/detection_items/' + id);
};

// 批量删除
export const batchDeleteDetection = (data) => {
  return postRequest('/mfp-service/api/v1/detection_items/batch_delete', data);
};

// 查看日志
export const getLog = (id, data) => {
  return postRequest('/mfp-service/api/v1/detection_items/' + id + '/get_log', data);
};

// 批量关联项目
export const assocTask = data => {
  return postRequest('/mfp-service/api/v1/detection_items/assoc_task', data);
};

// 关联主机
export const assocHost = (id, data) => {
  return postRequest('/mfp-service/api/v1/packages/' + id + '/connect', data);
};

// 检测项分析
export const analysis = (id, data) => {
  return postRequest('/mfp-service/api/v1/detection_items/' + id + '/analyse', data);
};

// 获取 检测项配置信息
export const getDetectionItems = (id, data) => {
  return getRequest('/mfp-service/api/v1/detection_items/' + id, data);
};

// 检测项配置 修改
export const editDetectionItems = data => {
  return putRequest('/mfp-service/api/v1/detection_items/' + data.id, data.editForm);
};


// 获取关联主机信息
export const getAssocHost = (data) => {
  return getRequest('/resources-center/api/v1/resources/property/hosts/nameView', data);
};

// 获取批量关联任务下拉内容信息
export const getTaskOptions = (data) => {
  return getRequest('/mfp-service/api/v1/tasks/options', data);
};


// 仅检测
export const detect = (id, data) => {
  return postRequest('/mfp-service/api/v1/detection_items/'+ id +'/detect', data);
};