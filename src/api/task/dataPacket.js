import { postRequest, getRequest } from '../request';

// 获取任务选项
export const tasksOptionApi = data => {
  return getRequest('/mfp-service/api/v1/tasks', data);
};

// 上传
export const uploadApi = data => {
  return postRequest('/mfp-service/api/v1/packages', data);
};

// 状态修改
export const uploadExistApi = (id, data) => {
  return postRequest('/mfp-service/api/v1/packages/' + id + '/exist', data);
};

// 获取分析插件选项
export const pluginsOptionApi = data => {
  return getRequest('/mfp-service/api/v1/plugins/options', data);
};


// ---------文件分块上传到uploadApiNew接口，然后调用packagesApiNew接口保存数据，最后调用uploadExistApiNew接口启动检测任务
// 文件上传接口
export const uploadApiNew = (data, signal) => {
  return request({
    url: '/eos-fm/api/v0/file/upload',
    data: data,
    method: 'POST',
    signal: signal.signal
  });
  // return postRequest('/eos-fm/api/v0/file/upload', data);
};

// 数据包添加（获取上传链接）
export const packagesApiNew = data => {
  return postRequest('/resources-center/api/v1/resources/file/packages', data);
};

// 数据包状态修改
export const uploadExistApiNew = (id, data) => {
  return postRequest('/mfp-service/api/v1/packages/' + id + '/exist', data);
};
