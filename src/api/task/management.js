import { deleteRequest, postRequest, getRequest, putRequest } from '../request';

// 获取任务列表
export const tasksList = data => {
  return getRequest('/mfp-service/api/v1/tasks', data);
};

// 添加任务
export const addtasks = data => {
  return postRequest('/mfp-service/api/v1/tasks', data);
};

// 编辑任务
export const edittasks = (id, data) => {
  return putRequest('/mfp-service/api/v1/tasks/' + id, data);
};

// 单条数据删除
export const deletetasks = id => {
  return deleteRequest('/mfp-service/api/v1/tasks/' + id);
};

// 获取详情
export const tasksDetail = (id, data) => {
  return getRequest('/mfp-service/api/v1/tasks/' + id, data);
};

// 获取yara规则模板选项
export const templateOption = data => {
  return getRequest('/resources-center/api/v1/resources/rule/templates/getIdAndName', data);
};
