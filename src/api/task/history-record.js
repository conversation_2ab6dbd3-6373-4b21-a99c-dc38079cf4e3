import { deleteRequest, postRequest, getRequest, putRequest } from '../request';
// 检测项记录接口

// 检测记录列表
export const detectionRecordsList = data => {
  return getRequest('/mfp-service/api/v1/detection_records', data);
};

// 批量删除
export const batchDelete = data => {
  return postRequest('/mfp-service/api/v1/detection_records/batch_delete', data);
};

// 删除所有记录
export const deleteAllRecords = data => {
  return postRequest('/mfp-service/api/v1/detection_records/delete_all', data);
};

// 检测记录时间线（展开列表）
export const timelineList = data => {
  return getRequest('/mfp-service/api/v1/detection_records/timeline', data);
};

// 单个删除
export const deleteDetectionRecords = (id, data) => {
  return deleteRequest('/mfp-service/api/v1/detection_records/' + id, data);
};

// 查看日志
export const getLog = (id, data) => {
  return getRequest('/mfp-service/api/v1/detection_records/' + id + '/get_log', data);
};
