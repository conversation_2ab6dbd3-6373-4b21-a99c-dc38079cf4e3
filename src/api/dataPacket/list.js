import { postRequest, getRequest, deleteRequest } from '../request';

// 列表
export const listApi = data => {
  return getRequest('/mfp-service/api/v1/packages', data);
};
// 详情
export const listDetailApi = data => {
  return getRequest('/mfp-service/api/v1/packages/' + data.id);
};

// 删除
export const listDelApi = data => {
  return deleteRequest('/mfp-service/api/v1/packages/' + data.id);
};

// 批量删除
export const listBatchDelApi = data => {
  return postRequest('/mfp-service/api/v1/packages/batchDelete', data);
};

// 下载
export const listDownloadApi = data => {
  return getRequest('/mfp-service/api/v1/packages/' + data + '/download');
};

// 检测配置信息
export const getPkgConfig = data => {
  return postRequest('/mfp-service/api/v1/detection_items/get_pkg_config', data);
};

// 获取任务选项
export const tasksOptionApi = data => {
  return getRequest('/mfp-service/api/v1/tasks', data);
};

// 获取symbol选项
export const symbolsOptionApi = data => {
  return getRequest('/mfp-service/api/v1/symbols', data);
};

// 再次检测
export const analysePkg = data => {
  return postRequest('/mfp-service/api/v1/detection_items/analyse_pkg', data);
};

// 处置备注
export const editDescription = (data, id) => {
  return postRequest('/mfp-service/api/v1/packages/' + id + '/edit_description', data);
};
