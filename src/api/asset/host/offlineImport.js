import { postRequest, getRequest, putRequest } from '../../request';

// 主机资产接口

// 获取资产列表
export const getHostsList = data => {
  return getRequest('/resources-center/api/v1/resources/property/hosts', data);
};

// 添加资产信息
export const addHost = data => {
  return postRequest('/resources-center/api/v1/resources/property/hosts', data);
};

// 修改资产信息
export const editHost = (id, data) => {
  return putRequest('/resources-center/api/v1/resources/property/hosts/' + id, data);
};

// 批量删除
export const batchDelete = data => {
  return postRequest('/resources-center/api/v1/resources/property/hosts/batchDelete', data);
};

// 导入资产
export const importHost = data => {
  return postRequest('/resources-center/api/v1/resources/property/hosts/import', data);
};

// 解除关联主机接口
export const disassociateHost = (id, data) => {
  return postRequest('/mfp-service/api/v1/packages/' + id + '/connect', data);
};

// 获取编辑内容详情
export const getDetail = (id, data) => {
  return getRequest('/resources-center/api/v1/resources/property/hosts/' + id, data);
};

// 获取主机关联镜像信息
export const getHostAssociatedPkg = data => {
  return getRequest('/mfp-service/api/v1/tasks/getHostAssociatedPkg', data);
};
