import axios from 'axios';
import { ZrMessage, ZrMessageBox } from 'qianji-ui';
import { useCookies } from 'vue3-cookies';
import { router } from '@/main';
const { cookies } = useCookies();

// 发起请求
let baseURL;
var url = window.location.href.split('/mfp');
// if (window.__POWERED_BY_QIANKUN__) {
console.log(process.env.NODE_ENV);
if (window.__POWERED_BY_QIANKUN__ && (process.env.NODE_ENV === 'production' || process.env.NODE_ENV === 'development')) {
  // baseURL = window.$parentStore.state.baseURL;
  baseURL = window.BASE_URL;
} else {
  baseURL = url[0].indexOf('localhost') === -1 ? url[0] : window.BASE_URL;
}
// window.$baseURL = baseURL;
export const request = axios.create({
  baseURL: baseURL, // url = base url + request url
  // baseURL: 'http://**************:3020', // url = base url + request url
  timeout: 30000 // request timeout
});
window.request = request;
// 前置拦截器
request.interceptors.request.use(
  config => {
    if (window.__POWERED_BY_QIANKUN__) {
      config.headers.Authorization = window.$parentStore.state.getToken();
      config.headers.userId = window.$parentStore.state.userId;
    } else {
      const token = cookies.get('dsp_token');
      config.headers.Token = token;
    }
    return config;
  },
  error => {
    // do something with request error
    ZrMessage.error('请求超时');
    console.log(error); // for debug
    return Promise.reject(error);
  }
);
let messageNum = 0;
request.interceptors.response.use(response => { // 拦截-接口返回状态统一处理
  // console.log(response, 'response');
  if (response.data.code == "0") {
    if (response.headers.token && response.headers.token !== '') {
      cookies.set('dsp_token', response.headers.token);
    }
    return response;
  } else if (response.data.code == 401) {
    cookies.remove('dsp_token');
    router.push({ name: 'Login' });
  } else {
    ZrMessage.error(response.data.msg);
    return Promise.reject(response);
  }
}, err => {
  if (err.response.status == 401) {
    cookies.remove('dsp_token');
    localStorage.removeItem('menuList');
    if (messageNum === 0) {
      messageNum = 1;
      ZrMessageBox.confirm('登录已超时，请重新登录！', '登录超时', {
        confirmButtonText: '确定',
        showClose: false,
        showCancelButton: false,
        closeOnClickModal: false,
        closeOnPressEscape: false,
        type: 'warning'
      })
        .then(() => {
          messageNum = 0;
          router.push({ name: 'Login' });
        });
    }
  } else if (err.response.status == 400 || err.response.status == 404 || err.response.status == 500) {
    ZrMessage.error(err.response.data.msg);
  } else {
    ZrMessage.error('响应超时或连接错误，请重试');
  }
  return Promise.reject(err);
});

// 后置拦截器
// request.interceptors.response.use(
//   res => {
//     // 10200 千机平台接口正常返回的状态码
//     if (res?.data?.status || res?.data?.code === 10200 || res?.data?.code === 200) {
//       return Promise.resolve(res.data);
//     } else {
//       Message({ type: 'error', message: res.data.message });
//       return Promise.reject(res);
//     }
//   },
//   error => {
//     if (error.message.indexOf('timeout') !== -1) {
//       Message({
//         message: '网络请求超时，请稍后再试！',
//         type: 'error',
//         duration: 5 * 1000
//       });
//       return Promise.reject(error);
//     }
//     // 以下几种错误码时，不弹顶部弹窗，通知主应用跳页面
//     if (![401, 403, 404].includes(error.response.status)) {
//       Message({ type: 'error', message: error.message });
//     }
//     if (window.__POWERED_BY_QIANKUN__) {
//       ce.emit('networkError', error);
//     }
//   }
// );
// 快速发起get请求
export const getRequest = (url, query) => {
  return request({
    url: url,
    params: query,
    method: 'GET'
  });
};

// 快速发起post请求
export const postRequest = (url, data) => {
  return request({
    url: url,
    data: data,
    method: 'POST'
  });
};

// 快速发起delete请求
export const deleteRequest = (url, data) => {
  return request({
    url: url,
    params: data,
    method: 'DELETE'
  });
};
// 快速发起put请求
export const putRequest = (url, data) => {
  return request({
    url: url,
    data: data,
    method: 'PUT'
  });
};

// 可配置参数的post
export const post = (url, data, config) => {
  return request.post(url, data, config);
};
