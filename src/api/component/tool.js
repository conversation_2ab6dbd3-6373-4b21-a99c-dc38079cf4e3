import { postRequest, getRequest, deleteRequest, putRequest } from '../request';

// 列表
export const listApi = data => {
  return getRequest('/eos-service/api/v1/tools', data);
};

// 新增
export const addApi = data => {
  return postRequest('/eos-service/api/v1/tools/upload', data);
};

// 删除
export const delApi = (id, data) => {
  return deleteRequest('/eos-service/api/v1/tools/' + id, data);
};

// 编辑
export const editApi = (id, data) => {
  return putRequest('/eos-service/api/v1/tools/' + id, data);
};
