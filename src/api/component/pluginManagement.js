import { postRequest, getRequest, deleteRequest, putRequest } from '../request';

// 新增插件
export const addPlugins = data => {
  return postRequest('/mfp-service/api/v1/plugins', data);
};

// 获取插件列表
export const pluginsList = data => {
  return getRequest('/mfp-service/api/v1/plugins', data);
};

// 获取插件详情
export const pluginsDetail = (id, data) => {
  return getRequest('/mfp-service/api/v1/plugins/' + id, data);
};

// 修改插件
export const editPlugins = (id, data) => {
  return putRequest('/mfp-service/api/v1/plugins/' + id, data);
};

// 删除插件
export const deletePlugins = (id, data) => {
  return deleteRequest('/mfp-service/api/v1/plugins/' + id, data);
};

// 下载插件文件
export const downloadPlugins = (id, data) => {
  return putRequest('/mfp-service/api/v1/plugins/' + id + '/download', data);
};
