// 独立启动时 读取本list作为路由表
export const routes = [
  {
    'path': '/',
    'component': '/Login/index',
    'name': 'Login',
    'title': '登录',
    'full_screen': false,
    'hidden': false,
    "meta": {
      "isNotLogin": true
    }
  }
  // {
  //   'path': '/index',
  //   'component': 'Layout',
  //   'name': 'Index',
  //   'title': '首页',
  //   'icon': 'iconfont zr-catalogue',
  //   'hidden': true,
  //   'redirect': '/home',
  //   children: [
  //     {
  //       'path': '/home',
  //       'component': '/Home/index',
  //       'name': 'Home',
  //       'title': '数据检测任务',
  //       'hidden': true
  //     }
  //   ]
  // },
  // {
  //   'path': '/threat',
  //   'component': 'Layout',
  //   'name': 'Threat',
  //   'icon': 'iconfont zr-monitor',
  //   'hidden': true,
  //   "meta": {
  //     'title': '威胁检测'
  //   },
  //   children: [
  //     {
  //       'path': '/threat/mirror-angle',
  //       'component': '/Threat/MirrorAngle/index',
  //       'name': 'ThreatMirrorAngle',
  //       "meta": {
  //         'title': '内存镜像视角'
  //       },
  //       'hidden': true
  //     }
  //   ]
  // },
  // {
  //   'path': '/mirror-angle/:id',
  //   'name': 'MirrorAngle',
  //   'title': '内存镜像视角-详情',
  //   'hidden': false,
  //   'component': '/Threat/MirrorAngle/Detail'

  // },
  // {
  //   'path': '/asset',
  //   'component': 'Layout',
  //   'name': 'Asset',
  //   'icon': 'iconfont zr-application',
  //   "meta": {
  //     'title': '资产管理'
  //   },
  //   'hidden': true,
  //   children: [
  //     {
  //       'path': '/asset/host',
  //       'component': '/Asset/Host/index',
  //       'name': 'AssetHost',
  //       "meta": {
  //         'title': '主机资产'
  //       },
  //       'hidden': true
  //     }
  //   ]
  // },
  // {
  //   'path': '/task',
  //   'component': 'Layout',
  //   'name': 'Task',
  //   "meta": {
  //     'title': '检测任务'
  //   },
  //   'icon': 'iconfont zr-comprehensive-analysis-b',
  //   'hidden': true,
  //   children: [
  //     {
  //       'path': '/task/data-packet',
  //       'component': '/Task/DataPacket/index',
  //       'name': 'TaskDataPacket',
  //       "meta": {
  //         'title': '数据包上传'
  //       },
  //       'hidden': true
  //     },
  //     {
  //       'path': '/task/detection',
  //       'component': '/Task/Detection/index',
  //       'name': 'TaskDetection',
  //       "meta": {
  //         'title': '内存检测'
  //       },
  //       'hidden': true
  //     },
  //     {
  //       'path': '/task/management',
  //       'component': '/Task/Management/index',
  //       'name': 'TaskManagement',
  //       "meta": {
  //         'title': '任务管理'
  //       },
  //       'hidden': true
  //     }
  //   ]
  // },
  // {
  //   'path': '/rule',
  //   'component': 'Layout',
  //   'name': 'Rule',
  //   "meta": {
  //     'title': '规则管理'
  //   },
  //   'icon': 'iconfont zr-trusted-configuration-b',
  //   'hidden': true,
  //   children: [
  //     {
  //       'path': '/rule/yara',
  //       'component': '/Rule/Yara/index',
  //       'name': 'RuleYara',
  //       "meta": {
  //         'title': 'Yara规则管理'
  //       },
  //       'hidden': true
  //     },
  //     {
  //       'path': '/rule/symbol',
  //       'component': '/Rule/Symbol/index',
  //       'name': 'RuleSymbol',
  //       "meta": {
  //         'title': 'Symbol文件管理'
  //       },
  //       'hidden': true
  //     }
  //   ]
  // },
  // {
  //   'path': '/threat-intelligence',
  //   'component': 'Layout',
  //   'name': 'ThreatIntelligence',
  //   "meta": {
  //     'title': '威胁情报'
  //   },
  //   'icon': 'iconfont zr-fingerprint-library',
  //   'hidden': true,
  //   children: [
  //     {
  //       'path': '/threat-intelligence/manage',
  //       'component': '/ThreatIntelligence/Manage/index',
  //       'name': 'ThreatIntelligenceManage',
  //       "meta": {
  //         'title': '情报管理'
  //       },
  //       'hidden': true
  //     }
  //   ]
  // },
  // {
  //   'path': '/data-packet',
  //   'component': 'Layout',
  //   'name': 'DataPacket',
  //   "meta": {
  //     'title': '数据包管理'
  //   },
  //   'icon': 'iconfont zr-scene-b',
  //   'hidden': true,
  //   children: [
  //     {
  //       'path': '/data-packet/list',
  //       'component': '/DataPacket/List/index',
  //       'name': 'DataPacketList',
  //       "meta": {
  //         'title': '数据包列表'
  //       },
  //       'hidden': true
  //     }
  //   ]
  // },
  
//  {
//   'path': '/component',
//   'component': 'Layout',
//   'name': 'Component',
//   "meta": {
//     'title': '组件管理'
//   },
//   'icon': 'iconfont zr-project-b',
//   'hidden': true,
//   children: [
//     {
//       'path': '/component/tool',
//       'component': '/Component/Tool/index',
//       'name': 'ComponentTool',
//       "meta": {
//         'title': '工具管理'
//       },
//       'hidden': true
//     }
//   ]
// },
  // {
  //   'path': '/system',
  //   'component': 'Layout',
  //   'name': 'System',
  //   "meta": {
  //     'title': '系统管理'
  //   },
  //   'icon': 'iconfont zr-set-b',
  //   'hidden': true,
  //   children: [
  //     {
  //       'path': '/system/user',
  //       'component': '/System/User/index',
  //       'name': 'SystemUser',
  //       "meta": {
  //         'title': '用户管理'
  //       },
  //       'hidden': true
  //     },
  //     {
  //       'path': '/system/log',
  //       'component': '/System/Log/index',
  //       'name': 'SystemLog',
  //       "meta": {
  //         'title': '日志管理'
  //       },
  //       'hidden': true
  //     },
  //     {
  //       'path': '/system/configuration',
  //       'component': '/System/Configuration/index',
  //       'name': 'SystemConfiguration',
  //       "meta": {
  //         'title': '系统管理'
  //       },
  //       'hidden': true
  //     }
  //   ]
  // },
  // {
  //   path: '/:pathMatch(.*)*',
  //   component: () => import('@/views/ErrorPage/404'),
  //   "meta": {
  //     'title': ':pathMatch(.*)*'
  //   },
  //   name: ':pathMatch(.*)*'
  // }
];
