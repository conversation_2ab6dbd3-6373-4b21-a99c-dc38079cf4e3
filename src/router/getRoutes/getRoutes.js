// import { router } from '@/main'

let router = null;
// let { router } = require('@/main');
// 获取当前用户的路由表
// 此为临时代码 后期将接入真正的接口
import Layout from '@/layout/index.vue';
import { menusList, collectList } from '@/api/login/index';
import { useStore } from '@/store/store';
import { storeToRefs } from 'pinia';
var routeList = [];
export const getRoutes = async() => {
  router = await import('@/main');
  router = router.router;
  window.Router = router;

  return new Promise((resolve, reject) => {
    // let routeList;
    // if (window.__POWERED_BY_QIANKUN__) {
    //   // 从qiankun启动时
    //   routeList = window.$parentStore.state.activeApp.menus;
    // } else {
    //   routeList = routes;
    // }
    // if (router.getRoutes().length > 0) {
    //   return resolve();
    // }
    var list = [];
    const stores = useStore();
    const { routeBool, routerMenuList, collectIdList } = storeToRefs(stores);
    menusList().then(res => {
      routeList = res.data.data || [];
    }).finally(() => {
      // collectList().then(res => {
      //   collectIdList.value = res.data.data.map(item => item.menu_id);
      //   res.data.data.forEach(item => {
      //     item.path = '/collect' + item.path;
      //     item.name = 'Collect' + item.name;
      //     list.push(item);
      //   });
      //   routeList.unshift({
      //     'path': '/collect',
      //     'component': 'Layout',
      //     'name': 'Collect',
      //     'icon': 'iconfont zr-collection',
      //     'hidden': true,
      //     'meta': {
      //       'title': '我的收藏'
      //     },
      //     'sort': 999,
      //     children: list
      //   });
      routerMenuList.value = routeList;
      routeList.forEach(route => {
        transformComponent(route);
        if (route.children && route.children.length > 0) {
          route.children.forEach(child => {
            transformComponent(child);
            if (child.children && child.children.length > 0) {
              child.children.forEach(sun => {
                transformComponent(sun);
              });
            }
          });
        }
        router.addRoute(route);
        routeBool.value = true;
        resolve();
      });
      // });
    });
    // 获取到路由表，开始加载动态路由
    // 三级路由 如果无限层级，则将此处改成递归 因为循环比递归效率更高
    // routeList.forEach(route => {
    //   transformComponent(route);
    //   if (route.children && route.children.length > 0) {
    //     route.children.forEach(child => {
    //       transformComponent(child);
    //       if (child.children && child.children.length > 0) {
    //         child.children.forEach(sun => {
    //           transformComponent(sun);
    //         });
    //       }
    //     });
    //   }
    //   router.addRoute(route);
    // });
    // 从独立启动时
    // if (!window.__POWERED_BY_QIANKUN__) {
    //   console.log(window);
    //   window.$parentStore.state.routes = routeList;
    // }
  });
};

export function transformComponent(route) {
  if (typeof route.component === 'object') {
    return;
  }
  if (route.component == 'Layout') {
    route.component = Layout;
  } else {
    const path = route.component;
    try {
      route.component = require(`@/views${path}`).default;
      // console.log(route.component);
    } catch (error) {
      route.component = Layout;
      console.error('路由组件引用错误', error);
    }
  }
}

