@font-face {
  font-family: "iconfont"; /* Project id 3566823 */
  src: url('iconfont.woff2?t=1678756548949') format('woff2'),
       url('iconfont.woff?t=1678756548949') format('woff'),
       url('iconfont.ttf?t=1678756548949') format('truetype'),
       url('iconfont.svg?t=1678756548949#iconfont') format('svg');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.zr-quick-fill:before {
  content: "\e668";
}

.zr-quick:before {
  content: "\e854";
}

.zr-network-fill:before {
  content: "\e824";
}

.zr-user:before {
  content: "\e791";
}

.zr-cloud-upload-fill-b:before {
  content: "\e853";
}

.zr-vector-b:before {
  content: "\e7ee";
}

.zr-vector-a:before {
  content: "\e852";
}

.zr-earphone-fill:before {
  content: "\e7b5";
}

.zr-movie-fill:before {
  content: "\e7b6";
}

.zr-internet-fill:before {
  content: "\e7b7";
}

.zr-music-fill:before {
  content: "\e7b8";
}

.zr-notebook-fill:before {
  content: "\e7b9";
}

.zr-umbrella-fill:before {
  content: "\e7ba";
}

.zr-home-a-fill:before {
  content: "\e7bb";
}

.zr-set-a-fill:before {
  content: "\e7bc";
}

.zr-set-c-fill:before {
  content: "\e7bd";
}

.zr-project-a-fill:before {
  content: "\e7be";
}

.zr-video-fill:before {
  content: "\e7bf";
}

.zr-square-edit-fill:before {
  content: "\e7c0";
}

.zr-android-fill:before {
  content: "\e7c1";
}

.zr-voice-on-fill:before {
  content: "\e7c2";
}

.zr-square-play-fill:before {
  content: "\e7c3";
}

.zr-project-b-fill:before {
  content: "\e7c4";
}

.zr-sound-recording-fill:before {
  content: "\e7c5";
}

.zr-agreement-sign-fill:before {
  content: "\e7c6";
}

.zr-comprehensive-analysis-a-fill:before {
  content: "\e7c7";
}

.zr-like-fill:before {
  content: "\e7c8";
}

.zr-angry-fill:before {
  content: "\e7c9";
}

.zr-chart-border-fill:before {
  content: "\e7ca";
}

.zr-voice-off-fill:before {
  content: "\e7cb";
}

.zr-configuration-a-fill:before {
  content: "\e7cc";
}

.zr-telephone-fill:before {
  content: "\e7cd";
}

.zr-ie-fill:before {
  content: "\e7ce";
}

.zr-calm-fill:before {
  content: "\e7cf";
}

.zr-configuration-b-fill:before {
  content: "\e7d0";
}

.zr-customer-service-fill:before {
  content: "\e7d1";
}

.zr-configuration-c-fill:before {
  content: "\e7d2";
}

.zr-data-packet-fill:before {
  content: "\e7d3";
}

.zr-dashboard-fill:before {
  content: "\e7d4";
}

.zr-configuration-d-fill:before {
  content: "\e7d5";
}

.zr-circle-reduce-fill:before {
  content: "\e7d6";
}

.zr-gift-fill:before {
  content: "\e7d7";
}

.zr-email-fill:before {
  content: "\e7d8";
}

.zr-excel-file-fill:before {
  content: "\e7d9";
}

.zr-check-fill:before {
  content: "\e7da";
}

.zr-copy-file-fill:before {
  content: "\e7db";
}

.zr-form-fill:before {
  content: "\e7dc";
}

.zr-find-fill:before {
  content: "\e7dd";
}

.zr-copy-fill:before {
  content: "\e7de";
}

.zr-set-b-fill:before {
  content: "\e7df";
}

.zr-database-fill:before {
  content: "\e7e0";
}

.zr-flow-chart-vertical-fill:before {
  content: "\e7e1";
}

.zr-none-file-fill:before {
  content: "\e7e2";
}

.zr-goal-a-fill:before {
  content: "\e7e3";
}

.zr-infiltration-b-fill:before {
  content: "\e7e4";
}

.zr-fun-fill:before {
  content: "\e7e5";
}

.zr-label-fill:before {
  content: "\e7e6";
}

.zr-famale-fill:before {
  content: "\e7e7";
}

.zr-add-file-fill:before {
  content: "\e7e8";
}

.zr-infiltration-a-fill:before {
  content: "\e7e9";
}

.zr-comprehensive-analysis-c-fill:before {
  content: "\e7ea";
}

.zr-location-fill:before {
  content: "\e7eb";
}

.zr-ios-fill:before {
  content: "\e7ec";
}

.zr-share-fill:before {
  content: "\e7ed";
}

.zr-map-fill:before {
  content: "\e7ef";
}

.zr-navigation-fill:before {
  content: "\e7f0";
}

.zr-overall-situation-fill:before {
  content: "\e7f1";
}

.zr-pie-chart-fill:before {
  content: "\e7f2";
}

.zr-male-fill:before {
  content: "\e7f3";
}

.zr-report-fill:before {
  content: "\e7f4";
}

.zr-port-fill:before {
  content: "\e7f5";
}

.zr-print-fill:before {
  content: "\e7f6";
}

.zr-module-fill:before {
  content: "\e7f7";
}

.zr-screen-fill:before {
  content: "\e7f8";
}

.zr-ppt-file-fill:before {
  content: "\e7f9";
}

.zr-reduce-file-fill:before {
  content: "\e7fa";
}

.zr-line-chart-fill:before {
  content: "\e7fb";
}

.zr-paste-fill:before {
  content: "\e7fc";
}

.zr-picture-file-fill:before {
  content: "\e7fd";
}

.zr-wait-fill:before {
  content: "\e7fe";
}

.zr-security-intelligence-fill:before {
  content: "\e7ff";
}

.zr-safe-b-fill:before {
  content: "\e800";
}

.zr-unknown-file-fill:before {
  content: "\e801";
}

.zr-search-fill:before {
  content: "\e802";
}

.zr-template-fill:before {
  content: "\e803";
}

.zr-zoom-out-fill:before {
  content: "\e804";
}

.zr-application-fill:before {
  content: "\e805";
}

.zr-sad-fill:before {
  content: "\e806";
}

.zr-zoom-in-fill:before {
  content: "\e807";
}

.zr-target-b-fill:before {
  content: "\e808";
}

.zr-target-a-fill:before {
  content: "\e809";
}

.zr-usb-fill:before {
  content: "\e80a";
}

.zr-workflow-fill:before {
  content: "\e80b";
}

.zr-agent-fill:before {
  content: "\e80c";
}

.zr-asset-scanning-fill:before {
  content: "\e80d";
}

.zr-and-fill:before {
  content: "\e80e";
}

.zr-backups-fill:before {
  content: "\e80f";
}

.zr-pdf-file-fill:before {
  content: "\e810";
}

.zr-asset-analysis-b-fill:before {
  content: "\e811";
}

.zr-box-fill:before {
  content: "\e812";
}

.zr-terrified-fill:before {
  content: "\e813";
}

.zr-business-configuration-a-fill:before {
  content: "\e814";
}

.zr-safe-a-fill:before {
  content: "\e815";
}

.zr-a-attackdistribution-fill:before {
  content: "\e816";
}

.zr-class-fill:before {
  content: "\e817";
}

.zr-asset-analysis-a-fill:before {
  content: "\e818";
}

.zr-catalogue-fill:before {
  content: "\e819";
}

.zr-server-fill:before {
  content: "\e81a";
}

.zr-exit-configuration-fill:before {
  content: "\e81b";
}

.zr-bug-library-fill:before {
  content: "\e81c";
}

.zr-deal-fill:before {
  content: "\e81d";
}

.zr-global-configuration-fill:before {
  content: "\e81e";
}

.zr-information-extraction-fill:before {
  content: "\e81f";
}

.zr-details-fill:before {
  content: "\e820";
}

.zr-intercept-fill:before {
  content: "\e821";
}

.zr-mapping-fill:before {
  content: "\e822";
}

.zr-decision-analysis-fill:before {
  content: "\e823";
}

.zr-operator-fill:before {
  content: "\e825";
}

.zr-file-update-fill:before {
  content: "\e826";
}

.zr-Paragraph-b-fill:before {
  content: "\e827";
}

.zr-goal-c-fill:before {
  content: "\e828";
}

.zr-country-fill:before {
  content: "\e829";
}

.zr-Intrusion-detection-fill:before {
  content: "\e82a";
}

.zr-flow-chart-transverse-fill:before {
  content: "\e82b";
}

.zr-plan-fill:before {
  content: "\e82c";
}

.zr-relation-fill:before {
  content: "\e82d";
}

.zr-business-configuration-b-fill:before {
  content: "\e82e";
}

.zr-Paragraph-c-fill:before {
  content: "\e82f";
}

.zr-rule-policy-fill:before {
  content: "\e830";
}

.zr-scene-a-fill:before {
  content: "\e831";
}

.zr-monitor-fill:before {
  content: "\e832";
}

.zr-scene-b-fill:before {
  content: "\e833";
}

.zr-reduction-fill:before {
  content: "\e834";
}

.zr-scene-c-fill:before {
  content: "\e835";
}

.zr-safety-equipment-fill:before {
  content: "\e836";
}

.zr-search-categories-fill:before {
  content: "\e837";
}

.zr-permission-management-fill:before {
  content: "\e838";
}

.zr-system-update-fill:before {
  content: "\e839";
}

.zr-safety-report-fill:before {
  content: "\e83a";
}

.zr-down-fill1:before {
  content: "\e83b";
}

.zr-scene-e-fill:before {
  content: "\e83c";
}

.zr-statistics-fill:before {
  content: "\e83d";
}

.zr-vulnerability-control-fill:before {
  content: "\e83e";
}

.zr-up-fill1:before {
  content: "\e83f";
}

.zr-visualization-fill:before {
  content: "\e840";
}

.zr-strategy-fill:before {
  content: "\e841";
}

.zr-threaten-fill:before {
  content: "\e842";
}

.zr-region-fill:before {
  content: "\e843";
}

.zr-cut-fill:before {
  content: "\e844";
}

.zr-realm-name-fill:before {
  content: "\e845";
}

.zr-goal-b-fill:before {
  content: "\e846";
}

.zr-agreement-user-fill:before {
  content: "\e847";
}

.zr-word-file-fill:before {
  content: "\e848";
}

.zr-clean-cache-fill:before {
  content: "\e849";
}

.zr-operation-tools-fill:before {
  content: "\e84a";
}

.zr-remarks-fill:before {
  content: "\e84b";
}

.zr-trusted-configuration-b-fill:before {
  content: "\e84c";
}

.zr-infiltration-c-fill:before {
  content: "\e84d";
}

.zr-available-bug-library-fill:before {
  content: "\e84e";
}

.zr-fingerprint-library-fill:before {
  content: "\e84f";
}

.zr-scene-d-fill:before {
  content: "\e850";
}

.zr-trusted-configuration-a-fill:before {
  content: "\e851";
}

.zr-circle-add-fill:before {
  content: "\e75f";
}

.zr-back-fill:before {
  content: "\e760";
}

.zr-square-add-fill:before {
  content: "\e761";
}

.zr-square-seleted-fill:before {
  content: "\e762";
}

.zr-circle-fill:before {
  content: "\e763";
}

.zr-circle-close-fill:before {
  content: "\e764";
}

.zr-battery-fill:before {
  content: "\e765";
}

.zr-bottom-fill:before {
  content: "\e766";
}

.zr-circle-seleted-fill:before {
  content: "\e767";
}

.zr-square-fill:before {
  content: "\e768";
}

.zr-cancel-withdrawal-fill:before {
  content: "\e769";
}

.zr-camera-fill:before {
  content: "\e76a";
}

.zr-charge-fill:before {
  content: "\e76b";
}

.zr-circle-money-fill:before {
  content: "\e76c";
}

.zr-decline-fill:before {
  content: "\e76d";
}

.zr-edit-a-fill:before {
  content: "\e76e";
}

.zr-date-fill:before {
  content: "\e76f";
}

.zr-circle-doubt-fill:before {
  content: "\e770";
}

.zr-circle-head-a-fill:before {
  content: "\e771";
}

.zr-edit-d-fill:before {
  content: "\e772";
}

.zr-change-fill:before {
  content: "\e773";
}

.zr-bluetooth-fill:before {
  content: "\e774";
}

.zr-circle-Warning-fill:before {
  content: "\e775";
}

.zr-cloud-upload-fill:before {
  content: "\e776";
}

.zr-circle-head-b-fill:before {
  content: "\e777";
}

.zr-edit-b-fill:before {
  content: "\e778";
}

.zr-circle-information-fill:before {
  content: "\e779";
}

.zr-enlarge-fill:before {
  content: "\e77a";
}

.zr-edit-v-fill:before {
  content: "\e77b";
}

.zr-cloud-download-fill:before {
  content: "\e77c";
}

.zr-moments-fill:before {
  content: "\e77d";
}

.zr-follow-fill:before {
  content: "\e77e";
}

.zr-narrow-fill:before {
  content: "\e77f";
}

.zr-circle-logout-fill:before {
  content: "\e780";
}

.zr-file-fill:before {
  content: "\e781";
}

.zr-hide-fill:before {
  content: "\e782";
}

.zr-history-fill:before {
  content: "\e783";
}

.zr-lable-fill:before {
  content: "\e784";
}

.zr-lock-fill:before {
  content: "\e785";
}

.zr-roll-fill:before {
  content: "\e786";
}

.zr-password-fill:before {
  content: "\e787";
}

.zr-rise-fill:before {
  content: "\e788";
}

.zr-download-fill:before {
  content: "\e789";
}

.zr-square-picture-fill:before {
  content: "\e78a";
}

.zr-square-bottom-fill:before {
  content: "\e78b";
}

.zr-square-head-b-fill:before {
  content: "\e78c";
}

.zr-square-logout-fill:before {
  content: "\e78d";
}

.zr-protect-fill:before {
  content: "\e78e";
}

.zr-polygon-Warning-fill:before {
  content: "\e78f";
}

.zr-top-fill:before {
  content: "\e790";
}

.zr-display-fill:before {
  content: "\e792";
}

.zr-square-reduce-fill:before {
  content: "\e793";
}

.zr-triangle-Warning-fill:before {
  content: "\e794";
}

.zr-square-login-fill:before {
  content: "\e795";
}

.zr-square-top-fill:before {
  content: "\e796";
}

.zr-withdraw-fill:before {
  content: "\e797";
}

.zr-refresh-fill:before {
  content: "\e798";
}

.zr-unlock-fill:before {
  content: "\e799";
}

.zr-Subtract-fill:before {
  content: "\e79a";
}

.zr-circle-pause-fill:before {
  content: "\e79b";
}

.zr-user-fill:before {
  content: "\e79c";
}

.zr-add-folder-fill:before {
  content: "\e79d";
}

.zr-upload-fill:before {
  content: "\e79e";
}

.zr-ban-fill:before {
  content: "\e79f";
}

.zr-circle-stop-fill:before {
  content: "\e7a0";
}

.zr-circle-play-a-fill:before {
  content: "\e7a1";
}

.zr-notice-fill:before {
  content: "\e7a2";
}

.zr-desktop-computer-fill:before {
  content: "\e7a3";
}

.zr-transform-fill:before {
  content: "\e7a4";
}

.zr-square-close-fill:before {
  content: "\e7a5";
}

.zr-collection-fill:before {
  content: "\e7a6";
}

.zr-vertical-center-fill:before {
  content: "\e7a7";
}

.zr-folder-fill:before {
  content: "\e7a8";
}

.zr-circle-play-b-fill:before {
  content: "\e7a9";
}

.zr-preserve-fill:before {
  content: "\e7aa";
}

.zr-home-b-fill:before {
  content: "\e7ab";
}

.zr-dislike-fill:before {
  content: "\e7ac";
}

.zr-delete-fill:before {
  content: "\e7ad";
}

.zr-clear-fill:before {
  content: "\e7ae";
}

.zr-home-c-fill:before {
  content: "\e7af";
}

.zr-layout-fill:before {
  content: "\e7b0";
}

.zr-time-fill:before {
  content: "\e7b1";
}

.zr-google-fill:before {
  content: "\e7b2";
}

.zr-comment-fill:before {
  content: "\e7b3";
}

.zr-phone-fill:before {
  content: "\e7b4";
}

.zr-down-fill:before {
  content: "\e75d";
}

.zr-up-fill:before {
  content: "\e75e";
}

.zr-folder:before {
  content: "\e75c";
}

.zr-reduce-file:before {
  content: "\e75b";
}

.zr-Battery1:before {
  content: "\e6ca";
}

.zr-deal:before {
  content: "\e6cb";
}

.zr-asset-scanning:before {
  content: "\e6cc";
}

.zr-file-update:before {
  content: "\e6cd";
}

.zr-backups:before {
  content: "\e6ce";
}

.zr-details:before {
  content: "\e6cf";
}

.zr-and:before {
  content: "\e6d0";
}

.zr-mapping:before {
  content: "\e6d1";
}

.zr-scene-a:before {
  content: "\e6d2";
}

.zr-clean-cache:before {
  content: "\e6d3";
}

.zr-safety-report:before {
  content: "\e6d4";
}

.zr-intercept:before {
  content: "\e6d5";
}

.zr-scene-b:before {
  content: "\e6d6";
}

.zr-monitor:before {
  content: "\e6d7";
}

.zr-threaten:before {
  content: "\e6d8";
}

.zr-Paragraph-c:before {
  content: "\e6d9";
}

.zr-asset-analysis-b:before {
  content: "\e6da";
}

.zr-statistics:before {
  content: "\e6db";
}

.zr-scene-e:before {
  content: "\e6dc";
}

.zr-asset-analysis-a:before {
  content: "\e6dd";
}

.zr-scene-c:before {
  content: "\e6de";
}

.zr-global-configuration:before {
  content: "\e6df";
}

.zr-reduction:before {
  content: "\e6e0";
}

.zr-information-extraction:before {
  content: "\e6e1";
}

.zr-network:before {
  content: "\e6e2";
}

.zr-exit-configuration:before {
  content: "\e6e3";
}

.zr-system-update:before {
  content: "\e6e4";
}

.zr-permission-management:before {
  content: "\e6e5";
}

.zr-visualization:before {
  content: "\e6e6";
}

.zr-strategy:before {
  content: "\e6e7";
}

.zr-agreement:before {
  content: "\e6e8";
}

.zr-network-attack-b:before {
  content: "\e6e9";
}

.zr-vulnerability-control:before {
  content: "\e6ea";
}

.zr-class:before {
  content: "\e6eb";
}

.zr-Intrusion-detection:before {
  content: "\e6ec";
}

.zr-scene-d:before {
  content: "\e6ed";
}

.zr-comprehensive-analysis-a:before {
  content: "\e6ee";
}

.zr-operation-tools:before {
  content: "\e6ef";
}

.zr-agreement-sign:before {
  content: "\e6f0";
}

.zr-infiltration-c:before {
  content: "\e6f1";
}

.zr-infiltration-a:before {
  content: "\e6f2";
}

.zr-box:before {
  content: "\e6f3";
}

.zr-bug-library:before {
  content: "\e6f4";
}

.zr-flow-chart-transverse:before {
  content: "\e6f5";
}

.zr-operator:before {
  content: "\e6f6";
}

.zr-trusted-configuration-a:before {
  content: "\e6f7";
}

.zr-available-bug-library:before {
  content: "\e6f8";
}

.zr-plan:before {
  content: "\e6f9";
}

.zr-decision-analysis:before {
  content: "\e6fa";
}

.zr-port:before {
  content: "\e6fb";
}

.zr-configuration-d:before {
  content: "\e6fc";
}

.zr-Paragraph-b:before {
  content: "\e6fd";
}

.zr-catalogue:before {
  content: "\e6fe";
}

.zr-attack-analysis:before {
  content: "\e6ff";
}

.zr-configuration-b:before {
  content: "\e700";
}

.zr-a-attackdistribution:before {
  content: "\e701";
}

.zr-business-configuration-b:before {
  content: "\e702";
}

.zr-label:before {
  content: "\e703";
}

.zr-business-configuration-a:before {
  content: "\e704";
}

.zr-overall-situation:before {
  content: "\e705";
}

.zr-fingerprint-library:before {
  content: "\e706";
}

.zr-goal-c:before {
  content: "\e707";
}

.zr-calm:before {
  content: "\e708";
}

.zr-angry:before {
  content: "\e709";
}

.zr-remarks:before {
  content: "\e70a";
}

.zr-data-analysis:before {
  content: "\e70b";
}

.zr-network-attack-a:before {
  content: "\e70c";
}

.zr-configuration-a:before {
  content: "\e70d";
}

.zr-search-documents:before {
  content: "\e70e";
}

.zr-customer-service:before {
  content: "\e70f";
}

.zr-application:before {
  content: "\e710";
}

.zr-database:before {
  content: "\e711";
}

.zr-data-packet:before {
  content: "\e712";
}

.zr-chart:before {
  content: "\e713";
}

.zr-comprehensive-analysis-c:before {
  content: "\e714";
}

.zr-trusted-configuration-b:before {
  content: "\e715";
}

.zr-security-intelligence:before {
  content: "\e716";
}

.zr-email:before {
  content: "\e717";
}

.zr-sad:before {
  content: "\e718";
}

.zr-flow-chart-vertical:before {
  content: "\e719";
}

.zr-comprehensive-analysis-b:before {
  content: "\e71a";
}

.zr-agent:before {
  content: "\e71b";
}

.zr-form:before {
  content: "\e71c";
}

.zr-ios:before {
  content: "\e71d";
}

.zr-configuration-c:before {
  content: "\e71e";
}

.zr-chart-border:before {
  content: "\e71f";
}

.zr-terrified:before {
  content: "\e720";
}

.zr-android:before {
  content: "\e721";
}

.zr-safe-a:before {
  content: "\e722";
}

.zr-ascending-order:before {
  content: "\e723";
}

.zr-rule-policy:before {
  content: "\e724";
}

.zr-target-a:before {
  content: "\e725";
}

.zr-male:before {
  content: "\e726";
}

.zr-scan:before {
  content: "\e727";
}

.zr-qr-code:before {
  content: "\e728";
}

.zr-pie-chart:before {
  content: "\e729";
}

.zr-find:before {
  content: "\e72a";
}

.zr-usb:before {
  content: "\e72b";
}

.zr-line-chart:before {
  content: "\e72c";
}

.zr-center-horizontally-align:before {
  content: "\e72d";
}

.zr-infiltration-b:before {
  content: "\e72e";
}

.zr-location:before {
  content: "\e72f";
}

.zr-safe-b:before {
  content: "\e730";
}

.zr-agreement-user:before {
  content: "\e731";
}

.zr-realm-name:before {
  content: "\e732";
}

.zr-famale:before {
  content: "\e733";
}

.zr-enclosure:before {
  content: "\e734";
}

.zr-copy:before {
  content: "\e735";
}

.zr-center-vetically-align:before {
  content: "\e736";
}

.zr-navigation:before {
  content: "\e737";
}

.zr-map:before {
  content: "\e738";
}

.zr-check:before {
  content: "\e739";
}

.zr-bottom-align:before {
  content: "\e73a";
}

.zr-module:before {
  content: "\e73b";
}

.zr-menu-transverse:before {
  content: "\e73c";
}

.zr-dashboard:before {
  content: "\e73d";
}

.zr-paste:before {
  content: "\e73e";
}

.zr-search-categories:before {
  content: "\e73f";
}

.zr-menu-vertical:before {
  content: "\e740";
}

.zr-region:before {
  content: "\e741";
}

.zr-country:before {
  content: "\e742";
}

.zr-workflow:before {
  content: "\e743";
}

.zr-left-align:before {
  content: "\e744";
}

.zr-fun:before {
  content: "\e745";
}

.zr-server:before {
  content: "\e746";
}

.zr-report:before {
  content: "\e747";
}

.zr-right-align:before {
  content: "\e748";
}

.zr-menu:before {
  content: "\e749";
}

.zr-ppt-file:before {
  content: "\e74a";
}

.zr-print:before {
  content: "\e74b";
}

.zr-excel-file:before {
  content: "\e74c";
}

.zr-cut:before {
  content: "\e74d";
}

.zr-wait:before {
  content: "\e74e";
}

.zr-target-b:before {
  content: "\e74f";
}

.zr-template:before {
  content: "\e750";
}

.zr-search:before {
  content: "\e751";
}

.zr-picture-file:before {
  content: "\e752";
}

.zr-zoom-out:before {
  content: "\e753";
}

.zr-unknown-file:before {
  content: "\e754";
}

.zr-descending-order:before {
  content: "\e755";
}

.zr-zoom-in:before {
  content: "\e756";
}

.zr-pdf-file:before {
  content: "\e757";
}

.zr-word-file:before {
  content: "\e758";
}

.zr-top-align:before {
  content: "\e759";
}

.zr-safety-equipment:before {
  content: "\e75a";
}

.zr-text:before {
  content: "\e6b4";
}

.zr-add-folder:before {
  content: "\e6b5";
}

.zr-copy-file:before {
  content: "\e6b6";
}

.zr-none-file:before {
  content: "\e6b7";
}

.zr-add-file:before {
  content: "\e6b8";
}

.zr-screen:before {
  content: "\e6b9";
}

.zr-google:before {
  content: "\e6ba";
}

.zr-ban:before {
  content: "\e6bb";
}

.zr-earphone:before {
  content: "\e6bc";
}

.zr-project-a:before {
  content: "\e6bd";
}

.zr-goal-a:before {
  content: "\e6be";
}

.zr-layout:before {
  content: "\e6bf";
}

.zr-internet:before {
  content: "\e6c0";
}

.zr-home-a:before {
  content: "\e6c1";
}

.zr-home-c:before {
  content: "\e6c2";
}

.zr-ie:before {
  content: "\e6c3";
}

.zr-home-b:before {
  content: "\e6c4";
}

.zr-voice-on:before {
  content: "\e6c5";
}

.zr-gift:before {
  content: "\e6c6";
}

.zr-goal-b:before {
  content: "\e6c7";
}

.zr-project-b:before {
  content: "\e6c8";
}

.zr-voice-off:before {
  content: "\e6c9";
}

.zr-circle-play-b:before {
  content: "\e694";
}

.zr-circle-stop:before {
  content: "\e695";
}

.zr-clear:before {
  content: "\e696";
}

.zr-comment:before {
  content: "\e697";
}

.zr-circle-pause:before {
  content: "\e698";
}

.zr-desktop:before {
  content: "\e699";
}

.zr-collection:before {
  content: "\e69a";
}

.zr-circle-play-a:before {
  content: "\e69b";
}

.zr-pause:before {
  content: "\e69c";
}

.zr-music:before {
  content: "\e69d";
}

.zr-delete:before {
  content: "\e69e";
}

.zr-edit-a:before {
  content: "\e69f";
}

.zr-phone:before {
  content: "\e6a0";
}

.zr-dislike:before {
  content: "\e6a1";
}

.zr-like:before {
  content: "\e6a2";
}

.zr-edit-d:before {
  content: "\e6a3";
}

.zr-set-a:before {
  content: "\e6a4";
}

.zr-notebook:before {
  content: "\e6a5";
}

.zr-movie:before {
  content: "\e6a6";
}

.zr-play:before {
  content: "\e6a7";
}

.zr-disconnect:before {
  content: "\e6a8";
}

.zr-edit-c:before {
  content: "\e6a9";
}

.zr-edit-b:before {
  content: "\e6aa";
}

.zr-stop:before {
  content: "\e6ab";
}

.zr-sound-recording:before {
  content: "\e6ac";
}

.zr-square-edit:before {
  content: "\e6ad";
}

.zr-set-c:before {
  content: "\e6ae";
}

.zr-telephone:before {
  content: "\e6af";
}

.zr-square-play:before {
  content: "\e6b0";
}

.zr-video:before {
  content: "\e6b1";
}

.zr-set-b:before {
  content: "\e6b2";
}

.zr-share:before {
  content: "\e6b3";
}

.zr-bluetooth:before {
  content: "\e680";
}

.zr-decline:before {
  content: "\e67c";
}

.zr-connect-wifi:before {
  content: "\e67d";
}

.zr-Battery:before {
  content: "\e67e";
}

.zr-charge:before {
  content: "\e67f";
}

.zr-money:before {
  content: "\e681";
}

.zr-rise:before {
  content: "\e682";
}

.zr-group:before {
  content: "\e683";
}

.zr-circle-head:before {
  content: "\e684";
}

.zr-circle-money:before {
  content: "\e685";
}

.zr-file:before {
  content: "\e686";
}

.zr-connect:before {
  content: "\e687";
}

.zr-user-management:before {
  content: "\e688";
}

.zr-link:before {
  content: "\e689";
}

.zr-protect:before {
  content: "\e68a";
}

.zr-square-head:before {
  content: "\e68b";
}

.zr-disconnect-wifi:before {
  content: "\e68c";
}

.zr-follow:before {
  content: "\e68d";
}

.zr-umbrella:before {
  content: "\e68e";
}

.zr-display:before {
  content: "\e68f";
}

.zr-notice:before {
  content: "\e690";
}

.zr-lable:before {
  content: "\e691";
}

.zr-moments:before {
  content: "\e692";
}

.zr-hide:before {
  content: "\e693";
}

.zr-circle-picture:before {
  content: "\e664";
}

.zr-add-user:before {
  content: "\e665";
}

.zr-effective-user:before {
  content: "\e666";
}

.zr-camera:before {
  content: "\e667";
}

.zr-backward:before {
  content: "\e669";
}

.zr-password:before {
  content: "\e66a";
}

.zr-unlock:before {
  content: "\e66b";
}

.zr-logout:before {
  content: "\e66c";
}

.zr-date:before {
  content: "\e66d";
}

.zr-transmit:before {
  content: "\e66e";
}

.zr-square-picture:before {
  content: "\e66f";
}

.zr-cloud-upload:before {
  content: "\e670";
}

.zr-upload:before {
  content: "\e671";
}

.zr-cloud-download:before {
  content: "\e672";
}

.zr-invalid-user:before {
  content: "\e673";
}

.zr-forward:before {
  content: "\e674";
}

.zr-download:before {
  content: "\e675";
}

.zr-square-login:before {
  content: "\e676";
}

.zr-lock:before {
  content: "\e677";
}

.zr-time:before {
  content: "\e678";
}

.zr-square-logout:before {
  content: "\e679";
}

.zr-delete-user:before {
  content: "\e67a";
}

.zr-circle-logout:before {
  content: "\e67b";
}

.zr-bottom:before {
  content: "\e64d";
}

.zr-back:before {
  content: "\e64e";
}

.zr-change:before {
  content: "\e64f";
}

.zr-cancel-withdrawal:before {
  content: "\e650";
}

.zr-doubt:before {
  content: "\e651";
}

.zr-enlarge:before {
  content: "\e652";
}

.zr-narrow:before {
  content: "\e653";
}

.zr-left-fold:before {
  content: "\e654";
}

.zr-full-screen:before {
  content: "\e655";
}

.zr-roll:before {
  content: "\e656";
}

.zr-circle-doubt:before {
  content: "\e657";
}

.zr-square-top:before {
  content: "\e658";
}

.zr-polygon-Warning:before {
  content: "\e659";
}

.zr-history:before {
  content: "\e65a";
}

.zr-refresh:before {
  content: "\e65b";
}

.zr-right-fold:before {
  content: "\e65c";
}

.zr-top:before {
  content: "\e65d";
}

.zr-vertical-center:before {
  content: "\e65e";
}

.zr-transform:before {
  content: "\e65f";
}

.zr-withdraw:before {
  content: "\e660";
}

.zr-preserve:before {
  content: "\e661";
}

.zr-square-bottom:before {
  content: "\e662";
}

.zr-triangle-Warning:before {
  content: "\e663";
}

.zr-circle:before {
  content: "\e635";
}

.zr-add:before {
  content: "\e636";
}

.zr-circle-add:before {
  content: "\e637";
}

.zr-circle-reduce:before {
  content: "\e638";
}

.zr-close:before {
  content: "\e639";
}

.zr-circle-seleted:before {
  content: "\e63a";
}

.zr-circle-close:before {
  content: "\e63b";
}

.zr-circle-Warning:before {
  content: "\e63c";
}

.zr-circle-information:before {
  content: "\e63d";
}

.zr-more-transverse:before {
  content: "\e63e";
}

.zr-code:before {
  content: "\e63f";
}

.zr-information:before {
  content: "\e640";
}

.zr-oblique-line:before {
  content: "\e641";
}

.zr-circle-Tick:before {
  content: "\e642";
}

.zr-reduce:before {
  content: "\e643";
}

.zr-more-vertical:before {
  content: "\e644";
}

.zr-square:before {
  content: "\e645";
}

.zr-Warning:before {
  content: "\e646";
}

.zr-square-add:before {
  content: "\e647";
}

.zr-seleted:before {
  content: "\e648";
}

.zr-square-reduce:before {
  content: "\e649";
}

.zr-square-close:before {
  content: "\e64a";
}

.zr-square-seleted:before {
  content: "\e64b";
}

.zr-square-tick:before {
  content: "\e64c";
}

.zr-arrow-down:before {
  content: "\e61d";
}

.zr-arrow-left-1:before {
  content: "\e61e";
}

.zr-circle-up:before {
  content: "\e61f";
}

.zr-circle-left:before {
  content: "\e620";
}

.zr-double-left:before {
  content: "\e621";
}

.zr-arrow-up:before {
  content: "\e622";
}

.zr-circle-right:before {
  content: "\e623";
}

.zr-arrow-left:before {
  content: "\e624";
}

.zr-double-down:before {
  content: "\e625";
}

.zr-circle-down:before {
  content: "\e626";
}

.zr-left:before {
  content: "\e627";
}

.zr-one-way-arrow-right:before {
  content: "\e628";
}

.zr-next:before {
  content: "\e629";
}

.zr-square-right:before {
  content: "\e62a";
}

.zr-square-down:before {
  content: "\e62b";
}

.zr-double-up:before {
  content: "\e62c";
}

.zr-square-up:before {
  content: "\e62d";
}

.zr-left-1:before {
  content: "\e62e";
}

.zr-double-right:before {
  content: "\e62f";
}

.zr-one-way-arrow-left:before {
  content: "\e630";
}

.zr-up:before {
  content: "\e631";
}

.zr-down:before {
  content: "\e632";
}

.zr-square-left:before {
  content: "\e633";
}

.zr-Previous:before {
  content: "\e634";
}

