<template>
  <!-- 子应用的根节点，classname可以自行修改 -->
  <div
    id="app"
    :class="`subapp_template subapp_template_${pageType}`"
  >
    <router-view />
  </div>
</template>
<script setup>
import { useRouter } from 'vue-router';
import { onBeforeUnmount, onMounted, provide } from 'vue';
import { getMainSearchUrl } from '@/utils';
import * as echarts from 'echarts';
import { useStore } from '@/store/store';
import { storeToRefs } from 'pinia';
const router = useRouter();
provide('ec', echarts);
onMounted(() => {
  if (window.__POWERED_BY_QIANKUN__) {
    ce.emit('appAfterMount');
    // 监听主应用的退出事件
    ce.on('afterLogout', window.$parentStore.state.activeApp.client_id, () => {
      console.log('退出登录');
      const searchUrl = getMainSearchUrl();
      if (searchUrl) {
        window.location.replace(searchUrl, '_blank');
      } else {
        ElMessage.error('系统错误!');
      }
      return true;
    });
  }
  const stores = useStore();
  const { routeBool, routerMenuList } = storeToRefs(stores);
  routeBool.value = false;
  routerMenuList.value = [];
});
onBeforeUnmount(() => {
  ce.off('logout', window.$parentStore.state.activeApp.client_id);
});
// 判断当前页面类型
const pageType = computed(() => {
  return router.currentRoute.value.fullPath !== '/search' ? 'page' : 'home';
});
</script>

<style lang="scss">
.subapp_template {
  &.subapp_template_home {
    background: url('./assets/imgs/homeBg.png') no-repeat center/cover;
  }
}
</style>
